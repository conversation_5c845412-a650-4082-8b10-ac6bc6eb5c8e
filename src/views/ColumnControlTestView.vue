<template>
  <div class="column-control-test-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">列控制测试页面</h1>
        <p class="page-description">测试和演示表格列显示控制功能</p>
      </div>
      <div class="header-right">
        <el-button @click="exportData" type="default">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true" type="default">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card" shadow="never">
            <div class="stats-content">
              <div class="stats-number">{{ testData.length }}</div>
              <div class="stats-label">总记录数</div>
            </div>
            <el-icon class="stats-icon"><DataBoard /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="never">
            <div class="stats-content">
              <div class="stats-number">{{ visibleColumns.length }}</div>
              <div class="stats-label">可见列数</div>
            </div>
            <el-icon class="stats-icon"><View /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="never">
            <div class="stats-content">
              <div class="stats-number">
                {{ tableColumns.filter((col) => !col.visible).length }}
              </div>
              <div class="stats-label">隐藏列数</div>
            </div>
            <el-icon class="stats-icon"><Hide /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="never">
            <div class="stats-content">
              <div class="stats-number">{{ pagination.pageSize }}</div>
              <div class="stats-label">页面大小</div>
            </div>
            <el-icon class="stats-icon"><Grid /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <el-icon><List /></el-icon>
            <span>数据表格</span>
          </div>
          <div class="table-actions">
            <el-tooltip content="列设置" placement="top">
              <el-button @click="showColumnSettings = true" size="small" circle :icon="Setting" />
            </el-tooltip>
            <el-tooltip content="刷新数据" placement="top">
              <el-button @click="refreshData" size="small" circle :icon="Refresh" />
            </el-tooltip>
          </div>
        </div>
      </template>

      <el-table
        :data="paginatedData"
        v-loading="loading"
        stripe
        fit
        style="width: 100%"
        :header-cell-style="{ background: '#f8fafc', color: '#374151' }"
        @selection-change="handleSelectionChange"
      >
        <!-- 动态生成表格列 -->
        <template v-for="column in visibleColumns" :key="column.key">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.key === 'selection'"
            type="selection"
            :width="column.width"
            :min-width="column.minWidth"
          />

          <!-- ID 列 -->
          <el-table-column
            v-else-if="column.key === 'id'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            sortable
          >
            <template #default="{ row }">
              <el-tag size="small" type="info">#{{ row.id }}</el-tag>
            </template>
          </el-table-column>

          <!-- 姓名列 -->
          <el-table-column
            v-else-if="column.key === 'name'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            sortable
          >
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="32" class="user-avatar">
                  {{ row.name.charAt(0) }}
                </el-avatar>
                <div class="user-details">
                  <div class="user-name">{{ row.name }}</div>
                  <div class="user-subtitle">{{ row.department }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column
            v-else-if="column.key === 'status'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            sortable
          >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 等级列 -->
          <el-table-column
            v-else-if="column.key === 'level'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            sortable
          >
            <template #default="{ row }">
              <el-rate
                v-model="row.level"
                disabled
                show-score
                score-template="{value}/5"
                size="small"
              />
            </template>
          </el-table-column>

          <!-- 进度列 -->
          <el-table-column
            v-else-if="column.key === 'progress'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            sortable
          >
            <template #default="{ row }">
              <div class="progress-container">
                <el-progress
                  :percentage="row.progress"
                  :color="getProgressColor(row.progress)"
                  :stroke-width="6"
                  size="small"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 标签列 -->
          <el-table-column
            v-else-if="column.key === 'tags'"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :show-overflow-tooltip="false"
          >
            <template #default="{ row }">
              <div class="tags-container">
                <el-tag
                  v-for="(tag, index) in row.tags.slice(0, 2)"
                  :key="`${row.id}-${index}-${tag}`"
                  size="small"
                  class="tag-item"
                  :type="getTagType(tag)"
                >
                  {{ tag }}
                </el-tag>
                <el-tag v-if="row.tags.length > 2" size="small" class="more-tags" type="info">
                  +{{ row.tags.length - 2 }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 创建时间列 -->
          <el-table-column
            v-else-if="column.key === 'created_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            sortable
          >
            <template #default="{ row }">
              <div class="date-info">
                <div class="date-primary">{{ formatDate(row.created_at) }}</div>
                <div class="date-secondary">{{ formatTime(row.created_at) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-else-if="column.key === 'actions'"
            :fixed="column.fixed"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleView(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button link type="warning" size="small" @click="handleEdit(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button link type="danger" size="small" @click="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>

          <!-- 其他普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :show-overflow-tooltip="column.showTooltip"
            :sortable="column.sortable"
          />
        </template>

        <!-- 空状态 -->
        <template #empty>
          <div class="empty-state">
            <el-icon size="64" color="#909399"><DataBoard /></el-icon>
            <p class="empty-text">暂无数据</p>
            <el-button type="primary" @click="refreshData">重新加载</el-button>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">
            共 {{ testData.length }} 条记录，已选择 {{ selectedRows.length }} 条
          </span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="testData.length"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog
      v-model="showColumnSettings"
      title="列设置"
      width="520px"
      class="column-settings-dialog"
      :before-close="handleCloseColumnSettings"
    >
      <div class="column-settings">
        <div class="setting-header">
          <div class="setting-info">
            <el-icon><Setting /></el-icon>
            <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          </div>
          <el-button size="small" @click="resetColumns">
            <el-icon><RefreshRight /></el-icon>
            重置默认
          </el-button>
        </div>

        <div class="settings-stats">
          <div class="stat-item">
            <span class="stat-label">总列数：</span>
            <span class="stat-value">{{ tableColumns.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">可见列：</span>
            <span class="stat-value visible">{{ visibleColumns.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">隐藏列：</span>
            <span class="stat-value hidden">{{
              tableColumns.filter((col) => !col.visible).length
            }}</span>
          </div>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :disabled="false"
          ghost-class="ghost-item"
          chosen-class="chosen-item"
          drag-class="drag-item"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'name',
                visible: element.visible,
                hidden: !element.visible,
              }"
            >
              <div class="column-left">
                <el-icon
                  class="drag-handle"
                  :class="{
                    disabled:
                      element.key === 'selection' ||
                      element.key === 'actions' ||
                      element.key === 'name',
                  }"
                >
                  <Rank />
                </el-icon>
                <el-checkbox
                  v-model="element.visible"
                  @change="saveColumnSettings"
                  :disabled="
                    element.key === 'selection' ||
                    element.key === 'actions' ||
                    element.key === 'name'
                  "
                  size="small"
                />
              </div>

              <div class="column-content">
                <span class="column-label">{{ element.label }}</span>
                <span
                  v-if="
                    element.key === 'selection' ||
                    element.key === 'name' ||
                    element.key === 'actions'
                  "
                  class="fixed-label"
                >
                  (固定)
                </span>
              </div>

              <div class="column-right">
                <el-tag
                  size="small"
                  :type="element.visible ? 'success' : 'info'"
                  effect="plain"
                  class="status-tag"
                >
                  {{ element.visible ? '显示' : '隐藏' }}
                </el-tag>
              </div>
            </div>
          </template>
        </draggable>

        <div class="settings-footer">
          <div class="footer-tips">
            <el-icon><InfoFilled /></el-icon>
            <span>选择框、姓名和操作列为必须显示的固定列</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showColumnSettings = false">取消</el-button>
          <el-button type="primary" @click="applyColumnSettings">
            <el-icon><Check /></el-icon>
            应用设置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Download,
  Refresh,
  DataBoard,
  View,
  Hide,
  Grid,
  List,
  Edit,
  Delete,
  Rank,
  RefreshRight,
  InfoFilled,
  Check,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

// 类型定义
interface TestDataItem {
  id: number
  name: string
  email: string
  department: string
  position: string
  status: 'active' | 'inactive' | 'pending'
  level: number
  progress: number
  tags: string[]
  created_at: string
  updated_at: string
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  minWidth?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
  sortable?: boolean
}

interface Pagination {
  currentPage: number
  pageSize: number
}

// 响应式数据
const loading = ref(false)
const showColumnSettings = ref(false)
const selectedRows = ref<TestDataItem[]>([])

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
})

// 默认列配置
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true },
  { key: 'id', prop: 'id', label: 'ID', width: 80, visible: true, sortable: true },
  {
    key: 'name',
    prop: 'name',
    label: '姓名',
    minWidth: 180,
    visible: true,
    fixed: true,
    sortable: true,
  },
  {
    key: 'email',
    prop: 'email',
    label: '邮箱',
    minWidth: 200,
    visible: true,
    showTooltip: true,
    sortable: true,
  },
  {
    key: 'department',
    prop: 'department',
    label: '部门',
    minWidth: 120,
    visible: true,
    sortable: true,
  },
  {
    key: 'position',
    prop: 'position',
    label: '职位',
    minWidth: 150,
    visible: true,
    sortable: true,
  },
  { key: 'status', prop: 'status', label: '状态', width: 100, visible: true, sortable: true },
  { key: 'level', prop: 'level', label: '等级', width: 120, visible: true, sortable: true },
  {
    key: 'progress',
    prop: 'progress',
    label: '进度',
    minWidth: 150,
    visible: true,
    sortable: true,
  },
  { key: 'tags', label: '标签', minWidth: 200, visible: true },
  {
    key: 'created_at',
    prop: 'created_at',
    label: '创建时间',
    width: 160,
    visible: true,
    sortable: true,
  },
  {
    key: 'updated_at',
    prop: 'updated_at',
    label: '更新时间',
    width: 160,
    visible: false,
    sortable: true,
  },
  { key: 'actions', label: '操作', width: 180, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// 计算可见的列
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => column.visible)
})

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return testData.value.slice(start, end)
})

// 本地存储键名
const COLUMN_SETTINGS_KEY = 'column-control-test-settings'

// 模拟测试数据
const testData = ref<TestDataItem[]>([])

// 生成模拟数据
const generateTestData = (): TestDataItem[] => {
  const departments = ['技术部', '产品部', '设计部', '运营部', '市场部', '销售部']
  const positions = ['经理', '专员', '主管', '总监', '助理', '实习生']
  const statuses: ('active' | 'inactive' | 'pending')[] = ['active', 'inactive', 'pending']
  const tagPools = [
    '前端',
    '后端',
    '全栈',
    'UI',
    'UX',
    'Python',
    'JavaScript',
    'Vue',
    'React',
    'Node.js',
  ]

  const data: TestDataItem[] = []

  for (let i = 1; i <= 150; i++) {
    const name = `用户${i.toString().padStart(3, '0')}`
    const dept = departments[Math.floor(Math.random() * departments.length)]
    const pos = positions[Math.floor(Math.random() * positions.length)]

    // 生成随机标签
    const numTags = Math.floor(Math.random() * 4) + 1
    const tags = []
    const usedTags = new Set()

    while (tags.length < numTags) {
      const tag = tagPools[Math.floor(Math.random() * tagPools.length)]
      if (!usedTags.has(tag)) {
        tags.push(tag)
        usedTags.add(tag)
      }
    }

    data.push({
      id: i,
      name,
      email: `user${i}@company.com`,
      department: dept,
      position: pos,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      level: Math.floor(Math.random() * 5) + 1,
      progress: Math.floor(Math.random() * 101),
      tags,
      created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    })
  }

  return data
}

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟 API 调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))
    testData.value = generateTestData()
    ElMessage.success('数据刷新成功')
  } catch {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSelectionChange = (selection: TestDataItem[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleView = (row: TestDataItem) => {
  ElMessage.info(`查看用户：${row.name}`)
}

const handleEdit = (row: TestDataItem) => {
  ElMessage.info(`编辑用户：${row.name}`)
}

const handleDelete = async (row: TestDataItem) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${row.name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 从数据中移除
    const index = testData.value.findIndex((item) => item.id === row.id)
    if (index > -1) {
      testData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
  ElMessage.success('已重置为默认列设置')
}

const handleCloseColumnSettings = () => {
  showColumnSettings.value = false
}

const applyColumnSettings = () => {
  saveColumnSettings()
  showColumnSettings.value = false
  ElMessage.success('列设置已应用')
}

// 工具方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'danger',
    pending: 'warning',
  }
  return types[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活跃',
    inactive: '停用',
    pending: '待审核',
  }
  return labels[status] || status
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  if (progress >= 20) return '#f56c6c'
  return '#909399'
}

const getTagType = (tag: string) => {
  const types = ['primary', 'success', 'info', 'warning']
  const index = tag.length % types.length
  return types[index]
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 生命周期
onMounted(() => {
  // 加载保存的列设置
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    try {
      tableColumns.value = JSON.parse(savedColumns)
    } catch {
      // 如果解析失败，使用默认设置
      tableColumns.value = [...defaultColumns]
    }
  }

  // 初始化数据
  refreshData()
})
</script>

<style scoped>
.column-control-test-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 统计信息样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stats-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.stats-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
  font-weight: 500;
}

.stats-icon {
  font-size: 48px;
  color: #e5e7eb;
  opacity: 0.6;
}

/* 表格卡片样式 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-card :deep(.el-table) {
  table-layout: auto;
  width: 100% !important;
}

.table-card :deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

.table-card :deep(.el-table .cell) {
  word-break: break-word;
}

.table-card :deep(.el-card__header) {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  border-bottom: 1px solid #e5e7eb;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.table-title .el-icon {
  color: #667eea;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表格内容样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.user-subtitle {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.progress-container {
  padding: 0 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tag-item {
  font-size: 12px;
}

.more-tags {
  font-size: 12px;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date-primary {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.date-secondary {
  font-size: 12px;
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 空状态样式 */
.empty-state {
  padding: 40px;
  text-align: center;
}

.empty-text {
  margin: 16px 0 24px 0;
  color: #6b7280;
  font-size: 16px;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* 列设置弹窗样式 */
.column-settings-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.column-settings-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
}

.column-settings-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.column-settings-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.column-settings-dialog :deep(.el-dialog__body) {
  padding: 24px 32px;
}

.column-settings {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.setting-info .el-icon {
  color: #667eea;
}

.settings-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 13px;
  color: #6b7280;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
}

.stat-value.visible {
  color: #16a34a;
}

.stat-value.hidden {
  color: #dc2626;
}

.column-list {
  min-height: 200px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 12px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: move;
}

.column-item:last-child {
  margin-bottom: 0;
}

.column-item:hover {
  background: #f0f4ff;
  border-color: #c7d2fe;
  transform: translateY(-1px);
}

.column-item.visible {
  border-left: 4px solid #16a34a;
}

.column-item.hidden {
  border-left: 4px solid #dc2626;
  opacity: 0.7;
}

.column-item.fixed-column {
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border: 1px solid #c7d2fe;
}

.column-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drag-handle {
  cursor: grab;
  color: #9ca3af;
  font-size: 16px;
  transition: color 0.3s ease;
}

.drag-handle:hover {
  color: #667eea;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #d1d5db;
}

.column-item:active .drag-handle {
  cursor: grabbing;
}

.column-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.column-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.fixed-label {
  font-size: 11px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.column-right {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 11px;
  font-weight: 500;
}

.settings-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.footer-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.footer-tips .el-icon {
  color: #3b82f6;
}

/* 拖拽状态样式 */
.ghost-item {
  opacity: 0.5;
  background: #e5e7eb !important;
}

.chosen-item {
  transform: rotate(2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.drag-item {
  transform: rotate(5deg);
  opacity: 0.8;
}

/* 弹窗底部样式 */
.dialog-footer {
  padding: 16px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .column-control-test-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }

  .header-right {
    justify-content: center;
  }

  .stats-section :deep(.el-col) {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-actions {
    justify-content: center;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: center;
  }

  .column-settings-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .column-settings-dialog :deep(.el-dialog__header) {
    padding: 20px 24px;
  }

  .column-settings-dialog :deep(.el-dialog__body) {
    padding: 20px 24px;
  }

  .settings-stats {
    flex-direction: column;
    gap: 8px;
  }

  .stat-item {
    justify-content: space-between;
  }

  .column-item {
    padding: 10px 12px;
  }

  .column-left {
    gap: 8px;
  }

  .column-content {
    margin-left: 8px;
  }

  .dialog-footer {
    padding: 16px 24px;
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}

/* 深色主题支持（可选） */
@media (prefers-color-scheme: dark) {
  .column-control-test-container {
    background: #1f2937;
  }

  .page-header,
  .stats-card,
  .table-card {
    background: #374151;
    color: #f9fafb;
  }

  .page-title {
    color: #f9fafb;
  }

  .page-description,
  .stats-label,
  .total-info {
    color: #d1d5db;
  }
}
</style>
