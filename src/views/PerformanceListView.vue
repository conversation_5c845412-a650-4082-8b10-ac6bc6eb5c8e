<template>
  <div class="kol-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">数据追踪</h1>
        <p class="page-description">管理和查看已合作的作品数据追踪</p>
      </div>
      <div class="header-right">
        <el-button @click="exportSearchResults">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
        <el-button type="primary" @click="showAddPaymentDialog = true">
          <el-icon><Plus /></el-icon>
          添加支付记录
        </el-button>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <div class="search-title">
          <el-icon><Search /></el-icon>
          <span>智能搜索</span>
        </div>
      </div>

      <!-- 快速搜索栏 -->
      <div class="quick-search-bar">
        <div class="quick-search-container">
          <!-- KOL ID搜索 -->
          <el-input
            v-model="searchForm.quickSearchSocialId"
            placeholder="KOL ID"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>

          <!-- 帖子链接搜索 -->
          <el-input
            v-model="searchForm.quickSearchPostLink"
            placeholder="帖子链接"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>

          <!-- 平台搜索 -->
          <el-select
            v-model="searchForm.quickSearchPlatform"
            placeholder="平台"
            clearable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><Monitor /></el-icon>
            </template>
            <el-option label="TikTok" value="TIKTOK" />
            <el-option label="Instagram" value="INSTAGRAM" />
            <el-option label="YouTube" value="YOUTUBE" />
          </el-select>

          <!-- 项目编码搜索 -->
          <el-select
            v-model="searchForm.quickSearchProject"
            placeholder="项目编码"
            clearable
            filterable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><CollectionTag /></el-icon>
            </template>
            <el-option
              v-for="project in projectList"
              :key="project.code"
              :label="project.code"
              :value="project.code"
            >
              {{ project.code }}
            </el-option>
          </el-select>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            size="large"
            class="search-btn"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 搜索历史和模板 -->
      <div class="search-templates" v-if="searchTemplates.length > 0">
        <div class="templates-label">快速搜索模板：</div>
        <div class="templates-list">
          <el-tag
            v-for="template in searchTemplates"
            :key="template.id"
            class="template-tag"
            type="info"
            effect="plain"
            @click="applySearchTemplate(template)"
            closable
            @close="removeSearchTemplate(template.id)"
          >
            {{ template.name }}
          </el-tag>
        </div>
      </div>

      <!-- 当前搜索条件显示 -->
      <div class="current-filters" v-if="hasActiveFilters">
        <div class="filters-label">当前筛选条件：</div>
        <div class="filters-list">
          <el-tag
            v-for="filter in activeFilters"
            :key="filter.key"
            closable
            @close="removeFilter(filter.key)"
            class="filter-tag"
          >
            {{ filter.label }}: {{ filter.value }}
          </el-tag>
          <el-button link @click="clearAllFilters" class="clear-all">
            <el-icon><Delete /></el-icon>
            清空所有
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="kolList" v-loading="loading" stripe style="width: 100%">
        <!-- 动态生成表格列 -->
        <template v-for="column in visibleColumns" :key="column.key">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.key === 'selection'"
            type="selection"
            :width="column.width"
            :fixed="column.fixed"
          />

          <!-- CPM列 -->
          <el-table-column
            v-else-if="column.key === 'cpm'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :fixed="column.fixed"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.cpm" class="cpm-value"> ${{ row.cpm }} </span>
              <span v-else class="no-cpm">-</span>
            </template>
          </el-table-column>

          <!-- Engagement Rate列 -->
          <el-table-column
            v-else-if="column.key === 'engagement_rate'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.engagement_rate" :class="getEngagementClass(row.engagement_rate)">
                {{ row.engagement_rate }}%
              </span>
              <span v-else class="no-engagement">-</span>
            </template>
          </el-table-column>

          <!-- Views Total列 -->
          <el-table-column
            v-else-if="column.key === 'views_total'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatNumber(row.views_total) }}
            </template>
          </el-table-column>

          <!-- Likes Total列 -->
          <el-table-column
            v-else-if="column.key === 'likes_total'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatNumber(row.likes_total) }}
            </template>
          </el-table-column>

          <!-- Comments Total列 -->
          <el-table-column
            v-else-if="column.key === 'comments_total'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatNumber(row.comments_total) }}
            </template>
          </el-table-column>

          <!-- Shares Total列 -->
          <el-table-column
            v-else-if="column.key === 'shares_total'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatNumber(row.shares_total) }}
            </template>
          </el-table-column>

          <!-- Payment Amount列 -->
          <el-table-column
            v-else-if="column.key === 'payment_amount'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.payment_amount" class="payment-amount">
                ${{ row.payment_amount }}
              </span>
              <span v-else class="no-payment">未支付</span>
            </template>
          </el-table-column>

          <!-- Updated At列 -->
          <el-table-column
            v-else-if="column.key === 'updated_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <!-- Created At列 -->
          <el-table-column
            v-else-if="column.key === 'created_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>

          <!-- Post Link列 -->
          <el-table-column
            v-else-if="column.key === 'post_link'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tooltip
                :content="row.post_link"
                placement="top"
                :show-after="500"
                :hide-after="0"
              >
                <span class="post-link-text">{{ row.post_link }}</span>
              </el-tooltip>
            </template>
          </el-table-column>

          <!-- Post Date列 -->
          <el-table-column
            v-else-if="column.key === 'post_date'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatDate(row.post_date) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-else-if="column.key === 'actions'"
            :fixed="column.fixed"
            :label="column.label"
            :width="column.width"
          >
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="handleView(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </template>
          </el-table-column>

          <!-- Platform列 -->
          <el-table-column
            v-else-if="column.key === 'platform'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :fixed="column.fixed"
          >
            <template #default="{ row }">
              <div class="platform-badge">
                <el-icon v-if="row.platform === 'TIKTOK'" color="#ff0050"><VideoPlay /></el-icon>
                <el-icon v-else-if="row.platform === 'INSTAGRAM'" color="#e4405f"
                  ><Picture
                /></el-icon>
                <el-icon v-else-if="row.platform === 'YOUTUBE'" color="#ff0000"
                  ><Monitor
                /></el-icon>
                <span>{{ getPlatformLabel(row.platform) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 其他普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :show-overflow-tooltip="column.showTooltip"
            :fixed="column.fixed"
          />
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[100, 200, 500, 1000]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="500px">
      <div class="column-settings">
        <div class="setting-header">
          <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          <el-button size="small" @click="resetColumns">重置默认</el-button>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :move="checkMoveAllowed"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions',
              }"
            >
              <el-icon
                class="drag-handle"
                :class="{
                  disabled:
                    element.key === 'selection' ||
                    element.key === 'social_id' ||
                    element.key === 'actions',
                }"
              >
                <Rank />
              </el-icon>
              <el-checkbox
                v-model="element.visible"
                @change="saveColumnSettings"
                :disabled="
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'social_id'
                "
              >
                {{ element.label }}
              </el-checkbox>
              <span
                v-if="
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions'
                "
                class="fixed-label"
              >
                (固定)
              </span>
            </div>
          </template>
        </draggable>
      </div>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      class="export-dialog"
      append-to-body
    >
      <div class="export-content">
        <el-form :model="exportConfig" label-width="120px" class="export-form">
          <!-- 导出范围 -->
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio value="filtered">当前筛选结果</el-radio>
              <el-radio value="current">当前页面数据</el-radio>
              <el-radio value="all">全部数据</el-radio>
            </el-radio-group>
            <div class="form-hint">
              <span v-if="exportConfig.scope === 'filtered'"
                >将导出当前筛选条件下的所有 {{ kolList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'current'"
                >将导出当前页面的 {{ kolList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'all'"
                >将导出全部 {{ pagination.total }} 条数据</span
              >
              <span v-else>将导出选中的数据（功能开发中）</span>
            </div>
          </el-form-item>

          <!-- 导出格式 -->
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportConfig.format">
              <el-radio value="xlsx">Excel 文件 (.xlsx)</el-radio>
              <el-radio value="csv">CSV 文件 (.csv)</el-radio>
              <el-radio value="json">JSON 文件 (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件名设置 -->
          <el-form-item label="文件名">
            <el-input
              v-model="exportConfig.filename"
              placeholder="请输入文件名"
              maxlength="50"
              show-word-limit
            >
              <template #suffix>
                <span class="file-extension">.{{ exportConfig.format }}</span>
              </template>
            </el-input>
          </el-form-item>

          <!-- 其他选项 -->
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportConfig.includeHeaders">包含表头</el-checkbox>
          </el-form-item>

          <!-- 选择字段 -->
          <el-form-item label="导出字段" class="field-selection">
            <div class="field-selection-header">
              <span class="selection-info"
                >已选择 {{ exportConfig.fields.length }} / {{ exportableFields.length }} 项</span
              >
              <div class="selection-buttons">
                <el-button type="text" size="small" @click="selectAllFields">全选</el-button>
                <el-button type="text" size="small" @click="clearAllFields">全不选</el-button>
              </div>
            </div>
            <div class="field-list">
              <el-checkbox-group v-model="exportConfig.fields">
                <div class="field-grid">
                  <el-checkbox
                    v-for="field in exportableFields"
                    :key="field.key"
                    :value="field.key"
                    class="field-item"
                  >
                    {{ field.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :disabled="exportConfig.fields.length === 0"
          >
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加支付记录弹窗 -->
    <el-dialog
      v-model="showAddPaymentDialog"
      title="添加支付记录"
      width="1000px"
      class="add-payment-dialog"
      :before-close="handleAddPaymentClose"
      append-to-body
    >
      <div class="payment-form-container" v-loading="paymentLoading">
        <el-form
          ref="paymentFormRef"
          :model="{ ...performanceForm, ...paymentForm }"
          :rules="{ ...performanceRules, ...paymentRules }"
          label-width="120px"
          class="payment-form"
        >
          <!-- 支付信息部分 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><CreditCard /></el-icon>
                支付信息
              </h4>
            </div>
            <div class="section-content">
              <!-- 2x2布局：支付金额、PayPal账户、跟进人、资金来源 -->
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="支付金额" prop="payment_amount">
                    <el-input-number
                      v-model="paymentForm.payment_amount"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                      placeholder="请输入支付金额"
                      size="large"
                      :controls="false"
                    >
                      <template #prefix>$</template>
                    </el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="PayPal账户" prop="paypal_accounts">
                    <el-input
                      v-model="paymentForm.paypal_accounts"
                      placeholder="请输入PayPal账户"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="跟进人" prop="tracker">
                    <el-input
                      v-model="paymentForm.tracker"
                      placeholder="请输入跟进人"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资金来源" prop="fund_source">
                    <el-input
                      v-model="paymentForm.fund_source"
                      placeholder="请输入资金来源"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 支付日期行 -->
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="支付日期" prop="payout_date">
                    <el-date-picker
                      v-model="paymentForm.payout_date"
                      type="date"
                      placeholder="请选择支付日期"
                      style="width: 100%"
                      size="large"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!-- 预留空间或其他字段 -->
                </el-col>
              </el-row>

              <!-- 支付截图单独一行 -->
              <el-form-item label="支付截图" prop="payment_screenshot_filename">
                <el-upload
                  class="payment-upload"
                  drag
                  :auto-upload="false"
                  :show-file-list="true"
                  accept="image/*"
                  :limit="1"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                >
                  <el-icon class="el-icon--upload"><Upload /></el-icon>
                  <div class="el-upload__text">将支付截图拖到此处，或<em>点击上传</em></div>
                  <template #tip>
                    <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
                  </template>
                </el-upload>
              </el-form-item>

              <el-form-item label="备注信息" prop="note">
                <el-input
                  v-model="paymentForm.note"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入备注信息（可选）"
                  maxlength="1000"
                  show-word-limit
                  resize="none"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 绩效信息部分 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><DataAnalysis /></el-icon>
                绩效信息
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="平台" prop="platform">
                    <el-select
                      v-model="performanceForm.platform"
                      placeholder="请选择平台"
                      style="width: 100%"
                      size="large"
                    >
                      <el-option label="TikTok" value="TIKTOK" />
                      <el-option label="Instagram" value="INSTAGRAM" />
                      <el-option label="YouTube" value="YOUTUBE" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="社交媒体ID" prop="social_id">
                    <el-input
                      v-model="performanceForm.social_id"
                      placeholder="请输入社交媒体ID"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="项目编码" prop="project_code">
                    <el-select
                      v-model="performanceForm.project_code"
                      placeholder="请选择项目编码"
                      style="width: 100%"
                      size="large"
                      filterable
                    >
                      <el-option
                        v-for="project in projectList"
                        :key="project.code"
                        :label="project.code"
                        :value="project.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="帖子链接" prop="post_link">
                    <el-input
                      v-model="performanceForm.post_link"
                      placeholder="请输入帖子链接"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="resetAllForms" :disabled="paymentLoading">
              <el-icon><RefreshLeft /></el-icon>
              重置表单
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="showAddPaymentDialog = false" :disabled="paymentLoading">
              取消
            </el-button>
            <el-button
              type="primary"
              @click="handleCreatePaymentAndPerformance"
              :loading="paymentLoading"
            >
              <el-icon><Plus /></el-icon>
              创建记录
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- KOL 详情抽屉 -->
    <PerformanceDetailDrawer
      v-model="showDetailDrawer"
      :performance-data="selectedPerformanceData"
      @edit="handleEditFromDrawer"
      @save="handleSaveFromDrawer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  Search,
  Setting,
  Rank,
  View,
  Delete,
  RefreshLeft,
  Download,
  DataAnalysis,
  Plus,
  Upload,
  CreditCard,
  User,
  Link,
  Monitor,
  VideoPlay,
  Picture,
  CollectionTag,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  createPerformancePayment,
  fetchKolPerformanceData,
  fetchPerformanceById,
  searchKolPerformanceData,
  type Performance,
  type PerformancePaymentRequest,
  type KolPerformanceItem,
  type KolPerformanceParams,
  type KolPerformanceSearchParams,
} from '../services/performanceApi'
import { type Payment, fetchPaymentById } from '../services/paymentApi'
import { fetchAllProjects } from '../services/projectApi'

// 类型定义
// 使用KolPerformanceItem作为KolItem的类型
type KolItem = KolPerformanceItem

// 绩效详情抽屉的数据类型（与PerformanceDetailDrawer组件中的PerformanceItem保持一致）
interface PerformanceDetailItem {
  id: number
  platform: 'tiktok' | 'instagram' | 'youtube'
  kol_id: string
  project_code?: string
  cpm?: number | null
  post_link: string
  post_date?: string | null
  views_total?: number | null
  likes_total?: number | null
  comments_total?: number | null
  shares_total?: number | null
  views_day1?: number | null
  likes_day1?: number | null
  comments_day1?: number | null
  shares_day1?: number | null
  views_day3?: number | null
  likes_day3?: number | null
  comments_day3?: number | null
  shares_day3?: number | null
  views_day7?: number | null
  likes_day7?: number | null
  comments_day7?: number | null
  shares_day7?: number | null

  created_at: string
  updated_at: string
  // 新增支付信息
  payment?: Payment | null
}

interface SearchForm {
  // 快速搜索字段
  quickSearchSocialId: string
  quickSearchPostLink: string
  quickSearchPlatform: string
  quickSearchProject: string
}

interface SearchTemplate {
  id: string
  name: string
  conditions: Partial<SearchForm>
  createdAt: string
}

interface ActiveFilter {
  key: string
  label: string
  value: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
  sortable?: boolean
}

interface ExportConfig {
  fields: string[]
  format: 'xlsx' | 'csv' | 'json'
  scope: 'all' | 'current' | 'filtered' | 'selected'
  includeHeaders: boolean
  filename: string
}

interface ExportItem {
  [key: string]: string | number
}

interface PaymentForm {
  performance_id: number | null
  payment_amount: number | null
  paypal_accounts: string
  tracker: string
  payout_date: string | null
  fund_source: string
  payment_screenshot_filename: string
  payment_screenshot_file: File | null
  note: string
}

interface PerformanceForm {
  platform: string
  social_id: string
  project_code: string
  post_link: string
  post_date: string | null
}

// 响应式数据
const loading = ref(false)
const kolList = ref<KolItem[]>([])
const showDetailDrawer = ref(false)
const selectedKol = ref<KolItem | PerformanceDetailItem | null>(null)
const showColumnSettings = ref(false)

// 搜索相关数据
const showExportDialog = ref(false)
const searchTemplates = ref<SearchTemplate[]>([])

// 添加支付记录相关数据
const showAddPaymentDialog = ref(false)
const paymentLoading = ref(false)
const paymentFormRef = ref<FormInstance>()

const currentStep = ref(1)
const createdPerformance = ref<Performance | null>(null)
const createdPayment = ref<Payment | null>(null)
const projectList = ref<{ code: string; name: string }[]>([])

// 导出相关数据
const exportConfig = reactive<ExportConfig>({
  fields: [],
  format: 'xlsx',
  scope: 'filtered',
  includeHeaders: true,
  filename: '',
})

// 支付表单数据
const paymentForm = reactive<PaymentForm>({
  performance_id: null,
  payment_amount: null,
  paypal_accounts: '',
  tracker: '',
  payout_date: null,
  fund_source: '',
  payment_screenshot_filename: '',
  payment_screenshot_file: null,
  note: '',
})

// 绩效表单数据
const performanceForm = reactive<PerformanceForm>({
  platform: '',
  social_id: '',
  project_code: '',
  post_link: '',
  post_date: null,
})

const searchForm = reactive<SearchForm>({
  quickSearchSocialId: '',
  quickSearchPostLink: '',
  quickSearchPlatform: '',
  quickSearchProject: '',
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 100,
  total: 0,
})

// 绩效表单验证规则
const performanceRules = {
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  social_id: [{ required: true, message: '请输入KOL ID', trigger: 'blur' }],
  project_code: [{ required: true, message: '请选择项目编码', trigger: 'change' }],
  post_link: [{ required: true, message: '请输入帖子链接', trigger: 'blur' }],
}

// 支付表单验证规则
const paymentRules = {
  payment_amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '支付金额不能为负数', trigger: 'blur' },
  ],
  paypal_accounts: [
    { required: true, message: '请输入PayPal账户', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' },
  ],
  tracker: [{ required: true, message: '请输入跟进人', trigger: 'blur' }],
  fund_source: [{ required: true, message: '请选择资金来源', trigger: 'change' }],
  payout_date: [{ required: true, message: '请选择支付日期', trigger: 'change' }],
}

// 默认列配置
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true, fixed: 'left' },
  {
    key: 'social_id',
    prop: 'social_id',
    label: 'KOL ID',
    width: 120,
    visible: true,
    fixed: 'left',
  },
  { key: 'nick_name', prop: 'nick_name', label: 'KOL Name', width: 120, visible: true },
  { key: 'platform', prop: 'platform', label: 'Platform', width: 120, visible: true },
  { key: 'project_code', prop: 'project_code', label: 'Project Code', width: 120, visible: true },
  { key: 'post_link', prop: 'post_link', label: 'Post Link', width: 200, visible: true },
  {
    key: 'post_date',
    prop: 'post_date',
    label: 'Post Date',
    width: 130,
    visible: true,
    sortable: true,
  },
  { key: 'cpm', prop: 'cpm', label: 'CPM', width: 100, visible: true, sortable: true },
  {
    key: 'engagement_rate',
    prop: 'engagement_rate',
    label: 'Engagement Rate',
    width: 165,
    visible: true,
    sortable: true,
  },
  {
    key: 'views_total',
    prop: 'views_total',
    label: 'Views Total',
    width: 130,
    visible: true,
    sortable: true,
  },
  {
    key: 'likes_total',
    prop: 'likes_total',
    label: 'Likes Total',
    width: 120,
    visible: true,
    sortable: true,
  },
  {
    key: 'comments_total',
    prop: 'comments_total',
    label: 'Comments Total',
    width: 155,
    visible: true,
    sortable: true,
  },
  {
    key: 'shares_total',
    prop: 'shares_total',
    label: 'Shares Total',
    width: 130,
    visible: true,
    sortable: true,
  },
  {
    key: 'payment_amount',
    prop: 'payment_amount',
    label: 'Payment Amount',
    width: 160,
    visible: true,
    sortable: true,
  },
  { key: 'status', prop: 'status', label: 'Status', width: 100, visible: true },
  {
    key: 'updated_at',
    prop: 'updated_at',
    label: 'Updated At',
    width: 150,
    visible: true,
    sortable: true,
  },
  {
    key: 'created_at',
    prop: 'created_at',
    label: 'Created At',
    width: 150,
    visible: true,
    sortable: true,
  },
  { key: 'actions', label: '操作', width: 120, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// Performance 页面允许的字段白名单（与 defaultColumns 完全一致）
const allowedPerformanceFields = new Set([
  'selection',
  'social_id',
  'nick_name',
  'platform',
  'project_code',
  'post_link',
  'post_date',
  'cpm',
  'engagement_rate',
  'views_total',
  'likes_total',
  'comments_total',
  'shares_total',
  'payment_amount',

  'updated_at',
  'created_at',
  'actions',
])

// 计算可见的列（添加字段白名单验证）
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => {
    // 只显示在白名单中的字段
    if (!allowedPerformanceFields.has(column.key)) {
      console.warn(`检测到不属于 Performance 页面的字段: ${column.key}，已自动过滤`)
      return false
    }
    return column.visible
  })
})

// 计算属性：确保传递给抽屉组件的数据类型正确
const selectedPerformanceData = computed(() => {
  // 只有当selectedKol是PerformanceDetailItem类型时才返回数据
  if (selectedKol.value && 'id' in selectedKol.value && 'kol_id' in selectedKol.value) {
    return selectedKol.value as PerformanceDetailItem
  }
  return null
})

// 本地存储键名 - 使用唯一标识符避免与其他页面冲突
const COLUMN_SETTINGS_KEY = 'performance-list-column-settings'

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 计算分页参数
    const skip = (pagination.currentPage - 1) * pagination.pageSize

    // 准备查询参数
    const params: KolPerformanceParams = {
      skip,
      limit: pagination.pageSize,
    }

    // 检查是否有搜索条件
    const hasSearchConditions =
      searchForm.quickSearchSocialId ||
      searchForm.quickSearchPostLink ||
      searchForm.quickSearchPlatform ||
      searchForm.quickSearchProject

    if (hasSearchConditions) {
      // 使用搜索接口
      const searchParams: KolPerformanceSearchParams = {}

      if (searchForm.quickSearchSocialId) {
        searchParams.social_id_like = searchForm.quickSearchSocialId
      }
      if (searchForm.quickSearchPostLink) {
        searchParams.post_link_like = searchForm.quickSearchPostLink
      }
      if (searchForm.quickSearchPlatform) {
        searchParams.platform = searchForm.quickSearchPlatform as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE'
      }
      if (searchForm.quickSearchProject) {
        searchParams.project_code = searchForm.quickSearchProject
      }

      const response = await searchKolPerformanceData(searchParams, params)
      kolList.value = response.items
      pagination.total = response.total
    } else {
      // 使用普通列表接口
      const response = await fetchKolPerformanceData(params)
      kolList.value = response.items
      pagination.total = response.total
    }

    console.log('KOL表现数据加载完成，共', kolList.value.length, '条记录')
  } catch (error) {
    console.error('加载KOL表现数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    quickSearchSocialId: '',
    quickSearchPostLink: '',
    quickSearchPlatform: '',
    quickSearchProject: '',
  })
  handleSearch()
}

// 注意：现在使用API搜索，不再需要客户端过滤函数

const applySearchTemplate = (template: SearchTemplate) => {
  Object.assign(searchForm, template.conditions)
  handleSearch()
}

const removeSearchTemplate = (templateId: string) => {
  const index = searchTemplates.value.findIndex((t) => t.id === templateId)
  if (index > -1) {
    searchTemplates.value.splice(index, 1)
  }
}

const exportSearchResults = () => {
  // 自动生成文件名
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-')
  exportConfig.filename = `KOL数据_${timestamp}`

  // 默认选择所有可见列
  exportConfig.fields = visibleColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)

  showExportDialog.value = true
}

const handleExport = () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请至少选择一个字段进行导出')
    return
  }

  try {
    // 获取要导出的数据
    const dataToExport = getExportData()

    // 根据格式导出
    switch (exportConfig.format) {
      case 'xlsx':
        exportToExcel(dataToExport)
        break
      case 'csv':
        exportToCSV(dataToExport)
        break
      case 'json':
        exportToJSON(dataToExport)
        break
    }

    showExportDialog.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getExportData = () => {
  let sourceData: KolItem[] = []

  // 根据导出范围获取数据
  switch (exportConfig.scope) {
    case 'all':
      // 获取所有数据需要调用API，暂时使用当前页数据
      sourceData = kolList.value
      break
    case 'current':
      // 当前页面显示的数据
      sourceData = kolList.value
      break
    case 'filtered':
      // 当前筛选条件下的数据（已通过API搜索获取）
      sourceData = kolList.value
      break
    case 'selected':
      // 这里需要实现表格选择功能，暂时用当前页数据
      sourceData = kolList.value
      break
  }

  // 根据选择的字段过滤数据
  const filteredData = sourceData.map((item) => {
    const exportItem: ExportItem = {}
    exportConfig.fields.forEach((field) => {
      const column = tableColumns.value.find((col) => col.key === field)
      const label = column?.label || field

      switch (field) {
        case 'performance_id':
          exportItem[label] = item.performance_id
          break
        case 'platform':
          exportItem[label] = getPlatformLabel(item.platform)
          break
        case 'social_id':
          exportItem[label] = item.social_id
          break
        case 'nick_name':
          exportItem[label] = item.nick_name
          break
        case 'project_code':
          exportItem[label] = item.project_code || ''
          break
        case 'post_link':
          exportItem[label] = item.post_link
          break
        case 'post_date':
          exportItem[label] = item.post_date ? formatDate(item.post_date) : ''
          break
        case 'cpm':
          exportItem[label] = item.cpm ?? ''
          break
        case 'engagement_rate':
          exportItem[label] = item.engagement_rate ?? ''
          break
        case 'views_total':
          exportItem[label] = item.views_total ?? ''
          break
        case 'likes_total':
          exportItem[label] = item.likes_total ?? ''
          break
        case 'comments_total':
          exportItem[label] = item.comments_total ?? ''
          break
        case 'shares_total':
          exportItem[label] = item.shares_total ?? ''
          break
        case 'payment_amount':
          exportItem[label] = item.payment_amount ?? ''
          break
        case 'status':
          exportItem[label] = item.status
          break
        case 'created_at':
          exportItem[label] = formatDate(item.created_at)
          break
        case 'updated_at':
          exportItem[label] = formatDate(item.updated_at)
          break
        default:
          exportItem[label] = (item as unknown as Record<string, unknown>)[field]?.toString() || ''
      }
    })
    return exportItem
  })

  return filteredData
}

const exportToExcel = (data: ExportItem[]) => {
  // 这里可以使用 xlsx 库来导出 Excel
  // 为了演示，我们先用 CSV 的方式
  exportToCSV(data)
}

const exportToCSV = (data: ExportItem[]) => {
  if (data.length === 0) return

  const headers = Object.keys(data[0])
  let csvContent = ''

  // 添加表头
  if (exportConfig.includeHeaders) {
    csvContent += headers.join(',') + '\n'
  }

  // 添加数据行
  data.forEach((row) => {
    const values = headers.map((header) => {
      const value = row[header]
      // 处理包含逗号的值，用引号包围
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    })
    csvContent += values.join(',') + '\n'
  })

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const exportToJSON = (data: ExportItem[]) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.json`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 字段选择方法
const selectAllFields = () => {
  exportConfig.fields = tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)
}

const clearAllFields = () => {
  exportConfig.fields = []
}

// 可导出的字段选项
const exportableFields = computed(() => {
  return tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => ({
      key: col.key,
      label: col.label,
      checked: exportConfig.fields.includes(col.key),
    }))
})

// 计算属性 - 活跃的筛选条件
const activeFilters = computed(() => {
  const filters: ActiveFilter[] = []
  if (searchForm.quickSearchSocialId) {
    filters.push({
      key: 'quickSearchSocialId',
      label: 'KOL ID',
      value: searchForm.quickSearchSocialId,
    })
  }
  if (searchForm.quickSearchPostLink) {
    filters.push({
      key: 'quickSearchPostLink',
      label: '帖子链接',
      value: searchForm.quickSearchPostLink,
    })
  }
  if (searchForm.quickSearchPlatform) {
    filters.push({
      key: 'quickSearchPlatform',
      label: '平台',
      value: searchForm.quickSearchPlatform,
    })
  }
  if (searchForm.quickSearchProject) {
    filters.push({
      key: 'quickSearchProject',
      label: '项目编码',
      value: searchForm.quickSearchProject,
    })
  }
  return filters
})

const hasActiveFilters = computed(() => activeFilters.value.length > 0)

// 计算属性 - 是否有高级筛选条件（除了快速搜索）
const removeFilter = (filterKey: string) => {
  switch (filterKey) {
    case 'quickSearchSocialId':
      searchForm.quickSearchSocialId = ''
      break
    case 'quickSearchPostLink':
      searchForm.quickSearchPostLink = ''
      break
    case 'quickSearchPlatform':
      searchForm.quickSearchPlatform = ''
      break
    case 'quickSearchProject':
      searchForm.quickSearchProject = ''
      break
  }
  handleSearch()
}

const clearAllFilters = () => {
  handleReset()
}

const handleView = async (row: KolItem) => {
  try {
    loading.value = true

    // 同时获取绩效数据和支付数据
    const [detailedPerformance, paymentData] = await Promise.all([
      fetchPerformanceById(row.performance_id),
      row.payment_id ? fetchPaymentById(row.payment_id) : Promise.resolve(null),
    ])

    console.log('获取到的绩效详情数据:', detailedPerformance)
    console.log('获取到的支付数据:', paymentData)

    // 将API返回的数据转换为抽屉组件需要的格式
    const drawerData: PerformanceDetailItem = {
      id: detailedPerformance.id,
      platform: detailedPerformance.platform.toLowerCase() as 'tiktok' | 'instagram' | 'youtube',
      kol_id: detailedPerformance.social_id,
      project_code: detailedPerformance.project_code,
      cpm: row.cpm ? parseFloat(row.cpm) : null, // 从表格数据中获取CPM值
      post_link: detailedPerformance.post_link,
      post_date: detailedPerformance.post_date,
      views_total: detailedPerformance.views_total,
      likes_total: detailedPerformance.likes_total,
      comments_total: detailedPerformance.comments_total,
      shares_total: detailedPerformance.shares_total,
      views_day1: detailedPerformance.views_day1,
      likes_day1: detailedPerformance.likes_day1,
      comments_day1: detailedPerformance.comments_day1,
      shares_day1: detailedPerformance.shares_day1,
      views_day3: detailedPerformance.views_day3,
      likes_day3: detailedPerformance.likes_day3,
      comments_day3: detailedPerformance.comments_day3,
      shares_day3: detailedPerformance.shares_day3,
      views_day7: detailedPerformance.views_day7,
      likes_day7: detailedPerformance.likes_day7,
      comments_day7: detailedPerformance.comments_day7,
      shares_day7: detailedPerformance.shares_day7,

      created_at: detailedPerformance.created_at,
      updated_at: detailedPerformance.updated_at,
      // 添加支付信息
      payment: paymentData,
    }

    // 设置选中的数据并显示抽屉
    selectedKol.value = drawerData
    showDetailDrawer.value = true
  } catch (error) {
    console.error('获取详情数据失败:', error)
    ElMessage.error('获取详情数据失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleEditFromDrawer = (performance: PerformanceDetailItem) => {
  // 可以在这里处理编辑事件的额外逻辑
  console.log('开始编辑 performance id:', performance.id)
}

const handleSaveFromDrawer = (updatedPerformance: PerformanceDetailItem) => {
  // 在列表中更新对应的数据
  const index = kolList.value.findIndex((item) => item.performance_id === updatedPerformance.id)
  if (index !== -1) {
    // 将PerformanceDetailItem的数据映射回KolItem格式
    const updatedKolItem: KolItem = {
      ...kolList.value[index],
      // 更新存在于两种类型中的字段
      platform: updatedPerformance.platform.toUpperCase() as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE',
      social_id: updatedPerformance.kol_id,
      project_code: updatedPerformance.project_code || '',
      post_link: updatedPerformance.post_link,
      post_date: updatedPerformance.post_date || null,
      views_total: updatedPerformance.views_total || null,
      likes_total: updatedPerformance.likes_total || null,
      comments_total: updatedPerformance.comments_total || null,
      shares_total: updatedPerformance.shares_total || null,
      updated_at: updatedPerformance.updated_at,
    }

    kolList.value[index] = updatedKolItem
    selectedKol.value = { ...updatedPerformance }
  }
  ElMessage.success(`绩效ID为${updatedPerformance.id} 的绩效信息已更新`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
}

const checkMoveAllowed = (evt: {
  draggedContext: { element: TableColumn }
  relatedContext?: { element: TableColumn }
}) => {
  const { draggedContext, relatedContext } = evt

  // 固定列不能移动
  const fixedColumns = ['selection', 'social_id', 'actions']

  // 如果拖拽的是固定列，不允许移动
  if (fixedColumns.includes(draggedContext.element.key)) {
    return false
  }

  // 如果目标位置是固定列的位置，也不允许移动
  if (relatedContext && fixedColumns.includes(relatedContext.element.key)) {
    return false
  }

  return true
}

// 工具方法
const formatNumber = (num: number | null | undefined) => {
  if (num === null || num === undefined) {
    return '-'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) {
    return '-'
  }
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const getPlatformLabel = (platform: string) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
    TIKTOK: 'TikTok',
    INSTAGRAM: 'Instagram',
    YOUTUBE: 'YouTube',
  }
  return labels[platform] || platform
}

const getEngagementClass = (rate: number | null | undefined) => {
  if (rate === null || rate === undefined) {
    return 'engagement-low'
  }
  if (rate >= 10) return 'engagement-excellent'
  if (rate >= 5) return 'engagement-good'
  if (rate >= 2) return 'engagement-normal'
  return 'engagement-low'
}

// 支付记录相关方法
const handleAddPaymentClose = () => {
  resetAllForms()
  showAddPaymentDialog.value = false
}

const handleFileChange = (file: { name: string; raw?: File }) => {
  if (file.raw) {
    paymentForm.payment_screenshot_filename = file.name
    paymentForm.payment_screenshot_file = file.raw
  }
}

const handleFileRemove = () => {
  paymentForm.payment_screenshot_filename = ''
  paymentForm.payment_screenshot_file = null
}

const handleCreatePaymentAndPerformance = async () => {
  if (!paymentFormRef.value) return

  try {
    // 验证表单
    await paymentFormRef.value.validate()

    // 额外验证必填字段
    if (
      !performanceForm.platform ||
      !performanceForm.social_id ||
      !performanceForm.project_code ||
      !performanceForm.post_link
    ) {
      ElMessage.warning('请填写所有必填的绩效信息')
      return
    }

    if (
      !paymentForm.payment_amount ||
      !paymentForm.paypal_accounts ||
      !paymentForm.tracker ||
      !paymentForm.fund_source ||
      !paymentForm.payout_date
    ) {
      ElMessage.warning('请填写所有必填的支付信息')
      return
    }

    paymentLoading.value = true

    // 准备统一接口的数据
    const performancePaymentData: PerformancePaymentRequest = {
      platform: performanceForm.platform as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE',
      social_id: performanceForm.social_id,
      project_code: performanceForm.project_code,
      post_link: performanceForm.post_link,
      post_date: performanceForm.post_date || null,
      payment_amount: paymentForm.payment_amount,
      paypal_accounts: paymentForm.paypal_accounts,
      tracker: paymentForm.tracker,
      fund_source: paymentForm.fund_source,
      payout_date: paymentForm.payout_date || null,
      note: paymentForm.note,
      payment_screenshot_file: paymentForm.payment_screenshot_file || null,
    }

    // 调用统一的绩效支付接口
    await createPerformancePayment(performancePaymentData)

    ElMessage.success('绩效和支付记录创建成功')
    showAddPaymentDialog.value = false
    resetAllForms()

    // 重新加载数据
    await loadData()
  } catch (error) {
    if (error === false) {
      // 表单验证失败
      ElMessage.warning('请检查输入信息')
    } else {
      console.error('创建记录失败:', error)
      ElMessage.error('创建失败，请重试')
    }
  } finally {
    paymentLoading.value = false
  }
}

const resetAllForms = () => {
  // 重置绩效表单
  Object.assign(performanceForm, {
    platform: '',
    social_id: '',
    project_code: '',
    post_link: '',
    post_date: null,
  })

  // 重置支付表单
  Object.assign(paymentForm, {
    performance_id: null,
    payment_amount: null,
    paypal_accounts: '',
    tracker: '',
    payout_date: null,
    fund_source: '',
    payment_screenshot_filename: '',
    payment_screenshot_file: null,
    note: '',
  })

  // 重置状态
  currentStep.value = 1
  createdPerformance.value = null
  createdPayment.value = null
}

// 生命周期
onMounted(() => {
  // 一次性清理：检查并清理可能冲突的本地存储数据
  cleanupConflictedLocalStorage()

  loadData()
  loadProjectList()
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      // 验证列配置是否适用于当前页面（检查是否包含 Performance 特有的字段）
      // 过滤掉不属于 Performance 页面的字段
      const filteredColumns = parsedColumns.filter((col: TableColumn) =>
        allowedPerformanceFields.has(col.key),
      )

      const hasPerformanceFields = filteredColumns.some(
        (col: TableColumn) =>
          col.key === 'post_link' ||
          col.key === 'cpm' ||
          col.key === 'views_total' ||
          col.key === 'engagement_rate' ||
          col.key === 'payment_amount',
      )

      if (hasPerformanceFields && filteredColumns.length > 0) {
        // 检查是否有被过滤掉的字段
        if (filteredColumns.length < parsedColumns.length) {
          const removedFields = parsedColumns
            .filter((col: TableColumn) => !allowedPerformanceFields.has(col.key))
            .map((col: TableColumn) => col.key)
          console.warn(`检测到并过滤了不属于 Performance 页面的字段: ${removedFields.join(', ')}`)
        }
        tableColumns.value = filteredColumns
        // 如果有字段被过滤，保存清理后的配置
        if (filteredColumns.length < parsedColumns.length) {
          saveColumnSettings()
        }
      } else {
        // 如果不包含 Performance 特有字段，说明可能是其他页面的配置，使用默认配置
        console.warn('检测到可能来自其他页面的列配置，使用默认配置')
        tableColumns.value = [...defaultColumns]
        saveColumnSettings() // 保存正确的默认配置
      }
    } catch (error) {
      console.error('解析列配置失败，使用默认配置:', error)
      tableColumns.value = [...defaultColumns]
      saveColumnSettings()
    }
  }
})

// 清理冲突的本地存储数据（一次性执行）
const cleanupConflictedLocalStorage = () => {
  const cleanupFlag = 'performance-cleanup-done-v1'
  if (localStorage.getItem(cleanupFlag)) {
    return // 已经清理过了
  }

  try {
    // 检查是否存在旧的冲突键名数据
    const oldColumnData = localStorage.getItem('kol-list-column-settings')
    if (oldColumnData) {
      const parsedData = JSON.parse(oldColumnData)
      // 检查是否包含其他页面的字段（如 social_id, ai_scored_at 等）
      const hasKolFields = parsedData.some((col: TableColumn) => col.key === 'social_id')
      const hasMismatchFields = parsedData.some((col: TableColumn) => col.key === 'ai_scored_at')

      if (hasKolFields || hasMismatchFields) {
        // 这是其他页面的数据，不应该影响 Performance 页面
        console.log('清理检测到的其他页面列配置数据')
        // 不删除原数据，但确保当前页面使用正确的配置
        localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(defaultColumns))
      }
    }

    // 标记清理已完成
    localStorage.setItem(cleanupFlag, 'true')
  } catch (error) {
    console.error('清理本地存储时出错:', error)
  }
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const projects = await fetchAllProjects()
    projectList.value = projects.map((project) => ({
      code: project.code,
      name: project.name,
    }))
  } catch (error) {
    console.error('加载项目列表失败:', error)
    // 如果API调用失败，项目列表为空
    projectList.value = []
  }
}
</script>

<style scoped>
.kol-list-container {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

/* 智能搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

/* 快速搜索栏样式 */
.quick-search-bar {
  margin-bottom: 16px;
}

.quick-search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.quick-search-input {
  flex: 1;
  min-width: 0;
}

.search-btn {
  flex-shrink: 0;
  min-width: 100px;
}

/* 统一的快速搜索组件样式 */
.quick-search-input {
  --el-input-border-radius: 12px;
  --el-select-border-radius: 12px;
}

/* 输入框样式 */
.quick-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 选择框样式 */
.quick-search-input :deep(.el-select__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-select__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-select__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-select__selected-item) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

.quick-search-input :deep(.el-select__placeholder) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 图标样式统一 */
.quick-search-input :deep(.el-input__prefix),
.quick-search-input :deep(.el-select__prefix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 32px;
}

.quick-search-input :deep(.el-input__prefix .el-icon),
.quick-search-input :deep(.el-select__prefix .el-icon) {
  font-size: 16px;
  color: #6b7280;
}

/* 清除按钮样式统一 */
.quick-search-input :deep(.el-input__suffix),
.quick-search-input :deep(.el-select__suffix) {
  display: flex;
  align-items: center;
  height: 32px;
}

.quick-search-input :deep(.el-input__clear),
.quick-search-input :deep(.el-select__caret) {
  font-size: 14px;
  color: #9ca3af;
}

.search-btn {
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  min-height: 48px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 容器响应式优化 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }

  .header-left {
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-right {
    justify-content: center;
    gap: 8px;
  }

  .header-right .el-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 13px;
    padding: 8px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 14px;
  }

  .kol-list-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 18px;
  }

  .page-description {
    font-size: 12px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
    min-width: auto;
    justify-content: center;
    font-size: 12px;
    padding: 10px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 13px;
  }

  .kol-list-container {
    padding: 12px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .page-header {
    padding: 18px 22px;
  }

  .header-right {
    gap: 10px;
  }

  .header-right .el-button {
    font-size: 13px;
    padding: 8px 14px;
  }
}

/* 平板设备优化 */
@media (max-width: 900px) and (min-width: 481px) {
  .page-header {
    padding: 16px 20px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 100px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.header-right .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header-right .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 按钮图标优化 */
.header-right .el-button .el-icon {
  transition: transform 0.3s ease;
}

.header-right .el-button:hover .el-icon {
  transform: scale(1.1);
}

.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

/* 快速搜索栏样式 */
.quick-search-bar {
  margin-bottom: 16px;
}

.quick-search-input {
  --el-input-border-radius: 12px;
}

.quick-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
}

.quick-search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-suffix {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.search-btn {
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 600;
}

/* 搜索模板样式 */
.search-templates {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.templates-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 当前筛选条件样式 */
.current-filters {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.filters-label {
  font-size: 14px;
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 12px;
}

.filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-tag {
  background: white;
  border: 1px solid #c7d2fe;
  color: #4338ca;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  background: #eef2ff;
  transform: translateY(-1px);
}

.clear-all {
  color: #dc2626;
  font-size: 13px;
  padding: 4px 8px;
  margin-left: 8px;
}

.clear-all:hover {
  background: #fee2e2;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filters-list {
    justify-content: center;
  }
}

.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-card {
  margin-bottom: 20px;
}

/* KOL 信息样式 */
.kol-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nick-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 互动率样式 */
.engagement-excellent {
  color: #67c23a;
  font-weight: 600;
}

.engagement-good {
  color: #409eff;
  font-weight: 600;
}

.engagement-normal {
  color: #e6a23c;
  font-weight: 600;
}

.engagement-low {
  color: #f56c6c;
  font-weight: 600;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 禁用标签区域的动画 */
.tags-container {
  transition: none !important;
}

.tags-container * {
  transition: none !important;
  animation: none !important;
}

/* 自定义标签样式 */
.custom-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 4px;
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

/* 列设置弹窗样式 */
.column-settings {
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-header span {
  font-size: 14px;
  color: #666;
}

.column-list {
  min-height: 150px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.column-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #909399;
}

.column-item:active {
  cursor: grabbing;
}

.fixed-column {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px 12px !important;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.fixed-label {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 导出弹窗样式 */
.export-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.export-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

.export-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.export-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 16px;
}

.export-content {
  padding: 24px;
}

.export-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.form-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.file-extension {
  color: #9ca3af;
  font-size: 12px;
  padding: 0 8px;
}

.field-selection {
  margin-bottom: 0;
}

.field-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.selection-info {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selection-buttons {
  display: flex;
  gap: 8px;
}

.selection-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
  color: #409eff;
}

.selection-buttons .el-button:hover {
  color: #66b1ff;
}

.field-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background: #f9fafb;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
}

.field-item {
  margin: 0;
  font-size: 13px;
}

.field-item :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #374151;
}

.export-dialog .dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.export-dialog .dialog-footer .el-button {
  border-radius: 6px;
}

/* 添加支付记录弹窗样式 */
.add-payment-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.add-payment-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.add-payment-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.add-payment-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.add-payment-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 支付表单容器 */
.payment-form-container {
  background: #f8fafc;
  padding: 24px;
}

.payment-form {
  background: transparent;
}

/* 表单分组样式 */
.payment-form .form-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.payment-form .form-section:last-child {
  margin-bottom: 0;
}

.payment-form .section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.payment-form .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.payment-form .section-title .el-icon {
  color: #667eea;
}

.payment-form .section-content {
  padding: 24px;
}

/* 表单项样式优化 */
.payment-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.payment-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.payment-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.payment-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.payment-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.payment-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.payment-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

.payment-form :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.payment-form :deep(.el-textarea__inner:hover) {
  border-color: #667eea;
}

.payment-form :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.payment-form :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

/* 文件上传样式 */
.payment-upload {
  width: 100%;
}

.payment-upload :deep(.el-upload) {
  width: 100%;
}

.payment-upload :deep(.el-upload-dragger) {
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.payment-upload :deep(.el-upload-dragger:hover) {
  border-color: #667eea;
  background: #f0f4ff;
}

.payment-upload :deep(.el-icon--upload) {
  color: #667eea;
  font-size: 40px;
  margin-bottom: 8px;
}

.payment-upload :deep(.el-upload__text) {
  color: #374151;
  font-size: 14px;
}

.payment-upload :deep(.el-upload__text em) {
  color: #667eea;
  font-weight: 500;
}

.payment-upload :deep(.el-upload__tip) {
  color: #6b7280;
  font-size: 12px;
  margin-top: 8px;
}

/* 弹窗底部按钮 */
.add-payment-dialog .dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-payment-dialog .footer-left {
  display: flex;
  gap: 12px;
}

.add-payment-dialog .footer-right {
  display: flex;
  gap: 12px;
}

.add-payment-dialog .dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 响应式 */
@media (max-width: 768px) {
  .add-payment-dialog {
    width: 95% !important;
    margin: 2.5vh auto !important;
  }

  .add-payment-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }

  .add-payment-dialog :deep(.el-dialog__title) {
    font-size: 18px;
  }

  .payment-form-container {
    padding: 16px;
  }

  .payment-form .section-content {
    padding: 16px;
  }

  .payment-form :deep(.el-col) {
    margin-bottom: 16px;
  }

  .add-payment-dialog .dialog-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .add-payment-dialog .footer-left,
  .add-payment-dialog .footer-right {
    width: 100%;
    justify-content: center;
  }

  .add-payment-dialog .dialog-footer .el-button {
    flex: 1;
  }

  .export-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .field-grid {
    grid-template-columns: 1fr;
  }

  .field-selection-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    text-align: center;
  }

  .selection-buttons {
    justify-content: center;
  }

  .filters-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Post Link 样式 */
.post-link-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.post-link-text:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 空值显示样式 */
.no-engagement {
  color: #909399;
  font-style: italic;
}
</style>
