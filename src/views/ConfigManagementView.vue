<template>
  <div class="config-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">配置管理</h1>
        <p class="page-description">管理项目配置和邮件模板设置</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧：项目管理 -->
      <div class="projects-section">
        <el-card class="section-card" shadow="never">
          <template #header>
            <div class="section-header">
              <div class="header-title">
                <el-icon><Folder /></el-icon>
                <span>项目管理</span>
                <el-tag type="info" size="small" class="count-tag">{{ projects.length }}</el-tag>
              </div>
              <el-button type="primary" size="default" @click="showAddProjectDialog = true">
                <el-icon><Plus /></el-icon>
                添加项目
              </el-button>
            </div>
          </template>

          <div class="projects-list" v-loading="projectsLoading">
            <div v-if="projects.length === 0" class="empty-state">
              <el-empty description="暂无项目">
                <el-button type="primary" @click="showAddProjectDialog = true"
                  >创建第一个项目</el-button
                >
              </el-empty>
            </div>
            <div v-else class="projects-grid">
              <div
                v-for="project in projects"
                :key="project.code"
                class="project-card"
                :class="{ selected: selectedProject?.code === project.code }"
                @click="selectProject(project)"
              >
                <div class="project-header">
                  <div class="project-icon">
                    <el-icon><Folder /></el-icon>
                  </div>
                  <div class="project-actions">
                    <el-dropdown trigger="click" @command="handleProjectAction">
                      <el-button text size="small" class="action-btn">
                        <el-icon><More /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="{ action: 'edit', project }">
                            <el-icon><Edit /></el-icon>
                            编辑项目
                          </el-dropdown-item>
                          <el-dropdown-item :command="{ action: 'delete', project }" divided>
                            <el-icon><Delete /></el-icon>
                            删除项目
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
                <div class="project-content">
                  <h3 class="project-name">{{ project.code }}</h3>
                  <p class="project-code">{{ project.name }}</p>
                  <div class="project-stats">
                    <span class="stat-item">
                      <el-icon><Message /></el-icon>
                      {{ getTemplateCount(project.code) }} 个模板
                    </span>
                    <span class="stat-item">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(project.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：邮件模板管理 -->
      <div class="templates-section">
        <el-card class="section-card" shadow="never">
          <template #header>
            <div class="section-header">
              <div class="header-title">
                <el-icon><Message /></el-icon>
                <span>邮件模板</span>
                <el-tag v-if="selectedProject" type="success" size="small" class="project-tag">
                  {{ selectedProject.code }}
                </el-tag>
                <el-tag v-if="selectedProject" type="info" size="small" class="count-tag">
                  {{ filteredTemplates.length }}
                </el-tag>
              </div>
              <el-button
                type="primary"
                size="default"
                @click="handleAddTemplate"
                :disabled="!selectedProject"
              >
                <el-icon><Plus /></el-icon>
                添加模板
              </el-button>
            </div>
          </template>

          <div class="templates-content" v-loading="templatesLoading">
            <div v-if="!selectedProject" class="placeholder-state">
              <div class="placeholder-content">
                <el-icon class="placeholder-icon"><FolderOpened /></el-icon>
                <h3>请选择项目</h3>
                <p>选择左侧的项目来查看和管理邮件模板</p>
              </div>
            </div>

            <div v-else-if="filteredTemplates.length === 0" class="empty-state">
              <el-empty description="该项目暂无邮件模板">
                <el-button type="primary" @click="handleAddTemplate">创建第一个模板</el-button>
              </el-empty>
            </div>

            <div v-else class="templates-table">
              <el-table
                :data="filteredTemplates"
                stripe
                style="width: 100%"
                max-height="450"
                :scrollbar-always-on="true"
              >
                <el-table-column prop="code" label="模板代码" width="120" fixed="left" />
                <el-table-column prop="name" label="模板名称" min-width="150" />
                <el-table-column prop="from_email" label="发件人邮箱" min-width="180" />
                <el-table-column prop="postmark_token" label="Postmark Token" min-width="120">
                  <template #default="{ row }">
                    <span v-if="row.postmark_token" class="token-masked">
                      {{ maskToken(row.postmark_token) }}
                    </span>
                    <span v-else class="no-data">未配置</span>
                  </template>
                </el-table-column>
                <el-table-column prop="note" label="备注" min-width="120">
                  <template #default="{ row }">
                    <span v-if="row.note">{{ row.note }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="140" fixed="right">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="editTemplate(row)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button link type="danger" size="small" @click="deleteTemplate(row)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加项目弹窗 -->
    <el-dialog
      v-model="showAddProjectDialog"
      :title="editingProject ? '编辑项目' : '添加项目'"
      width="600px"
      class="project-dialog"
      append-to-body
    >
      <div class="project-form-container" v-loading="projectSubmitting">
        <el-form
          ref="projectFormRef"
          :model="projectForm"
          :rules="projectRules"
          label-width="100px"
          class="project-form"
        >
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Folder /></el-icon>
                项目信息
              </h4>
            </div>
            <div class="section-content">
              <el-form-item label="项目代码" prop="code">
                <el-input
                  v-model="projectForm.code"
                  placeholder="请输入项目代码"
                  size="large"
                  :disabled="!!editingProject"
                />
                <div class="form-tip">
                  <el-icon><InfoFilled /></el-icon>
                  项目代码用作唯一标识，创建后不可修改
                </div>
              </el-form-item>

              <el-form-item label="项目名称" prop="name">
                <el-input v-model="projectForm.name" placeholder="请输入项目名称" size="large" />
              </el-form-item>
              <el-form-item label="项目描述" prop="description">
                <el-input
                  v-model="projectForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入项目描述（可选）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelProject" :disabled="projectSubmitting">取消</el-button>
          <el-button type="primary" @click="submitProject" :loading="projectSubmitting">
            <el-icon><Check /></el-icon>
            {{ editingProject ? '保存修改' : '创建项目' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加邮件模板弹窗 -->
    <el-dialog
      v-model="showAddTemplateDialog"
      :title="editingTemplate ? '编辑邮件模板' : '添加邮件模板'"
      width="800px"
      class="template-dialog"
      append-to-body
      @open="handleTemplateDialogOpen"
    >
      <div class="template-form-container" v-loading="templateSubmitting">
        <el-form
          ref="templateFormRef"
          :model="templateForm"
          :rules="templateRules"
          label-width="120px"
          class="template-form"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Message /></el-icon>
                基本信息
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="模板代码" prop="code">
                    <el-input
                      v-model="templateForm.code"
                      placeholder="请输入模板代码"
                      size="large"
                      :disabled="!!editingTemplate"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="模板名称" prop="name">
                    <el-input
                      v-model="templateForm.name"
                      placeholder="请输入模板名称"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="所属项目" prop="project_code">
                <el-select
                  v-model="templateForm.project_code"
                  placeholder="请选择项目"
                  style="width: 100%"
                  size="large"
                  :disabled="!!selectedProject"
                >
                  <el-option
                    v-for="project in projects"
                    :key="project.code"
                    :label="`${project.code} (${project.name})`"
                    :value="project.code"
                  />
                </el-select>
                <div v-if="selectedProject" class="form-tip">
                  <el-icon><InfoFilled /></el-icon>
                  当前选中项目：{{ selectedProject.code }} ({{ selectedProject.name }})
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- Postmark 配置 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Setting /></el-icon>
                Postmark 配置
              </h4>
            </div>
            <div class="section-content">
              <el-form-item label="发件人邮箱" prop="from_email">
                <el-input
                  v-model="templateForm.from_email"
                  placeholder="请输入发件人邮箱"
                  size="large"
                />
              </el-form-item>

              <el-form-item label="Postmark Token" prop="postmark_token">
                <el-input
                  v-model="templateForm.postmark_token"
                  placeholder="请输入 Postmark API Token"
                  size="large"
                  show-password
                />
                <div class="form-tip">
                  <el-icon><InfoFilled /></el-icon>
                  用于连接 Postmark 邮件服务的 API 令牌
                </div>
              </el-form-item>

              <el-form-item label="备注信息" prop="note">
                <el-input
                  v-model="templateForm.note"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息（可选）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelTemplate" :disabled="templateSubmitting">取消</el-button>
          <el-button type="primary" @click="submitTemplate" :loading="templateSubmitting">
            <el-icon><Check /></el-icon>
            {{ editingTemplate ? '保存修改' : '创建模板' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Folder,
  FolderOpened,
  Message,
  Plus,
  Edit,
  Delete,
  More,
  Clock,
  Refresh,
  Setting,
  InfoFilled,
  Check,
} from '@element-plus/icons-vue'
import {
  fetchAllProjects,
  createProject,
  updateProject,
  deleteProject as deleteProjectApi,
} from '../services/projectApi'
import {
  fetchEmailTemplates,
  createEmailTemplate,
  updateEmailTemplate,
  deleteEmailTemplate,
  type EmailTemplate,
} from '../services/emailTemplateApi'

// 类型定义
interface Project {
  code: string
  name: string
  description: string
  created_at: string
  updated_at: string
}

interface ProjectForm {
  code: string
  name: string
  description: string
}

interface TemplateForm {
  code: string
  name: string
  project_code: string
  postmark_token: string
  from_email: string
  note: string
}

// 响应式数据
const projectsLoading = ref(false)
const templatesLoading = ref(false)
const projects = ref<Project[]>([])
const templates = ref<EmailTemplate[]>([])
const selectedProject = ref<Project | null>(null)

// 弹窗状态
const showAddProjectDialog = ref(false)
const showAddTemplateDialog = ref(false)
const projectSubmitting = ref(false)
const templateSubmitting = ref(false)
const editingProject = ref<Project | null>(null)
const editingTemplate = ref<EmailTemplate | null>(null)

// 表单引用
const projectFormRef = ref<FormInstance>()
const templateFormRef = ref<FormInstance>()

// 表单数据
const projectForm = reactive<ProjectForm>({
  code: '',
  name: '',
  description: '',
})

const templateForm = reactive<TemplateForm>({
  code: '',
  name: '',
  project_code: '',
  postmark_token: '',
  from_email: '',
  note: '',
})

// 表单验证规则
const projectRules: FormRules = {
  code: [
    { required: true, message: '请输入项目代码', trigger: 'blur' },
    { min: 2, max: 50, message: '项目代码长度在 2 到 50 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '项目代码只能包含字母、数字、下划线和连字符',
      trigger: 'blur',
    },
  ],
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 255, message: '项目名称长度在 2 到 255 个字符', trigger: 'blur' },
  ],
  description: [{ max: 500, message: '项目描述不能超过 500 个字符', trigger: 'blur' }],
}

const templateRules: FormRules = {
  code: [
    { required: true, message: '请输入模板代码', trigger: 'blur' },
    { min: 2, max: 50, message: '模板代码长度在 2 到 50 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '模板代码只能包含字母、数字、下划线和连字符',
      trigger: 'blur',
    },
  ],
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 255, message: '模板名称长度在 2 到 255 个字符', trigger: 'blur' },
  ],
  project_code: [{ required: true, message: '请选择项目', trigger: 'change' }],
  from_email: [{ type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }],
}

// 计算属性
const filteredTemplates = computed(() => {
  if (!selectedProject.value) return []
  return templates.value.filter((template) => template.project_code === selectedProject.value?.code)
})

// 方法
const refreshData = async () => {
  await Promise.all([loadProjects(), loadTemplates()])
  ElMessage.success('数据刷新成功')
}

const loadProjects = async () => {
  projectsLoading.value = true
  try {
    const projectList = await fetchAllProjects()
    projects.value = projectList
  } catch {
    ElMessage.error('加载项目数据失败')
  } finally {
    projectsLoading.value = false
  }
}

const loadTemplates = async () => {
  templatesLoading.value = true
  try {
    // 使用真实API获取所有邮件模板
    const response = await fetchEmailTemplates({ skip: 0, limit: 1000 })
    templates.value = response.items
  } catch (error) {
    console.error('加载模板数据失败:', error)
    ElMessage.error('加载模板数据失败')
  } finally {
    templatesLoading.value = false
  }
}

const selectProject = (project: Project) => {
  selectedProject.value = project
}

const getTemplateCount = (projectCode: string) => {
  return templates.value.filter((template) => template.project_code === projectCode).length
}

const handleProjectAction = ({ action, project }: { action: string; project: Project }) => {
  if (action === 'edit') {
    editProject(project)
  } else if (action === 'delete') {
    deleteProject(project)
  }
}

const editProject = (project: Project) => {
  editingProject.value = project
  Object.assign(projectForm, {
    code: project.code,
    name: project.name,
    description: project.description || '',
  })
  showAddProjectDialog.value = true
}

const deleteProject = async (project: Project) => {
  const templateCount = getTemplateCount(project.code)

  let message = `确定要删除项目 "${project.name}" 吗？`
  if (templateCount > 0) {
    message += `\n\n注意：该项目包含 ${templateCount} 个邮件模板，删除项目将同时删除这些模板。`
  }

  try {
    await ElMessageBox.confirm(message, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用删除API
    await deleteProjectApi(project.code)

    // 从本地列表中移除
    const index = projects.value.findIndex((p) => p.code === project.code)
    if (index > -1) {
      projects.value.splice(index, 1)
      // 删除关联的模板
      templates.value = templates.value.filter((t) => t.project_code !== project.code)
      // 如果删除的是当前选中的项目，清除选中状态
      if (selectedProject.value?.code === project.code) {
        selectedProject.value = null
      }
      ElMessage.success('项目删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除项目失败')
    }
  }
}

const submitProject = async () => {
  if (!projectFormRef.value) return

  try {
    await projectFormRef.value.validate()
    projectSubmitting.value = true

    // 检查项目代码是否已存在（仅在新建时）
    if (!editingProject.value) {
      const exists = projects.value.some((p) => p.code === projectForm.code)
      if (exists) {
        ElMessage.error('项目代码已存在，请选择其他代码')
        return
      }
    }

    if (editingProject.value) {
      // 编辑模式
      const updatedProject = await updateProject(editingProject.value.code, {
        name: projectForm.name,
        description: projectForm.description,
        status: 'active',
      })

      // 更新本地数据
      const index = projects.value.findIndex((p) => p.code === editingProject.value!.code)
      if (index > -1) {
        projects.value[index] = updatedProject
      }
      ElMessage.success('项目更新成功')
    } else {
      // 新建模式
      const newProject = await createProject({
        code: projectForm.code,
        name: projectForm.name,
        description: projectForm.description,
      })

      projects.value.unshift(newProject)
      ElMessage.success('项目创建成功')
    }

    showAddProjectDialog.value = false
    resetProjectForm()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('操作失败，请重试')
    }
  } finally {
    projectSubmitting.value = false
  }
}

const cancelProject = () => {
  showAddProjectDialog.value = false
  resetProjectForm()
}

const resetProjectForm = () => {
  editingProject.value = null
  Object.assign(projectForm, {
    code: '',
    name: '',
    description: '',
  })
  projectFormRef.value?.resetFields()
}

const editTemplate = (template: EmailTemplate) => {
  editingTemplate.value = template
  Object.assign(templateForm, {
    code: template.code,
    name: template.name,
    project_code: template.project_code,
    postmark_token: template.postmark_token || '',
    from_email: template.from_email || '',
    note: template.note || '',
  })
  showAddTemplateDialog.value = true
}

const deleteTemplate = async (template: EmailTemplate) => {
  try {
    await ElMessageBox.confirm(`确定要删除邮件模板 "${template.name}" 吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用删除API
    await deleteEmailTemplate(template.id)

    // 从本地列表中移除
    const index = templates.value.findIndex((t) => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
      ElMessage.success('模板删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败，请重试')
    }
  }
}

const submitTemplate = async () => {
  if (!templateFormRef.value) return

  try {
    await templateFormRef.value.validate()
    templateSubmitting.value = true

    // 检查模板代码是否已存在（仅在新建时）
    // if (!editingTemplate.value) {
    //   const exists = templates.value.some((t) => t.code === templateForm.code)
    //   if (exists) {
    //     ElMessage.error('模板代码已存在，请选择其他代码')
    //     return
    //   }
    // }

    // 模拟 API 调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    if (editingTemplate.value) {
      // 编辑模式
      try {
        const updatedTemplate = await updateEmailTemplate(editingTemplate.value.id, {
          name: templateForm.name,
          project_code: templateForm.project_code,
          postmark_token: templateForm.postmark_token || undefined,
          from_email: templateForm.from_email || undefined,
          note: templateForm.note || undefined,
        })

        // 更新本地数据
        const index = templates.value.findIndex((t) => t.id === editingTemplate.value!.id)
        if (index > -1) {
          templates.value[index] = updatedTemplate
        }
        ElMessage.success('模板更新成功')
      } catch (error) {
        console.error('更新模板失败:', error)
        ElMessage.error('更新模板失败，请重试')
        return
      }
    } else {
      // 新建模式
      try {
        const newTemplate = await createEmailTemplate({
          code: templateForm.code,
          name: templateForm.name,
          project_code: templateForm.project_code,
          postmark_token: templateForm.postmark_token || undefined,
          from_email: templateForm.from_email || undefined,
          note: templateForm.note || undefined,
        })
        templates.value.unshift(newTemplate)
        ElMessage.success('模板创建成功')
      } catch (error) {
        console.error('创建模板失败:', error)
        ElMessage.error('创建模板失败，请重试')
        return
      }
    }

    showAddTemplateDialog.value = false
    resetTemplateForm()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('操作失败，请重试')
    }
  } finally {
    templateSubmitting.value = false
  }
}

const cancelTemplate = () => {
  showAddTemplateDialog.value = false
  resetTemplateForm()
}

const resetTemplateForm = () => {
  editingTemplate.value = null
  Object.assign(templateForm, {
    code: '',
    name: '',
    project_code: selectedProject.value?.code || '',
    postmark_token: '',
    from_email: '',
    note: '',
  })
  templateFormRef.value?.resetFields()
}

// 工具方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const maskToken = (token: string) => {
  if (token.length <= 8) return token
  return token.substring(0, 4) + '*'.repeat(token.length - 8) + token.substring(token.length - 4)
}

// 监听选中项目变化，自动设置模板表单的项目
const handleAddTemplate = () => {
  // 重置编辑状态，确保是添加模式
  editingTemplate.value = null
  if (selectedProject.value) {
    templateForm.project_code = selectedProject.value.code
  }
  showAddTemplateDialog.value = true
}

// 监听弹窗显示状态，确保表单数据正确
const handleTemplateDialogOpen = () => {
  if (selectedProject.value && !editingTemplate.value) {
    templateForm.project_code = selectedProject.value.code
  }
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadTemplates()
})

// 暴露方法给模板使用
defineExpose({
  handleAddTemplate,
})
</script>

<style scoped>
.config-management-container {
  padding: 20px;
  min-height: 100vh;
  background: #f8fafc;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 主内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 24px;
  align-items: start;
  min-height: 70vh;
  max-height: 80vh; /* 设置最大高度，防止溢出 */
}

/* 模板区域 */
.templates-section {
  height: 100%;
  max-height: 80vh; /* 确保模板区域不会超出视口 */
  overflow: hidden;
}

.templates-section .section-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.templates-section .section-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.templates-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 区域卡片 */
.section-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.section-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.section-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
}

.section-card :deep(.el-card__body) {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.header-title .el-icon {
  color: #667eea;
  font-size: 20px;
}

.count-tag {
  margin-left: 8px;
}

.project-tag {
  margin-left: 8px;
  font-weight: 500;
}

/* 项目列表容器 */
.projects-list {
  max-height: 70vh;
  overflow-y: auto;
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.project-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.project-card:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.project-card:hover::before {
  transform: scaleX(1);
}

.project-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #ecf5ff 0%, #f0f4ff 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.project-card.selected::before {
  transform: scaleX(1);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.project-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.project-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-actions {
  opacity: 1;
}

.action-btn {
  color: #6b7280;
  transition: color 0.3s ease;
}

.action-btn:hover {
  color: #667eea;
}

.project-content {
  flex: 1;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.project-code {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.project-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.stat-item .el-icon {
  font-size: 14px;
  color: #9ca3af;
}

/* 模板区域 */
.templates-content {
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;
}

.placeholder-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
  color: #6b7280;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #d1d5db;
}

.placeholder-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.placeholder-content p {
  font-size: 14px;
  margin: 0;
  color: #6b7280;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* 模板表格 */
.templates-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  max-height: 600px; /* 设置最大高度 */
}

.templates-table :deep(.el-table) {
  border-radius: 8px;
}

.templates-table :deep(.el-table__body-wrapper) {
  /* 确保表格内容可以滚动 */
  overflow-y: auto;
}

.templates-table :deep(.el-table__header-wrapper) {
  /* 固定表头 */
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
}

/* 表格滚动条样式优化 */
.templates-table :deep(.el-scrollbar__wrap) {
  overflow-x: auto;
}

.templates-table :deep(.el-table__body-wrapper) {
  /* 确保水平滚动在内容溢出时可用 */
  overflow-x: auto;
}

/* 表格列最小宽度确保内容可读性 */
.templates-table :deep(.el-table__cell) {
  padding: 12px 8px;
  word-break: break-word;
}

/* 操作列固定样式优化 */
.templates-table :deep(.is-right) {
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.token-masked {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
}

.no-data {
  color: #9ca3af;
  font-style: italic;
}

/* 弹窗样式 */
.project-dialog :deep(.el-dialog),
.template-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.project-dialog :deep(.el-dialog__header),
.template-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.project-dialog :deep(.el-dialog__title),
.template-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.project-dialog :deep(.el-dialog__close),
.template-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.project-dialog :deep(.el-dialog__body),
.template-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 表单容器 */
.project-form-container,
.template-form-container {
  background: #f8fafc;
  padding: 24px;
}

/* 表单分组样式 */
.form-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section .section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.form-section .section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding: 24px;
}

/* 表单项样式 */
.project-form :deep(.el-form-item),
.template-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.project-form :deep(.el-form-item__label),
.template-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.project-form :deep(.el-input__wrapper),
.template-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.project-form :deep(.el-input__wrapper:hover),
.template-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.project-form :deep(.el-input__wrapper.is-focus),
.template-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.project-form :deep(.el-select .el-input__wrapper),
.template-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.project-form :deep(.el-textarea__inner),
.template-form :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.project-form :deep(.el-textarea__inner:hover),
.template-form :deep(.el-textarea__inner:hover) {
  border-color: #667eea;
}

.project-form :deep(.el-textarea__inner:focus),
.template-form :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 表单提示 */
.form-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 6px;
  font-size: 12px;
  color: #6b7280;
  padding: 6px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.form-tip .el-icon {
  color: #667eea;
  font-size: 14px;
}

/* 弹窗底部 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
    max-height: none; /* 在平板上移除高度限制 */
  }

  .projects-section {
    order: 1;
  }

  .templates-section {
    order: 2;
    max-height: 60vh; /* 在平板上设置合适的高度 */
  }

  .templates-table :deep(.el-table) {
    max-height: 400px; /* 在平板上调整表格高度 */
  }
}

@media (max-width: 768px) {
  .config-management-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }

  .header-right {
    justify-content: flex-start;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .section-card :deep(.el-card__header) {
    padding: 16px 20px;
  }

  .section-card :deep(.el-card__body) {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .project-dialog,
  .template-dialog {
    width: 95% !important;
    margin: 2.5vh auto !important;
  }

  .project-dialog :deep(.el-dialog__header),
  .template-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }

  .project-form-container,
  .template-form-container {
    padding: 16px;
  }

  .section-content {
    padding: 16px;
  }

  .dialog-footer {
    padding: 16px 20px;
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
  }

  .placeholder-content h3 {
    font-size: 16px;
  }

  .placeholder-content p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .project-card {
    padding: 16px;
  }

  .project-header {
    margin-bottom: 12px;
  }

  .project-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .project-name {
    font-size: 16px;
  }

  .templates-table {
    font-size: 13px;
  }

  .templates-table :deep(.el-table) {
    max-height: 350px; /* 在小屏幕上减少高度 */
  }

  /* 在小屏幕上调整主内容区域 */
  .main-content {
    max-height: 90vh;
  }

  .templates-section {
    max-height: 70vh;
  }
}
</style>
