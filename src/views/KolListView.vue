<template>
  <div class="kol-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">红人列表</h1>
        <p class="page-description">管理和查看所有 KOL 红人信息</p>
      </div>
      <div class="header-right">
        <el-button @click="exportSearchResults">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
        <el-button @click="handleBatchEmail" :loading="batchEmailLoading" type="success">
          <el-icon><Message /></el-icon>
          邮件群发
        </el-button>
        <el-button type="primary" @click="handleCreateKol" :icon="Plus"> 新增KOL </el-button>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <div class="search-title">
          <el-icon><Search /></el-icon>
          <span>智能搜索</span>
          <!-- 搜索模式状态指示器 -->
          <div class="search-mode-indicator" v-if="useAdvancedSearch">
            <el-tag type="warning" size="small">
              <el-icon><Filter /></el-icon>
              高级搜索模式
            </el-tag>
          </div>
        </div>
        <div class="search-actions">
          <el-button type="text" @click="openAdvancedSearchDialog" class="advanced-toggle">
            <el-icon><Operation /></el-icon>
            高级搜索
          </el-button>
          <el-button type="text" @click="openSaveSearchDialog" class="save-search">
            <el-icon><Star /></el-icon>
            保存搜索
          </el-button>
        </div>
      </div>

      <!-- 快速搜索栏 -->
      <div class="quick-search-bar">
        <div class="quick-search-container">
          <!-- 平台搜索 -->
          <el-select
            v-model="searchForm.quickSearchPlatform"
            placeholder="Platform"
            clearable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><Monitor /></el-icon>
            </template>
            <el-option label="TikTok" value="TIKTOK" />
            <el-option label="Instagram" value="INSTAGRAM" />
            <el-option label="YouTube" value="YOUTUBE" />
          </el-select>

          <!-- 项目编码搜索 -->
          <el-select
            v-model="searchForm.quickSearchProjectCode"
            placeholder="Project Code"
            clearable
            filterable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><CollectionTag /></el-icon>
            </template>
            <el-option
              v-for="project in projectList"
              :key="project.code"
              :label="project.code"
              :value="project.code"
            >
              {{ project.code }}
            </el-option>
          </el-select>

          <!-- KOL ID搜索 -->
          <el-input
            v-model="searchForm.quickSearchSocialId"
            placeholder="KOL ID"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>

          <!-- 邮箱搜索 -->
          <el-input
            v-model="searchForm.quickSearchEmail"
            placeholder="Email"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            size="large"
            class="search-btn"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 搜索历史和模板 -->
      <div class="search-templates" v-if="searchTemplates.length > 0">
        <div class="templates-label">快速搜索模板：</div>
        <div class="templates-list">
          <el-tag
            v-for="template in searchTemplates"
            :key="template.id"
            class="template-tag"
            type="info"
            effect="plain"
            @click="applySearchTemplate(template)"
            closable
            @close="removeSearchTemplate(template.id)"
          >
            {{ template.name }}
          </el-tag>
        </div>
      </div>

      <!-- 已保存的搜索 -->
      <div class="saved-searches" v-if="savedSearches.length > 0">
        <div class="saved-searches-label">已保存的搜索：</div>
        <div class="saved-searches-list">
          <el-tag
            v-for="savedSearch in savedSearches"
            :key="savedSearch.id"
            class="saved-search-tag"
            type="success"
            effect="plain"
            @click="applySavedSearch(savedSearch)"
            closable
            @close="deleteSavedSearch(savedSearch.id)"
          >
            {{ savedSearch.name }}
            <span class="saved-search-time">{{
              formatSavedSearchTime(savedSearch.createdAt)
            }}</span>
          </el-tag>
        </div>
      </div>

      <!-- 当前搜索条件显示 -->
      <div class="current-filters" v-if="hasActiveFilters">
        <div class="filters-label">
          当前筛选条件：
          <el-tag
            v-if="hasAdvancedFilters"
            :type="searchForm.searchLogic === 'and' ? 'warning' : 'success'"
            size="small"
            effect="plain"
            class="logic-tag"
          >
            {{ searchForm.searchLogic === 'and' ? 'AND（同时满足）' : 'OR（满足任一）' }}
          </el-tag>
          <!-- 搜索结果统计 - 只在搜索完成且不在搜索状态时显示 -->
          <el-tag
            v-if="hasExecutedSearch && !isSearching && !loading"
            type="info"
            size="small"
            effect="plain"
            class="result-count-tag"
          >
            找到 {{ pagination.total }} 条结果
          </el-tag>

          <!-- 搜索进行中状态 -->
          <el-tag
            v-if="isSearching || (hasExecutedSearch && loading)"
            type="warning"
            size="small"
            effect="plain"
            class="searching-tag"
          >
            <el-icon class="is-loading"><Loading /></el-icon>
            正在搜索...
          </el-tag>
        </div>
        <div class="filters-list">
          <el-tag
            v-for="filter in activeFilters"
            :key="filter.key"
            closable
            @close="removeFilter(filter.key)"
            class="filter-tag"
          >
            {{ filter.label }}: {{ filter.value }}
          </el-tag>
          <el-button type="text" @click="clearAllFilters" class="clear-all">
            <el-icon><Delete /></el-icon>
            清空所有
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearchDialog"
      title="设置筛选条件"
      width="800px"
      class="advanced-search-dialog"
      :before-close="handleAdvancedSearchClose"
      append-to-body
    >
      <div class="advanced-search-content" v-loading="loading">
        <!-- 搜索模式提示 -->
        <div class="search-mode-tip" v-if="hasQuickSearchContent()">
          <el-alert title="搜索模式提示" type="info" :closable="false" show-icon>
            <template #default>
              <p>检测到您已填写快速搜索条件，执行高级搜索时将清空快速搜索字段。</p>
              <p>高级搜索支持更复杂的筛选条件组合，适合精确查找特定数据。</p>
            </template>
          </el-alert>
        </div>

        <!-- 全局逻辑设置 -->
        <div class="global-logic">
          <span class="logic-label">符合以下</span>
          <el-select v-model="searchForm.searchLogic" size="large" class="logic-select">
            <el-option label="所有" value="and" />
            <el-option label="任一" value="or" />
          </el-select>
          <span class="logic-label">条件</span>
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </div>

        <!-- 动态筛选条件 -->
        <div class="filter-conditions">
          <div
            v-for="(condition, index) in searchForm.conditions"
            :key="condition.id"
            class="condition-row"
          >
            <!-- 字段选择 -->
            <el-select
              v-model="condition.field"
              placeholder="选择字段"
              size="large"
              class="field-select"
              @change="handleFieldChange(index)"
            >
              <el-option
                v-for="field in availableFields"
                :key="field.key"
                :label="field.label"
                :value="field.key"
              />
            </el-select>

            <!-- 操作符选择 -->
            <el-select
              v-model="condition.operator"
              placeholder="选择操作符"
              size="large"
              class="operator-select"
              @change="handleOperatorChange(index)"
            >
              <el-option
                v-for="operator in getOperatorsForField(condition.field)"
                :key="operator.value"
                :label="operator.label"
                :value="operator.value"
              />
            </el-select>

            <!-- 值输入 -->
            <div class="value-input">
              <!-- 文本输入 -->
              <el-input
                v-if="
                  getFieldType(condition.field) === 'text' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="
                  getFieldType(condition.field) === 'number' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                :min="getFieldMin(condition.field)"
                :max="getFieldMax(condition.field)"
                :precision="getFieldPrecision(condition.field)"
                size="large"
                :controls="false"
                style="width: 100%"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="
                  getFieldType(condition.field) === 'date' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                type="date"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />

              <!-- 选择框 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'select' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in getFieldOptions(condition.field)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 布尔选择 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'boolean' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>

              <!-- 范围数字输入 -->
              <el-input-number
                v-else-if="
                  getFieldType(condition.field) === 'range' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                :min="getFieldMin(condition.field)"
                :max="getFieldMax(condition.field)"
                :precision="getFieldPrecision(condition.field)"
                size="large"
                :controls="false"
                style="width: 100%"
              />

              <!-- 数组输入 -->
              <el-input
                v-else-if="
                  (getFieldType(condition.field) === 'array' ||
                    getFieldType(condition.field) === 'fuzzy_array') &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 日期范围输入 -->
              <el-date-picker
                v-else-if="
                  getFieldType(condition.field) === 'date_range' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                type="date"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>

            <!-- 删除按钮 -->
            <el-button
              type="danger"
              :icon="Close"
              circle
              size="large"
              class="delete-btn"
              @click="removeCondition(index)"
            />
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div class="add-condition">
          <el-button type="primary" :icon="Plus" @click="addCondition" class="add-btn">
            添加条件
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="handleAdvancedSearchClose">取消</el-button>
          </div>
          <div class="footer-right">
            <el-button @click="clearAdvancedSearch">清空</el-button>
            <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-container">
        <el-table :data="kolList" v-loading="loading" stripe style="width: 100%">
          <!-- 动态生成表格列 -->
          <template v-for="column in visibleColumns" :key="column.key">
            <!-- 选择列 -->
            <el-table-column
              v-if="column.key === 'selection'"
              type="selection"
              :width="column.width"
            />

            <!-- 昵称列 -->
            <el-table-column
              v-else-if="column.key === 'social_id'"
              :fixed="column.fixed"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <div class="kol-info">
                  <el-tooltip
                    v-if="getPlatformUrl(row.platform, row.social_id)"
                    :content="getPlatformUrl(row.platform, row.social_id)"
                    placement="top"
                    :show-after="200"
                  >
                    <span
                      class="nick-name clickable"
                      @click="openPlatformUrl(row.platform, row.social_id)"
                    >
                      {{ row.social_id || '-' }}
                    </span>
                  </el-tooltip>
                  <span v-else class="nick-name">{{ row.social_id || '-' }}</span>
                  <div class="platform-badge">
                    <el-icon v-if="row.platform === 'TIKTOK'" color="#ff0050"
                      ><VideoPlay
                    /></el-icon>
                    <el-icon v-else-if="row.platform === 'INSTAGRAM'" color="#e4405f"
                      ><Picture
                    /></el-icon>
                    <el-icon v-else-if="row.platform === 'YOUTUBE'" color="#ff0000"
                      ><Monitor
                    /></el-icon>
                    <span>{{ getPlatformLabel(row.platform) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 分级列 -->
            <el-table-column
              v-else-if="column.key === 'tier'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tag :type="getTierType(row.tier)">{{ getTierLabel(row.tier) }}</el-tag>
              </template>
            </el-table-column>

            <!-- 粉丝数列 -->
            <el-table-column
              v-else-if="column.key === 'followers_count'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatNumber(row.followers_count) }}
              </template>
            </el-table-column>

            <!-- 互动率列 -->
            <el-table-column
              v-else-if="column.key === 'engagement_rate'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                <span
                  v-if="row.engagement_rate && parseFloat(row.engagement_rate) > 0"
                  :class="getEngagementClass(parseFloat(row.engagement_rate) * 100)"
                >
                  {{ (parseFloat(row.engagement_rate) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 平均观看数列 -->
            <el-table-column
              v-else-if="column.key === 'mean_views_k'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{
                  row.mean_views_k && parseFloat(row.mean_views_k) > 0
                    ? parseFloat(row.mean_views_k) + 'K'
                    : '-'
                }}
              </template>
            </el-table-column>

            <!-- 中位观看数列 -->
            <el-table-column
              v-else-if="column.key === 'median_views_k'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{
                  row.median_views_k && parseFloat(row.median_views_k) > 0
                    ? parseFloat(row.median_views_k) + 'K'
                    : '-'
                }}
              </template>
            </el-table-column>

            <!-- 点赞数列 -->
            <el-table-column
              v-else-if="column.key === 'likes_count'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatNumber(row.likes_count) }}
              </template>
            </el-table-column>

            <!-- 标签列 -->
            <el-table-column
              v-else-if="column.key === 'hashtags'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.hashtags && row.hashtags.length > 2"
                  :content="formatHashtags(row.hashtags)"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(tag, index) in row.hashtags.slice(0, 2)"
                      :key="`${row.id}-${index}-${tag}`"
                      class="custom-tag"
                    >
                      #{{ tag }}
                    </span>
                    <span class="more-tags">+{{ row.hashtags.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.hashtags && row.hashtags.length" class="tags-container">
                  <span
                    v-for="(tag, index) in row.hashtags"
                    :key="`${row.id}-${index}-${tag}`"
                    class="custom-tag"
                  >
                    #{{ tag }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 文案列 -->
            <el-table-column
              v-else-if="column.key === 'captions'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.captions && row.captions.length > 2"
                  :content="row.captions.join(', ')"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(caption, index) in row.captions.slice(0, 2)"
                      :key="`${row.id}-caption-${index}-${caption}`"
                      class="custom-tag"
                    >
                      {{ caption }}
                    </span>
                    <span class="more-tags">+{{ row.captions.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.captions && row.captions.length" class="tags-container">
                  <span
                    v-for="(caption, index) in row.captions"
                    :key="`${row.id}-caption-${index}-${caption}`"
                    class="custom-tag"
                  >
                    {{ caption }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 话题列 -->
            <el-table-column
              v-else-if="column.key === 'topics'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.topics && row.topics.length > 2"
                  :content="row.topics.join(', ')"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(topic, index) in row.topics.slice(0, 2)"
                      :key="`${row.id}-topic-${index}-${topic}`"
                      class="custom-tag"
                    >
                      {{ topic }}
                    </span>
                    <span class="more-tags">+{{ row.topics.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.topics && row.topics.length" class="tags-container">
                  <span
                    v-for="(topic, index) in row.topics"
                    :key="`${row.id}-topic-${index}-${topic}`"
                    class="custom-tag"
                  >
                    {{ topic }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 时间列格式化 -->
            <el-table-column
              v-else-if="column.key === 'created_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="column.key === 'updated_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatDate(row.updated_at) }}
              </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
              v-else-if="column.key === 'actions'"
              :fixed="column.fixed"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-button link type="primary" size="small" @click="handleView(row)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <el-button link type="danger" size="small" @click="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-table-column>

            <!-- 其他普通列 -->
            <el-table-column
              v-else
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :show-overflow-tooltip="column.showTooltip"
            >
              <template #default="{ row }">
                {{
                  column.prop &&
                  row[column.prop] !== null &&
                  row[column.prop] !== undefined &&
                  row[column.prop] !== 0 &&
                  row[column.prop] !== ''
                    ? row[column.prop]
                    : '-'
                }}
              </template>
            </el-table-column>
          </template>
          <!-- 空状态插槽 -->
          <template #empty>
            <div class="empty-state">
              <el-empty :image-size="120" :description="getEmptyStateDescription()">
                <template #image>
                  <el-icon size="120" color="#d1d5db">
                    <Search v-if="hasActiveFilters" />
                    <User v-else />
                  </el-icon>
                </template>
                <el-button v-if="hasActiveFilters" type="primary" @click="clearAllFilters">
                  清空搜索条件
                </el-button>
              </el-empty>
            </div>
          </template>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[100, 200, 500, 1000]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="500px">
      <div class="column-settings">
        <div class="setting-header">
          <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          <el-button size="small" @click="resetColumns">重置默认</el-button>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :move="checkMoveAllowed"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions',
              }"
            >
              <el-icon
                class="drag-handle"
                :class="{
                  disabled:
                    element.key === 'selection' ||
                    element.key === 'social_id' ||
                    element.key === 'actions',
                }"
              >
                <Rank />
              </el-icon>
              <el-checkbox
                v-model="element.visible"
                @change="saveColumnSettings"
                :disabled="
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'social_id'
                "
              >
                {{ element.label }}
              </el-checkbox>
              <span
                v-if="
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions'
                "
                class="fixed-label"
              >
                (固定)
              </span>
            </div>
          </template>
        </draggable>
      </div>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      class="export-dialog"
      append-to-body
    >
      <div class="export-content">
        <el-form :model="exportConfig" label-width="120px" class="export-form">
          <!-- 导出范围 -->
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio value="filtered">当前筛选结果</el-radio>
              <el-radio value="current">当前页面数据</el-radio>
              <el-radio value="all">全部数据</el-radio>
            </el-radio-group>
            <div class="form-hint">
              <span v-if="exportConfig.scope === 'filtered'"
                >将导出当前筛选条件下的所有 {{ getFilteredDataCount() }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'current'"
                >将导出当前页面的 {{ kolList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'all'"
                >将导出全部 {{ pagination.total }} 条数据（忽略筛选条件）</span
              >
              <span v-else>将导出选中的数据（功能开发中）</span>
            </div>
          </el-form-item>

          <!-- 导出格式 -->
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportConfig.format">
              <el-radio value="xlsx">Excel 文件 (.xlsx)</el-radio>
              <el-radio value="csv">CSV 文件 (.csv)</el-radio>
              <el-radio value="json">JSON 文件 (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件名设置 -->
          <el-form-item label="文件名">
            <el-input
              v-model="exportConfig.filename"
              placeholder="请输入文件名"
              maxlength="50"
              show-word-limit
            >
              <template #suffix>
                <span class="file-extension">.{{ exportConfig.format }}</span>
              </template>
            </el-input>
          </el-form-item>

          <!-- 其他选项 -->
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportConfig.includeHeaders">包含表头</el-checkbox>
          </el-form-item>

          <!-- 选择字段 -->
          <el-form-item label="导出字段" class="field-selection">
            <div class="field-selection-header">
              <span class="selection-info"
                >已选择 {{ exportConfig.fields.length }} / {{ exportableFields.length }} 项</span
              >
              <div class="selection-buttons">
                <el-button type="text" size="small" @click="selectAllFields">全选</el-button>
                <el-button type="text" size="small" @click="clearAllFields">全不选</el-button>
              </div>
            </div>
            <div class="field-list">
              <el-checkbox-group v-model="exportConfig.fields">
                <div class="field-grid">
                  <el-checkbox
                    v-for="field in exportableFields"
                    :key="field.key"
                    :value="field.key"
                    class="field-item"
                  >
                    {{ field.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :disabled="exportConfig.fields.length === 0"
          >
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- KOL 详情抽屉 -->
    <KolDetailDrawer
      v-model="showDetailDrawer"
      :kol-data="selectedKol"
      @edit="handleEditFromDrawer"
      @save="handleSaveFromDrawer"
    />

    <!-- 创建KOL对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增KOL"
      width="900px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :before-close="handleCreateDialogClose"
      class="edit-dialog"
      append-to-body
    >
      <div class="edit-form-container" v-loading="createLoading">
        <!-- 基本信息分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>基本信息</span>
            </div>
          </div>
          <el-form
            :model="createForm"
            :rules="createRules"
            ref="createFormRef"
            label-width="120px"
            class="edit-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="KOL ID" prop="social_id">
                  <el-input
                    v-model="createForm.social_id"
                    placeholder="请输入KOL ID"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KOL Name" prop="nick_name">
                  <el-input
                    v-model="createForm.nick_name"
                    placeholder="请输入KOL Name"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Platform" prop="platform">
                  <el-select
                    v-model="createForm.platform"
                    placeholder="请选择Platform"
                    style="width: 100%"
                    size="large"
                  >
                    <el-option label="TikTok" value="TIKTOK">
                      <div class="platform-option">
                        <el-icon color="#ff0050"><VideoPlay /></el-icon>
                        <span>TikTok</span>
                      </div>
                    </el-option>
                    <el-option label="Instagram" value="INSTAGRAM">
                      <div class="platform-option">
                        <el-icon color="#e4405f"><Picture /></el-icon>
                        <span>Instagram</span>
                      </div>
                    </el-option>
                    <el-option label="YouTube" value="YOUTUBE">
                      <div class="platform-option">
                        <el-icon color="#ff0000"><Monitor /></el-icon>
                        <span>YouTube</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Project Code" prop="project_code">
                  <el-select
                    v-model="createForm.project_code"
                    placeholder="请选择Project Code"
                    style="width: 100%"
                    size="large"
                  >
                    <el-option
                      v-for="project in projectList"
                      :key="project.code"
                      :label="project.code"
                      :value="project.code"
                    >
                      {{ project.code }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Source" prop="source">
                  <el-input v-model="createForm.source" placeholder="请输入Source" size="large" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Email" prop="email">
                  <el-input v-model="createForm.email" placeholder="请输入Email" size="large" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="Bio" prop="bio">
                  <el-input
                    v-model="createForm.bio"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入Bio"
                    maxlength="500"
                    show-word-limit
                    resize="none"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 数据指标分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <div class="section-title">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据指标</span>
            </div>
          </div>
          <el-form :model="createForm" label-width="120px" class="edit-form">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Followers" prop="followers_count">
                  <el-input-number
                    v-model="createForm.followers_count"
                    :min="0"
                    :max="999999999"
                    style="width: 100%"
                    placeholder="请输入Followers"
                    size="large"
                    :controls="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Likes" prop="likes_count">
                  <el-input-number
                    v-model="createForm.likes_count"
                    :min="0"
                    :max="999999999"
                    style="width: 100%"
                    placeholder="请输入Likes"
                    size="large"
                    :controls="false"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 内容标签分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <div class="section-title">
              <el-icon><CollectionTag /></el-icon>
              <span>内容标签</span>
            </div>
          </div>
          <el-form :model="createForm" label-width="120px" class="edit-form">
            <el-form-item label="Hashtags" prop="hashtags">
              <el-select
                v-model="createForm.hashtags"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择Hashtags"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新Hashtags后按回车添加，支持多选
              </div>
            </el-form-item>
            <el-form-item label="Topics" prop="topics">
              <el-select
                v-model="createForm.topics"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择Topics"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新Topics后按回车添加，支持多选
              </div>
            </el-form-item>
            <el-form-item label="Captions" prop="captions">
              <el-select
                v-model="createForm.captions"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择Captions"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新Captions后按回车添加，支持多选
              </div>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 备注分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              <span>其他信息</span>
            </div>
          </div>
          <el-form :model="createForm" label-width="120px" class="edit-form">
            <el-form-item label="Note" prop="note">
              <el-input
                v-model="createForm.note"
                type="textarea"
                :rows="4"
                placeholder="请输入Note"
                maxlength="1000"
                show-word-limit
                resize="none"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCreateCancel" :disabled="createLoading">取消</el-button>
          <el-button type="primary" @click="handleCreateSave" :loading="createLoading">
            创建KOL
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 保存搜索对话框 -->
    <el-dialog
      v-model="showSaveSearchDialog"
      title="保存搜索"
      width="400px"
      class="save-search-dialog"
    >
      <div class="save-search-content">
        <el-form>
          <el-form-item label="搜索名称" required>
            <el-input
              v-model="saveSearchName"
              placeholder="请输入搜索名称"
              clearable
              @keyup.enter="saveCurrentSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSaveSearchDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentSearch" :disabled="!saveSearchName.trim()">
            <el-icon><Star /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邮件群发对话框 -->
    <el-dialog
      v-model="showBatchEmailDialog"
      title="邮件群发"
      width="500px"
      class="batch-email-dialog"
    >
      <div class="batch-email-content">
        <el-form>
          <el-form-item label="邮件模板" required>
            <el-select
              v-model="selectedTemplate"
              placeholder="请选择邮件模板"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="template in emailTemplates"
                :key="template.code"
                :label="template.name"
                :value="template.code"
              >
                <div class="template-option">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-code">{{ template.code }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="email-info">
              <el-icon><InfoFilled /></el-icon>
              <span>将向 {{ kolList.length }} 个KOL发送邮件</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchEmailDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSendBatchEmail"
            :loading="batchEmailLoading"
            :disabled="!selectedTemplate"
          >
            <el-icon><Message /></el-icon>
            发送邮件
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormRules } from 'element-plus'
import {
  Plus,
  Search,
  VideoPlay,
  Picture,
  Monitor,
  Setting,
  Rank,
  View,
  Delete,
  Close,
  Operation,
  Star,
  Download,
  User,
  Message,
  DataAnalysis,
  CollectionTag,
  InfoFilled,
  Document,
  Filter,
  QuestionFilled,
  Loading,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  searchKols,
  deleteKol,
  createKol,
  type KolSearchParams,
  type KolItem,
} from '../services/kolApi'
import { fetchAllProjects } from '../services/projectApi'
import { sendBatchEmailsToKols, type KOLIdentifier } from '../services/emailServiceApi'
import { fetchEmailTemplatesByProject, type EmailTemplate } from '../services/emailTemplateApi'

// 类型定义

interface SearchCondition {
  id: string
  field: string
  operator: string
  value: string | number | boolean | null
}

interface FieldConfig {
  key: string
  label: string
  type: string
  apiField: string
  minField?: string
  maxField?: string
  afterField?: string
  beforeField?: string
}

interface SearchForm {
  // 快速搜索
  quickSearchPlatform: string
  quickSearchProjectCode: string
  quickSearchSocialId: string
  quickSearchEmail: string
  // 搜索逻辑设置
  searchLogic: 'and' | 'or'
  // 动态筛选条件
  conditions: SearchCondition[]
  // 基本信息
  nick_name: string
  social_id: string
  email: string
  // 平台信息
  platforms: string[]
  tiers: string[]
  // 保留向后兼容
  platform: string
  tier: string
  // 项目和来源
  source: string
  project_code: string
  crawler_task_id?: number
  // 数据指标范围
  followersMin?: number
  followersMax?: number
  engagementMin?: number
  engagementMax?: number
  meanViewsMin?: number
  meanViewsMax?: number
  medianViewsMin?: number
  medianViewsMax?: number
  // AI相关
  aiScoreMin?: number
  aiScoreMax?: number
  ai_matched?: boolean
  // 邮箱相关
  email_fetch_status: string
  has_email?: boolean
  has_bio_extracted_email?: boolean
  has_nano_extracted_email?: boolean

  // 时间范围
  createTimeRange?: [Date, Date] | null
  updateTimeRange?: [Date, Date] | null
}

interface SearchTemplate {
  id: string
  name: string
  conditions: Partial<SearchForm>
  createdAt: string
}

interface SavedSearch {
  id: string
  name: string
  searchForm: SearchForm
  createdAt: string
}

interface ActiveFilter {
  key: string
  label: string
  value: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
}

interface ExportConfig {
  fields: string[]
  format: 'xlsx' | 'csv' | 'json'
  scope: 'all' | 'current' | 'filtered' | 'selected'
  includeHeaders: boolean
  filename: string
}

interface ExportItem {
  [key: string]: string | number
}

// 响应式数据
const loading = ref(false)
const batchEmailLoading = ref(false)
const showBatchEmailDialog = ref(false)
const emailTemplates = ref<EmailTemplate[]>([])
const selectedTemplate = ref<string>('')
const kolList = ref<KolItem[]>([])
const showDetailDrawer = ref(false)
const selectedKol = ref<KolItem | null>(null)
const showColumnSettings = ref(false)

// 搜索相关数据
const showAdvancedSearchDialog = ref(false)
const showExportDialog = ref(false)
const searchTemplates = ref<SearchTemplate[]>([])
const useAdvancedSearch = ref(false)
const searchDebounceTimer = ref<number | null>(null)
const hasExecutedSearch = ref(false) // 跟踪是否已执行过搜索操作
const isSearching = ref(false) // 跟踪搜索进行状态
const isRemovingFilter = ref(false) // 跟踪是否正在删除筛选条件

// 保存搜索相关数据
const savedSearches = ref<SavedSearch[]>([])
const showSaveSearchDialog = ref(false)
const saveSearchName = ref('')
const SAVED_SEARCHES_KEY = 'kol-saved-searches'

// 导出相关数据
const exportConfig = reactive<ExportConfig>({
  fields: [],
  format: 'xlsx',
  scope: 'filtered',
  includeHeaders: true,
  filename: '',
})

// 字段配置 - 与列设置保持一致
const availableFields: FieldConfig[] = [
  // 基本信息字段 - 支持等于操作
  { key: 'social_id', label: 'KOL ID', type: 'text', apiField: 'social_id' },
  { key: 'nick_name', label: 'KOL Name', type: 'text', apiField: 'nick_name' },
  { key: 'email', label: 'Email', type: 'text', apiField: 'email' },
  { key: 'bio', label: 'Bio', type: 'text', apiField: 'bio' },
  { key: 'tier', label: 'Tier', type: 'select', apiField: 'tier' },
  { key: 'platform', label: 'Platform', type: 'select', apiField: 'platform' },
  { key: 'source', label: 'Source', type: 'text', apiField: 'source' },
  { key: 'project_code', label: 'Project Code', type: 'select', apiField: 'project_code' },
  { key: 'note', label: 'Note', type: 'text', apiField: 'note' },

  // 布尔字段 - 支持等于操作
  { key: 'has_email', label: 'Has Email', type: 'boolean', apiField: 'has_email' },

  // 数值范围字段 - 支持大于/小于操作
  {
    key: 'followers_count',
    label: 'Followers',
    type: 'range',
    apiField: 'followers_count',
    minField: 'min_followers',
    maxField: 'max_followers',
  },
  {
    key: 'likes_count',
    label: 'Likes',
    type: 'range',
    apiField: 'likes_count',
    minField: 'min_likes',
    maxField: 'max_likes',
  },
  {
    key: 'engagement_rate',
    label: 'Engagement Rate',
    type: 'range',
    apiField: 'engagement_rate',
    minField: 'min_engagement_rate',
    maxField: 'max_engagement_rate',
  },
  {
    key: 'mean_views_k',
    label: 'Mean Views',
    type: 'range',
    apiField: 'mean_views_k',
    minField: 'min_mean_views_k',
    maxField: 'max_mean_views_k',
  },
  {
    key: 'median_views_k',
    label: 'Median Views',
    type: 'range',
    apiField: 'median_views_k',
    minField: 'min_median_views_k',
    maxField: 'max_median_views_k',
  },

  // 数组字段 - 支持包含操作
  { key: 'hashtags', label: 'Hashtags', type: 'fuzzy_array', apiField: 'hashtags_fuzzy' },
  { key: 'captions', label: 'Captions', type: 'fuzzy_array', apiField: 'captions_fuzzy' },
  { key: 'topics', label: 'Topics', type: 'fuzzy_array', apiField: 'topics_fuzzy' },

  // 时间字段 - 支持大于/小于操作
  {
    key: 'created_at',
    label: 'Created At',
    type: 'date_range',
    apiField: 'created_at',
    afterField: 'created_after',
    beforeField: 'created_before',
  },
  {
    key: 'updated_at',
    label: 'Updated At',
    type: 'date_range',
    apiField: 'updated_at',
    afterField: 'updated_after',
    beforeField: 'updated_before',
  },
]

// 操作符配置 - 根据系统支持的操作符重新设计
const operators = {
  text: [{ value: 'equals', label: '等于' }],
  number: [{ value: 'equals', label: '等于' }],
  range: [
    { value: 'greater_than', label: '大于' },
    { value: 'less_than', label: '小于' },
  ],
  select: [{ value: 'equals', label: '等于' }],
  boolean: [{ value: 'equals', label: '等于' }],
  array: [{ value: 'equals', label: '等于' }],
  fuzzy_array: [{ value: 'contains', label: '包含' }],
  date_range: [
    { value: 'greater_than', label: '晚于' },
    { value: 'less_than', label: '早于' },
  ],
}

const searchForm = reactive<SearchForm>({
  quickSearchPlatform: '',
  quickSearchProjectCode: '',
  quickSearchSocialId: '',
  quickSearchEmail: '',
  searchLogic: 'and',
  conditions: [],
  nick_name: '',
  social_id: '',
  email: '',
  platforms: [],
  tiers: [],
  platform: '',
  tier: '',
  source: '',
  project_code: '',
  crawler_task_id: undefined,
  followersMin: undefined,
  followersMax: undefined,
  engagementMin: undefined,
  engagementMax: undefined,
  meanViewsMin: undefined,
  meanViewsMax: undefined,
  medianViewsMin: undefined,
  medianViewsMax: undefined,
  aiScoreMin: undefined,
  aiScoreMax: undefined,
  ai_matched: undefined,
  email_fetch_status: '',
  has_email: undefined,
  has_bio_extracted_email: undefined,
  has_nano_extracted_email: undefined,
  createTimeRange: null,
  updateTimeRange: null,
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 100,
  total: 0,
})

// 默认列配置 - 按照用户要求重新配置
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true },
  { key: 'social_id', prop: 'social_id', label: 'KOL ID', width: 150, visible: true, fixed: true },
  { key: 'nick_name', prop: 'nick_name', label: 'KOL Name', width: 150, visible: true },
  { key: 'email', prop: 'email', label: 'Email', width: 200, visible: true },
  { key: 'bio', prop: 'bio', label: 'Bio', width: 300, visible: true, showTooltip: true },
  { key: 'tier', prop: 'tier', label: 'Tier', width: 100, visible: true },
  {
    key: 'followers_count',
    prop: 'followers_count',
    label: 'Followers',
    width: 110,
    visible: true,
  },
  { key: 'likes_count', prop: 'likes_count', label: 'Likes', width: 110, visible: true },
  {
    key: 'engagement_rate',
    prop: 'engagement_rate',
    label: 'Engagement Rate',
    width: 170,
    visible: true,
  },
  { key: 'mean_views_k', prop: 'mean_views_k', label: 'Mean Views', width: 130, visible: true },
  {
    key: 'median_views_k',
    prop: 'median_views_k',
    label: 'Median Views',
    width: 140,
    visible: true,
  },
  { key: 'hashtags', label: 'Hashtags', width: 210, visible: true },
  { key: 'captions', label: 'Captions', width: 200, visible: true },
  { key: 'topics', label: 'Topics', width: 200, visible: true },
  { key: 'source', prop: 'source', label: 'Source', width: 120, visible: true },
  { key: 'project_code', prop: 'project_code', label: 'Project Code', width: 120, visible: true },
  { key: 'note', prop: 'note', label: 'Note', width: 150, visible: true },
  { key: 'created_at', prop: 'created_at', label: 'Created At', width: 150, visible: true },
  { key: 'updated_at', prop: 'updated_at', label: 'Updated At', width: 150, visible: true },
  { key: 'actions', label: 'Actions', width: 160, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// KOL 页面允许的字段白名单
const allowedKolFields = new Set([
  'selection',
  'social_id',
  'nick_name',
  'email',
  'bio',
  'tier',
  'followers_count',
  'likes_count',
  'engagement_rate',
  'mean_views_k',
  'median_views_k',
  'hashtags',
  'captions',
  'topics',
  'source',
  'project_code',
  'note',
  'created_at',
  'updated_at',
  'actions',
])

// 计算可见的列（添加字段白名单验证）
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => {
    // 只显示在白名单中的字段
    if (!allowedKolFields.has(column.key)) {
      console.warn(`检测到不属于 KOL 页面的字段: ${column.key}，已自动过滤`)
      return false
    }
    return column.visible
  })
})

// 本地存储键名
const COLUMN_SETTINGS_KEY = 'kol-list-column-settings'

// 移除模拟数据，使用真实API

// 条件映射到API参数的函数
const mapConditionToApiParams = async (
  condition: SearchCondition,
  field: FieldConfig,
  searchParams: KolSearchParams,
) => {
  const { operator, value } = condition
  const { type, apiField, minField, maxField, afterField, beforeField } = field

  switch (type) {
    case 'text':
    case 'number':
    case 'select':
      if (operator === 'equals') {
        ;(searchParams as Record<string, unknown>)[apiField] = value
      }
      break
    case 'boolean':
      if (operator === 'equals') {
        // 将字符串 'true'/'false' 转换为布尔值
        const boolValue = value === 'true' || value === true
        ;(searchParams as Record<string, unknown>)[apiField] = boolValue
      }
      break

    case 'range':
      if (operator === 'greater_than' && minField) {
        ;(searchParams as Record<string, unknown>)[minField] = value
      } else if (operator === 'less_than' && maxField) {
        ;(searchParams as Record<string, unknown>)[maxField] = value
      }
      break

    case 'array':
      if (operator === 'equals') {
        ;(searchParams as Record<string, unknown>)[apiField] = Array.isArray(value)
          ? value
          : [value]
      }
      break

    case 'fuzzy_array':
      if (operator === 'contains') {
        ;(searchParams as Record<string, unknown>)[apiField] = Array.isArray(value)
          ? value
          : [value]
      }
      break

    case 'date_range':
      if (operator === 'greater_than' && afterField) {
        ;(searchParams as Record<string, unknown>)[afterField] = value
      } else if (operator === 'less_than' && beforeField) {
        ;(searchParams as Record<string, unknown>)[beforeField] = value
      }
      break
  }
}

// 方法
// 构建搜索参数的辅助函数
const buildSearchParams = async (): Promise<KolSearchParams> => {
  const searchParams: KolSearchParams = {}

  if (useAdvancedSearch.value && searchForm.conditions.length > 0) {
    // 处理高级搜索的动态筛选条件
    for (const condition of searchForm.conditions) {
      if (
        !condition.field ||
        !condition.operator ||
        condition.value === null ||
        condition.value === ''
      ) {
        continue
      }

      const field = availableFields.find((f) => f.key === condition.field)
      if (!field) continue

      // 根据字段类型和操作符映射到API参数
      await mapConditionToApiParams(condition, field, searchParams)
    }
  } else {
    // 处理快捷搜索条件
    if (searchForm.quickSearchPlatform) {
      searchParams.platform = searchForm.quickSearchPlatform as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE'
    }
    if (searchForm.quickSearchProjectCode) {
      searchParams.project_code = searchForm.quickSearchProjectCode
    }
    if (searchForm.quickSearchSocialId) {
      searchParams.social_id = searchForm.quickSearchSocialId
    }
    if (searchForm.quickSearchEmail) {
      searchParams.email = searchForm.quickSearchEmail
    }
  }

  return searchParams
}

const loadData = async () => {
  loading.value = true
  try {
    // 计算分页参数
    const skip = (pagination.currentPage - 1) * pagination.pageSize

    // 构建搜索参数
    const searchParams = await buildSearchParams()

    // 统一调用搜索API
    const response = await searchKols(searchParams, { skip, limit: pagination.pageSize })
    kolList.value = response.items
    pagination.total = response.total

    console.log('搜索模式:', useAdvancedSearch.value ? '高级搜索' : '快速搜索')
    console.log('搜索参数:', searchParams)
    console.log('API响应数据:', kolList.value)
    console.log('搜索完成时的状态:', {
      isSearching: isSearching.value,
      hasExecutedSearch: hasExecutedSearch.value,
      total: response.total,
    })

    // 只有在搜索状态下才标记为已执行搜索
    if (isSearching.value) {
      hasExecutedSearch.value = true
    }
  } catch (error) {
    console.error('加载KOL数据失败:', error)
    if (error instanceof Error) {
      console.error('错误详情:', error.message) // 详细错误信息
    }

    // 检查是否是后端不可用的错误
    if (
      error instanceof Error &&
      (error.message.includes('Network Error') ||
        error.message.includes('ERR_NETWORK') ||
        error.message === 'BACKEND_UNAVAILABLE')
    ) {
      console.warn('后端服务不可用，使用空数据继续渲染页面')
      // 静默处理，不显示错误消息，让用户知道页面可以正常使用
    } else {
      ElMessage.error('加载数据失败')
    }

    // 设置空数据，确保页面仍然可以正常显示
    kolList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
    // 重置搜索进行状态
    isSearching.value = false
  }
}

// 防抖搜索函数
const debouncedSearch = (immediate = false) => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }

  if (immediate) {
    executeSearch()
  } else {
    searchDebounceTimer.value = window.setTimeout(() => {
      executeSearch()
    }, 300) // 300ms 防抖延迟
  }
}

// 执行搜索的核心函数
const executeSearch = () => {
  pagination.currentPage = 1

  // 检查是否有高级搜索内容，如果有则提示用户
  const hadAdvancedSearchContent = hasAdvancedSearchContent()

  // 清空所有高级搜索条件，确保只有快速搜索生效
  clearAdvancedSearchConditions()

  // 切换到快速搜索模式
  useAdvancedSearch.value = false

  // 提供用户反馈
  if (hadAdvancedSearchContent) {
    ElMessage.info('已切换到快速搜索模式，高级搜索条件已清除')
  }

  // 只有在还没有设置搜索状态时才设置（避免重复设置）
  if (!isSearching.value) {
    isSearching.value = true
  }

  loadData()
}

const handleSearch = () => {
  debouncedSearch(true) // 立即执行搜索
}

// 专门用于删除筛选条件后的搜索
const handleSearchAfterFilterRemoval = () => {
  // 立即设置搜索状态，确保UI立即响应
  isSearching.value = true
  // 立即执行搜索，不使用防抖
  executeSearch()
}

// 清空高级搜索条件的辅助函数
const clearAdvancedSearchConditions = () => {
  searchForm.conditions = []
  searchForm.searchLogic = 'and'
}

// 调试辅助函数 - 获取当前搜索状态
const getSearchStateDebugInfo = () => {
  return {
    searchMode: useAdvancedSearch.value ? 'advanced' : 'quick',
    hasExecutedSearch: hasExecutedSearch.value,
    isSearching: isSearching.value,
    isRemovingFilter: isRemovingFilter.value,
    loading: loading.value,
    quickSearchFields: {
      platform: searchForm.quickSearchPlatform,
      projectCode: searchForm.quickSearchProjectCode,
      socialId: searchForm.quickSearchSocialId,
      email: searchForm.quickSearchEmail,
    },
    advancedConditionsCount: searchForm.conditions.length,
    activeFiltersCount: activeFilters.value.length,
    totalResults: pagination.total,
  }
}

// 检查是否有快速搜索内容的辅助函数
const hasQuickSearchContent = (): boolean => {
  return !!(
    searchForm.quickSearchPlatform ||
    searchForm.quickSearchProjectCode ||
    searchForm.quickSearchSocialId ||
    searchForm.quickSearchEmail
  )
}

// 检查是否有高级搜索内容的辅助函数
const hasAdvancedSearchContent = (): boolean => {
  return searchForm.conditions.length > 0
}

// 动态筛选条件相关方法
const addCondition = () => {
  const newCondition: SearchCondition = {
    id: Date.now().toString(),
    field: 'social_id', // 默认选择KOL ID字段
    operator: 'equals', // 默认选择等于操作符
    value: null,
  }
  searchForm.conditions.push(newCondition)
}

const removeCondition = (index: number) => {
  searchForm.conditions.splice(index, 1)
}

const handleFieldChange = (index: number) => {
  const condition = searchForm.conditions[index]
  condition.operator = ''
  condition.value = null
}

const handleOperatorChange = (index: number) => {
  const condition = searchForm.conditions[index]
  if (isNoValueOperator(condition.operator)) {
    condition.value = null
  }
}

const getFieldType = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  return field?.type || 'text'
}

const getOperatorsForField = (fieldKey: string) => {
  const fieldType = getFieldType(fieldKey)
  return operators[fieldType as keyof typeof operators] || operators.text
}

const isNoValueOperator = (operator: string): boolean => {
  return ['is_empty', 'is_not_empty'].includes(operator)
}

const getFieldPlaceholder = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  if (!field) return '请输入'

  // 为 select 类型字段提供更合适的占位符
  if (field.type === 'select') {
    return `请选择${field.label}`
  }

  return `请输入${field.label}`
}

const getFieldMin = (fieldKey: string): number => {
  if (fieldKey === 'engagement_rate' || fieldKey === 'ai_score') {
    return 0
  }
  return 0
}

const getFieldMax = (fieldKey: string): number | undefined => {
  if (fieldKey === 'engagement_rate' || fieldKey === 'ai_score') {
    return 100
  }
  return undefined
}

const getFieldPrecision = (fieldKey: string): number => {
  if (fieldKey === 'engagement_rate') {
    return 2
  }
  return 0
}

const getFieldOptions = (fieldKey: string) => {
  switch (fieldKey) {
    case 'platform':
      return [
        { label: 'TikTok', value: 'TIKTOK' },
        { label: 'Instagram', value: 'INSTAGRAM' },
        { label: 'YouTube', value: 'YOUTUBE' },
      ]
    case 'tier':
      return [
        { label: 'NANO', value: 'NANO' },
        { label: 'MICRO', value: 'MICRO' },
        { label: 'MID', value: 'MID' },
        { label: 'MACRO', value: 'MACRO' },
        { label: 'MEGA', value: 'MEGA' },
      ]
    case 'email_fetch_status':
      return [
        { label: '待处理', value: 'PENDING' },
        { label: '已解析', value: 'BIO_PARSED' },
        { label: 'AI评分', value: 'AI_SCORED' },
        { label: '已获取', value: 'NANO_FETCHED' },
        { label: '已完成', value: 'COMPLETED' },
      ]
    case 'has_email':
      return [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' },
      ]
    case 'project_code':
      return projectList.value.map((project) => ({
        label: project.code,
        value: project.code,
      }))
    default:
      return []
  }
}

const clearAdvancedSearch = () => {
  searchForm.conditions = []
  searchForm.searchLogic = 'and'
}

// 高级搜索相关方法
const handleAdvancedSearch = () => {
  // 验证搜索条件
  if (!validateSearchConditions()) {
    return
  }

  showAdvancedSearchDialog.value = false
  pagination.currentPage = 1

  // 检查是否有快速搜索内容，如果有则提示用户
  const hadQuickSearchContent = hasQuickSearchContent()

  // 清空所有快速搜索字段，确保只有高级搜索生效
  clearQuickSearchFields()

  // 标记已经执行了高级搜索
  useAdvancedSearch.value = true

  // 开始搜索状态，但不立即标记为已执行搜索
  isSearching.value = true

  // 提供用户反馈
  if (hadQuickSearchContent) {
    ElMessage.success(`已切换到高级搜索模式，快速搜索条件已清除`)
  } else {
    ElMessage.success(`已应用${getSearchSummary()}`)
  }

  loadData()
}

// 清空快速搜索字段的辅助函数
const clearQuickSearchFields = () => {
  searchForm.quickSearchPlatform = ''
  searchForm.quickSearchProjectCode = ''
  searchForm.quickSearchSocialId = ''
  searchForm.quickSearchEmail = ''
}

// 搜索状态管理辅助函数
const getSearchSummary = () => {
  if (useAdvancedSearch.value) {
    const conditionCount = searchForm.conditions.filter(
      (c) => c.field && c.operator && c.value !== null && c.value !== '',
    ).length
    return `高级搜索 (${conditionCount} 个条件)`
  } else {
    const quickFields = [
      searchForm.quickSearchPlatform,
      searchForm.quickSearchProjectCode,
      searchForm.quickSearchSocialId,
      searchForm.quickSearchEmail,
    ].filter(Boolean)
    return quickFields.length > 0 ? `快速搜索 (${quickFields.length} 个条件)` : '无搜索条件'
  }
}

// 验证搜索条件是否有效
const validateSearchConditions = (): boolean => {
  if (useAdvancedSearch.value) {
    const validConditions = searchForm.conditions.filter(
      (c) => c.field && c.operator && c.value !== null && c.value !== '',
    )
    if (validConditions.length === 0) {
      ElMessage.warning('请至少设置一个有效的高级搜索条件')
      return false
    }
  }
  return true
}

// 获取空状态描述
const getEmptyStateDescription = (): string => {
  if (loading.value) {
    return '正在加载数据...'
  }
  if (hasExecutedSearch.value && hasActiveFilters.value) {
    return '没有找到符合条件的KOL数据，请尝试调整搜索条件'
  }
  if (hasExecutedSearch.value && !hasActiveFilters.value) {
    return '搜索结果为空，请尝试其他搜索条件'
  }
  return '暂无KOL数据，请联系管理员添加数据'
}

const handleAdvancedSearchClose = () => {
  showAdvancedSearchDialog.value = false
  // 如果取消高级搜索，清除未执行的搜索条件
  if (!useAdvancedSearch.value) {
    searchForm.conditions = []
  }
}

// 打开高级搜索弹窗
const openAdvancedSearchDialog = () => {
  showAdvancedSearchDialog.value = true
}

// 移除本地过滤函数，现在使用API进行过滤

// 获取筛选后的数据数量
const getFilteredDataCount = () => {
  // 由于现在使用真实API，这里返回当前总数
  return pagination.total
}

const applySearchTemplate = (template: SearchTemplate) => {
  Object.assign(searchForm, template.conditions)
  handleAdvancedSearch()
}

const removeSearchTemplate = (templateId: string) => {
  const index = searchTemplates.value.findIndex((t) => t.id === templateId)
  if (index > -1) {
    searchTemplates.value.splice(index, 1)
  }
}

const exportSearchResults = () => {
  // 自动生成文件名
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-')
  exportConfig.filename = `KOL数据_${timestamp}`

  // 默认选择所有可见列
  exportConfig.fields = visibleColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)

  showExportDialog.value = true
}

const handleExport = () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请至少选择一个字段进行导出')
    return
  }

  try {
    // 获取要导出的数据
    const dataToExport = getExportData()

    // 根据格式导出
    switch (exportConfig.format) {
      case 'xlsx':
        exportToExcel(dataToExport)
        break
      case 'csv':
        exportToCSV(dataToExport)
        break
      case 'json':
        exportToJSON(dataToExport)
        break
    }

    showExportDialog.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getExportData = () => {
  let sourceData: KolItem[] = []

  // 根据导出范围获取数据
  switch (exportConfig.scope) {
    case 'all':
      // 由于使用真实API，暂时使用当前页数据
      sourceData = kolList.value
      break
    case 'current':
      // 当前页面显示的数据
      sourceData = kolList.value
      break
    case 'filtered':
      // 当前筛选条件下的数据
      sourceData = kolList.value
      break
    case 'selected':
      // 这里需要实现表格选择功能，暂时用当前页数据
      sourceData = kolList.value
      break
  }

  // 根据选择的字段过滤数据
  const filteredData = sourceData.map((item) => {
    const exportItem: ExportItem = {}
    exportConfig.fields.forEach((field) => {
      const column = tableColumns.value.find((col) => col.key === field)
      const label = column?.label || field

      switch (field) {
        case 'social_id':
          exportItem[label] = item.social_id
          break
        case 'nick_name':
          exportItem[label] = item.nick_name
          break
        case 'platform':
          exportItem[label] = getPlatformLabel(item.platform)
          break
        case 'tier':
          exportItem[label] = getTierLabel(item.tier)
          break
        case 'followers_count':
          exportItem[label] = item.followers_count || 0
          break
        case 'engagement_rate':
          exportItem[label] = item.engagement_rate
            ? (item.engagement_rate * 100).toFixed(2) + '%'
            : '-'
          break
        case 'mean_views_k':
          exportItem[label] = item.mean_views_k ? item.mean_views_k + 'K' : '-'
          break
        case 'email':
          exportItem[label] = item.email || ''
          break
        case 'bio':
          exportItem[label] = item.bio || ''
          break
        case 'hashtags':
          exportItem[label] = item.hashtags?.join(', ') || ''
          break
        case 'topics':
          exportItem[label] = item.topics?.join(', ') || ''
          break
        case 'note':
          exportItem[label] = item.note || ''
          break
        case 'created_at':
          exportItem[label] = formatDate(item.created_at)
          break
        case 'updated_at':
          exportItem[label] = formatDate(item.updated_at)
          break
        default:
          exportItem[label] = (item as unknown as Record<string, unknown>)[field]?.toString() || ''
      }
    })
    return exportItem
  })

  return filteredData
}

const exportToExcel = (data: ExportItem[]) => {
  // 这里可以使用 xlsx 库来导出 Excel
  // 为了演示，我们先用 CSV 的方式
  exportToCSV(data)
}

const exportToCSV = (data: ExportItem[]) => {
  if (data.length === 0) return

  const headers = Object.keys(data[0])
  let csvContent = ''

  // 添加表头
  if (exportConfig.includeHeaders) {
    csvContent += headers.join(',') + '\n'
  }

  // 添加数据行
  data.forEach((row) => {
    const values = headers.map((header) => {
      const value = row[header]
      // 处理包含逗号的值，用引号包围
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    })
    csvContent += values.join(',') + '\n'
  })

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const exportToJSON = (data: ExportItem[]) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.json`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 字段选择方法
const selectAllFields = () => {
  exportConfig.fields = tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)
}

const clearAllFields = () => {
  exportConfig.fields = []
}

// 可导出的字段选项
const exportableFields = computed(() => {
  return tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => ({
      key: col.key,
      label: col.label,
      checked: exportConfig.fields.includes(col.key),
    }))
})

// 计算属性 - 活跃的筛选条件
const hasActiveFilters = computed(() => {
  return activeFilters.value.length > 0
})

// 计算属性 - 是否有高级筛选条件（除了快速搜索）
const hasAdvancedFilters = computed(() => {
  return activeFilters.value.some(
    (filter) =>
      filter.key !== 'quickSearchPlatform' &&
      filter.key !== 'quickSearchProjectCode' &&
      filter.key !== 'quickSearchSocialId' &&
      filter.key !== 'quickSearchEmail',
  )
})

const activeFilters = computed(() => {
  const filters: ActiveFilter[] = []

  if (searchForm.quickSearchPlatform) {
    filters.push({
      key: 'quickSearchPlatform',
      label: 'Platform',
      value: searchForm.quickSearchPlatform,
    })
  }
  if (searchForm.quickSearchProjectCode) {
    filters.push({
      key: 'quickSearchProjectCode',
      label: 'Project Code',
      value: searchForm.quickSearchProjectCode,
    })
  }
  if (searchForm.quickSearchSocialId) {
    filters.push({
      key: 'quickSearchSocialId',
      label: 'KOL ID',
      value: searchForm.quickSearchSocialId,
    })
  }
  if (searchForm.quickSearchEmail) {
    filters.push({ key: 'quickSearchEmail', label: 'Email', value: searchForm.quickSearchEmail })
  }

  // 添加动态筛选条件 - 只有在实际执行了高级搜索后才显示
  if (useAdvancedSearch.value) {
    searchForm.conditions.forEach((condition, index) => {
      if (condition.field && condition.operator) {
        const field = availableFields.find((f) => f.key === condition.field)
        const fieldLabel = field ? field.label : condition.field

        let operatorLabel = ''
        const operators = getOperatorsForField(condition.field)
        const operator = operators.find((op) => op.value === condition.operator)
        operatorLabel = operator ? operator.label : condition.operator

        let value = ''
        if (isNoValueOperator(condition.operator)) {
          value = operatorLabel
        } else if (condition.value !== null && condition.value !== '') {
          if (condition.field === 'platform') {
            const platformOptions = getFieldOptions('platform')
            const option = platformOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'tier') {
            const tierOptions = getFieldOptions('tier')
            const option = tierOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'email_fetch_status') {
            const statusOptions = getFieldOptions('email_fetch_status')
            const option = statusOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'project_code') {
            const projectOptions = getFieldOptions('project_code')
            const option = projectOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (
            condition.field === 'ai_matched' ||
            condition.field === 'has_email' ||
            condition.field === 'has_bio_extracted_email' ||
            condition.field === 'has_nano_extracted_email'
          ) {
            value = condition.value === true || condition.value === 'true' ? '是' : '否'
          } else {
            value = String(condition.value)
          }
        } else {
          value = operatorLabel
        }

        filters.push({
          key: `condition_${index}`,
          label: `${fieldLabel} ${operatorLabel}`,
          value: value,
        })
      }
    })
  }

  if (searchForm.nick_name) {
    filters.push({ key: 'nick_name', label: 'KOL Name', value: searchForm.nick_name })
  }
  if (searchForm.social_id) {
    filters.push({ key: 'social_id', label: 'KOL ID', value: searchForm.social_id })
  }
  if (searchForm.email) {
    filters.push({ key: 'email', label: 'Email', value: searchForm.email })
  }
  if (searchForm.source) {
    filters.push({ key: 'source', label: 'Source', value: searchForm.source })
  }
  if (searchForm.project_code) {
    filters.push({ key: 'project_code', label: 'Project Code', value: searchForm.project_code })
  }
  if (searchForm.crawler_task_id !== undefined) {
    filters.push({
      key: 'crawler_task_id',
      label: '爬虫任务ID',
      value: searchForm.crawler_task_id.toString(),
    })
  }
  if (searchForm.platforms.length > 0) {
    filters.push({ key: 'platforms', label: 'Platform', value: searchForm.platforms.join(', ') })
  }
  if (searchForm.tiers.length > 0) {
    filters.push({ key: 'tiers', label: 'Tier', value: searchForm.tiers.join(', ') })
  }

  // 数据指标范围筛选条件
  if (searchForm.followersMin !== undefined || searchForm.followersMax !== undefined) {
    const min = searchForm.followersMin || 0
    const max = searchForm.followersMax || '∞'
    filters.push({ key: 'followersRange', label: 'Followers', value: `${min} - ${max}` })
  }
  if (searchForm.engagementMin !== undefined || searchForm.engagementMax !== undefined) {
    const min = searchForm.engagementMin || 0
    const max = searchForm.engagementMax || '∞'
    filters.push({ key: 'engagementRange', label: '互动率', value: `${min}% - ${max}%` })
  }
  if (searchForm.meanViewsMin !== undefined || searchForm.meanViewsMax !== undefined) {
    const min = searchForm.meanViewsMin || 0
    const max = searchForm.meanViewsMax || '∞'
    filters.push({ key: 'meanViewsRange', label: '平均观看数', value: `${min}K - ${max}K` })
  }
  if (searchForm.medianViewsMin !== undefined || searchForm.medianViewsMax !== undefined) {
    const min = searchForm.medianViewsMin || 0
    const max = searchForm.medianViewsMax || '∞'
    filters.push({ key: 'medianViewsRange', label: '中位观看数', value: `${min}K - ${max}K` })
  }

  // AI相关筛选条件
  if (searchForm.aiScoreMin !== undefined || searchForm.aiScoreMax !== undefined) {
    const min = searchForm.aiScoreMin || 0
    const max = searchForm.aiScoreMax || '∞'
    filters.push({ key: 'aiScoreRange', label: 'AI分数', value: `${min} - ${max}` })
  }
  if (searchForm.ai_matched !== undefined) {
    filters.push({
      key: 'ai_matched',
      label: 'AI匹配状态',
      value: searchForm.ai_matched ? '已匹配' : '未匹配',
    })
  }

  // 邮箱相关筛选条件
  if (searchForm.email_fetch_status) {
    filters.push({
      key: 'email_fetch_status',
      label: '邮箱获取状态',
      value: searchForm.email_fetch_status,
    })
  }
  if (searchForm.has_email !== undefined) {
    filters.push({ key: 'has_email', label: '有邮箱', value: searchForm.has_email ? '是' : '否' })
  }
  if (searchForm.has_bio_extracted_email !== undefined) {
    filters.push({
      key: 'has_bio_extracted_email',
      label: 'Bio提取邮箱',
      value: searchForm.has_bio_extracted_email ? '是' : '否',
    })
  }
  if (searchForm.has_nano_extracted_email !== undefined) {
    filters.push({
      key: 'has_nano_extracted_email',
      label: 'Nano提取邮箱',
      value: searchForm.has_nano_extracted_email ? '是' : '否',
    })
  }

  // 时间范围筛选条件
  if (searchForm.createTimeRange && searchForm.createTimeRange.length === 2) {
    const [start, end] = searchForm.createTimeRange
    const startDate = start.toLocaleDateString('zh-CN')
    const endDate = end.toLocaleDateString('zh-CN')
    filters.push({ key: 'createTimeRange', label: '创建时间', value: `${startDate} 至 ${endDate}` })
  }
  if (searchForm.updateTimeRange && searchForm.updateTimeRange.length === 2) {
    const [start, end] = searchForm.updateTimeRange
    const startDate = start.toLocaleDateString('zh-CN')
    const endDate = end.toLocaleDateString('zh-CN')
    filters.push({ key: 'updateTimeRange', label: '更新时间', value: `${startDate} 至 ${endDate}` })
  }

  return filters
})

const removeFilter = (filterKey: string) => {
  // 处理动态筛选条件（高级搜索条件）
  if (filterKey.startsWith('condition_')) {
    const index = parseInt(filterKey.replace('condition_', ''))
    if (!isNaN(index) && index >= 0 && index < searchForm.conditions.length) {
      searchForm.conditions.splice(index, 1)
      handleAdvancedSearch()
      return
    }
  }

  // 处理快速搜索条件
  const quickSearchKeys = [
    'quickSearchPlatform',
    'quickSearchProjectCode',
    'quickSearchSocialId',
    'quickSearchEmail',
  ]
  if (quickSearchKeys.includes(filterKey)) {
    // 设置删除标志，防止 watcher 触发
    isRemovingFilter.value = true

    let removedLabel = ''
    switch (filterKey) {
      case 'quickSearchPlatform':
        removedLabel = 'Platform'
        searchForm.quickSearchPlatform = ''
        break
      case 'quickSearchProjectCode':
        removedLabel = 'Project Code'
        searchForm.quickSearchProjectCode = ''
        break
      case 'quickSearchSocialId':
        removedLabel = 'KOL ID'
        searchForm.quickSearchSocialId = ''
        break
      case 'quickSearchEmail':
        removedLabel = 'Email'
        searchForm.quickSearchEmail = ''
        break
    }

    console.log(`删除快速搜索条件: ${removedLabel}`)
    console.log('删除后的搜索状态:', getSearchStateDebugInfo())

    // 删除快速搜索条件后，使用专门的搜索函数确保状态正确
    handleSearchAfterFilterRemoval()

    // 重置删除标志
    setTimeout(() => {
      isRemovingFilter.value = false
    }, 100)

    return
  }

  // 处理其他高级搜索字段
  switch (filterKey) {
    case 'nick_name':
      searchForm.nick_name = ''
      break
    case 'social_id':
      searchForm.social_id = ''
      break
    case 'email':
      searchForm.email = ''
      break
    case 'source':
      searchForm.source = ''
      break
    case 'project_code':
      searchForm.project_code = ''
      break
    case 'crawler_task_id':
      searchForm.crawler_task_id = undefined
      break
    case 'platforms':
      searchForm.platforms = []
      break
    case 'tiers':
      searchForm.tiers = []
      break
    case 'followersRange':
      searchForm.followersMin = undefined
      searchForm.followersMax = undefined
      break
    case 'engagementRange':
      searchForm.engagementMin = undefined
      searchForm.engagementMax = undefined
      break
    case 'meanViewsRange':
      searchForm.meanViewsMin = undefined
      searchForm.meanViewsMax = undefined
      break
    case 'medianViewsRange':
      searchForm.medianViewsMin = undefined
      searchForm.medianViewsMax = undefined
      break
    case 'aiScoreRange':
      searchForm.aiScoreMin = undefined
      searchForm.aiScoreMax = undefined
      break
    case 'ai_matched':
      searchForm.ai_matched = undefined
      break
    case 'email_fetch_status':
      searchForm.email_fetch_status = ''
      break
    case 'has_email':
      searchForm.has_email = undefined
      break
    case 'has_bio_extracted_email':
      searchForm.has_bio_extracted_email = undefined
      break
    case 'has_nano_extracted_email':
      searchForm.has_nano_extracted_email = undefined
      break

    case 'createTimeRange':
      searchForm.createTimeRange = null
      break
    case 'updateTimeRange':
      searchForm.updateTimeRange = null
      break
  }
  handleAdvancedSearch()
}

const clearAllFilters = () => {
  // 清除动态筛选条件
  searchForm.conditions = []

  // 清空快速搜索字段
  clearQuickSearchFields()

  // 重置搜索模式
  useAdvancedSearch.value = false

  // 重置搜索执行状态
  hasExecutedSearch.value = false
  isSearching.value = false
  isRemovingFilter.value = false

  // 重置分页并加载初始数据
  pagination.currentPage = 1
  loadData()

  ElMessage.success('已清空所有搜索条件')
}

const handleView = (row: KolItem) => {
  selectedKol.value = row
  showDetailDrawer.value = true
}

const handleEditFromDrawer = (kol: KolItem) => {
  // 可以在这里处理编辑事件的额外逻辑
  console.log('开始编辑 KOL:', kol.nick_name)
}

const handleSaveFromDrawer = (updatedKol: KolItem) => {
  // 在列表中更新对应的数据
  const index = kolList.value.findIndex((item) => item.id === updatedKol.id)
  if (index !== -1) {
    kolList.value[index] = { ...updatedKol }
    selectedKol.value = { ...updatedKol }
  }
  ElMessage.success(`${updatedKol.nick_name} 的信息已更新`)
}

const handleDelete = async (row: KolItem) => {
  try {
    await ElMessageBox.confirm(`确定要删除红人 "${row.nick_name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用删除API
    await deleteKol(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除KOL失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
}

const checkMoveAllowed = (evt: {
  draggedContext: { element: TableColumn }
  relatedContext?: { element: TableColumn }
}) => {
  const { draggedContext, relatedContext } = evt

  // 固定列不能移动
  const fixedColumns = ['selection', 'social_id', 'actions']

  // 如果拖拽的是固定列，不允许移动
  if (fixedColumns.includes(draggedContext.element.key)) {
    return false
  }

  // 如果目标位置是固定列的位置，也不允许移动
  if (relatedContext && fixedColumns.includes(relatedContext.element.key)) {
    return false
  }

  return true
}

// 工具方法
const formatNumber = (num: number | null | undefined) => {
  if (num === null || num === undefined || num === 0) {
    return '-'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '-'
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const getPlatformLabel = (platform: string) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
    TIKTOK: 'TikTok',
    INSTAGRAM: 'Instagram',
    YOUTUBE: 'YouTube',
  }
  return labels[platform] || platform
}

const getTierLabel = (tier: string) => {
  if (!tier) return '-'
  const labels: Record<string, string> = {
    NANO: 'NANO',
    MICRO: 'MICRO',
    MID: 'MID',
    MACRO: 'MACRO',
    MEGA: 'MEGA',
  }
  return labels[tier] || tier
}

const getTierType = (tier: string) => {
  if (!tier) return 'info'
  const types: Record<string, string> = {
    NANO: 'info',
    MICRO: 'warning',
    MID: 'success',
    MACRO: 'danger',
    MEGA: 'danger',
  }
  return types[tier] || 'info'
}

const getEngagementClass = (rate: number) => {
  if (rate >= 10) return 'engagement-excellent'
  if (rate >= 5) return 'engagement-good'
  if (rate >= 2) return 'engagement-normal'
  return 'engagement-low'
}

const formatHashtags = (hashtags: string[]) => {
  if (!hashtags || hashtags.length === 0) return '-'
  return hashtags.map((tag) => '#' + tag).join(', ')
}

const getPlatformUrl = (platform: string, socialId: string) => {
  if (!platform || !socialId) return null
  switch (platform?.toUpperCase()) {
    case 'TIKTOK':
      return `https://www.tiktok.com/@${socialId}`
    case 'INSTAGRAM':
      return `https://www.instagram.com/${socialId}`
    case 'YOUTUBE':
      return null // 暂时不处理
    default:
      return null
  }
}

const openPlatformUrl = (platform: string, socialId: string) => {
  const url = getPlatformUrl(platform, socialId)
  if (url) {
    window.open(url, '_blank')
  }
}

// 保存搜索相关方法
const loadSavedSearches = () => {
  try {
    const saved = localStorage.getItem(SAVED_SEARCHES_KEY)
    if (saved) {
      savedSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载保存的搜索失败:', error)
  }
}

const saveCurrentSearch = () => {
  if (!saveSearchName.value.trim()) {
    ElMessage.warning('请输入搜索名称')
    return
  }

  // 检查是否已存在同名搜索
  const existingIndex = savedSearches.value.findIndex(
    (search) => search.name === saveSearchName.value.trim(),
  )

  // 准备要保存的搜索表单数据
  const searchFormToSave = JSON.parse(JSON.stringify(searchForm)) // 深拷贝

  // 如果当前使用的是快捷搜索，需要将快捷搜索条件转换为高级搜索条件
  if (!useAdvancedSearch.value && (searchForm.quickSearchSocialId || searchForm.quickSearchEmail)) {
    // 清空高级搜索条件，避免重复
    searchFormToSave.conditions = []

    // 将快捷搜索条件转换为高级搜索条件
    if (searchForm.quickSearchSocialId) {
      searchFormToSave.conditions.push({
        id: Date.now().toString() + '_social_id',
        field: 'social_id',
        operator: 'equals',
        value: searchForm.quickSearchSocialId,
      })
    }

    if (searchForm.quickSearchEmail) {
      searchFormToSave.conditions.push({
        id: Date.now().toString() + '_email',
        field: 'email',
        operator: 'equals',
        value: searchForm.quickSearchEmail,
      })
    }

    // 清空快捷搜索字段，避免重复
    searchFormToSave.quickSearchSocialId = ''
    searchFormToSave.quickSearchEmail = ''
  }

  const newSearch: SavedSearch = {
    id: Date.now().toString(),
    name: saveSearchName.value.trim(),
    searchForm: searchFormToSave,
    createdAt: new Date().toISOString(),
  }

  if (existingIndex > -1) {
    // 更新现有搜索
    savedSearches.value[existingIndex] = newSearch
    ElMessage.success('搜索已更新')
  } else {
    // 添加新搜索
    savedSearches.value.push(newSearch)
    ElMessage.success('搜索已保存')
  }

  // 保存到localStorage
  localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))

  // 关闭对话框并清空输入
  showSaveSearchDialog.value = false
  saveSearchName.value = ''
}

const applySavedSearch = (savedSearch: SavedSearch) => {
  try {
    // 清空当前搜索条件，避免重复
    clearAllSearchConditions()

    // 应用保存的搜索条件
    Object.assign(searchForm, savedSearch.searchForm)

    // 如果保存的搜索包含高级搜索条件，标记为使用高级搜索
    if (savedSearch.searchForm.conditions && savedSearch.searchForm.conditions.length > 0) {
      useAdvancedSearch.value = true
    }

    // 执行搜索
    handleAdvancedSearch()

    ElMessage.success(`已应用搜索: ${savedSearch.name}`)
  } catch (error) {
    console.error('应用保存的搜索失败:', error)
    ElMessage.error('应用搜索失败，请重试')
  }
}

// 清空所有搜索条件的辅助函数
const clearAllSearchConditions = () => {
  // 清空快捷搜索
  searchForm.quickSearchSocialId = ''
  searchForm.quickSearchEmail = ''

  // 清空高级搜索条件
  searchForm.conditions = []

  // 清空其他搜索字段
  searchForm.nick_name = ''
  searchForm.social_id = ''
  searchForm.email = ''
  searchForm.platforms = []
  searchForm.tiers = []
  searchForm.platform = ''
  searchForm.tier = ''
  searchForm.source = ''
  searchForm.project_code = ''
  searchForm.crawler_task_id = undefined
  searchForm.followersMin = undefined
  searchForm.followersMax = undefined
  searchForm.engagementMin = undefined
  searchForm.engagementMax = undefined
  searchForm.meanViewsMin = undefined
  searchForm.meanViewsMax = undefined
  searchForm.medianViewsMin = undefined
  searchForm.medianViewsMax = undefined
  searchForm.aiScoreMin = undefined
  searchForm.aiScoreMax = undefined
  searchForm.ai_matched = undefined
  searchForm.email_fetch_status = ''
  searchForm.has_email = undefined
  searchForm.has_bio_extracted_email = undefined
  searchForm.has_nano_extracted_email = undefined
  searchForm.createTimeRange = null
  searchForm.updateTimeRange = null
}

const deleteSavedSearch = (searchId: string) => {
  const index = savedSearches.value.findIndex((search) => search.id === searchId)
  if (index > -1) {
    const searchName = savedSearches.value[index].name
    savedSearches.value.splice(index, 1)
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))
    ElMessage.success(`已删除搜索: ${searchName}`)
  }
}

const openSaveSearchDialog = () => {
  saveSearchName.value = ''
  showSaveSearchDialog.value = true
}

const formatSavedSearchTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    return '刚刚'
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}小时前`
  } else if (diffInHours < 24 * 7) {
    return `${Math.floor(diffInHours / 24)}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 监听快速搜索字段变化，重置搜索执行状态
watch(
  [
    () => searchForm.quickSearchPlatform,
    () => searchForm.quickSearchProjectCode,
    () => searchForm.quickSearchSocialId,
    () => searchForm.quickSearchEmail,
  ],
  (newValues, oldValues) => {
    // 只有在初始化完成后才处理变化
    if (!oldValues) return

    // 检查是否有字段值发生了变化（从有值变为有值，或从空值变为有值）
    const hasChanged = newValues.some((newVal, index) => {
      const oldVal = oldValues[index]
      // 只有当新值不为空，且与旧值不同时，才认为是用户主动修改
      return newVal && newVal !== oldVal
    })

    if (hasChanged && hasExecutedSearch.value && !isSearching.value && !isRemovingFilter.value) {
      console.log('快速搜索字段值发生变化，重置搜索执行状态')
      console.log('变化前:', oldValues)
      console.log('变化后:', newValues)

      // 重置搜索执行状态，但保持其他状态
      hasExecutedSearch.value = false
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  loadData()
  loadSavedSearches() // 加载保存的搜索
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      // 验证列配置是否适用于当前页面（检查是否包含 KOL 特有的字段）
      // 过滤掉不属于 KOL 页面的字段
      const filteredColumns = parsedColumns.filter((col: TableColumn) =>
        allowedKolFields.has(col.key),
      )

      const hasKolFields = filteredColumns.some(
        (col: TableColumn) => col.key === 'social_id' || col.key === 'username',
      )

      if (hasKolFields && filteredColumns.length > 0) {
        // 检查是否有被过滤掉的字段
        if (filteredColumns.length < parsedColumns.length) {
          const removedFields = parsedColumns
            .filter((col: TableColumn) => !allowedKolFields.has(col.key))
            .map((col: TableColumn) => col.key)
          console.warn(`检测到并过滤了不属于 KOL 页面的字段: ${removedFields.join(', ')}`)
        }
        tableColumns.value = filteredColumns
        // 如果有字段被过滤，保存清理后的配置
        if (filteredColumns.length < parsedColumns.length) {
          saveColumnSettings()
        }
      } else {
        // 如果不包含 KOL 特有字段，说明可能是其他页面的配置，使用默认配置
        console.warn('检测到可能来自其他页面的列配置，使用默认配置')
        tableColumns.value = [...defaultColumns]
        saveColumnSettings() // 保存正确的默认配置
      }
    } catch (error) {
      console.error('解析列配置失败，使用默认配置:', error)
      tableColumns.value = [...defaultColumns]
      saveColumnSettings()
    }
  }
  loadProjectList()

  // 初始化一个默认的搜索条件
  if (searchForm.conditions.length === 0) {
    addCondition()
  }
})

// 创建KOL相关
const showCreateDialog = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const projectList = ref<Array<{ code: string; name: string }>>([])

const createForm = reactive({
  social_id: '',
  nick_name: '',
  platform: '',
  project_code: '',
  source: '',
  email: '',
  bio: '',
  followers_count: 0,
  likes_count: 0,
  hashtags: [] as string[],
  topics: [] as string[],
  captions: [] as string[],
  note: '',
})

// 获取项目列表
const loadProjectList = async () => {
  try {
    const projects = await fetchAllProjects()
    projectList.value = projects.map((project) => ({
      code: project.code,
      name: project.name,
    }))
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

const createRules: FormRules = {
  social_id: [{ required: true, message: '请输入KOL ID', trigger: 'blur' }],
  nick_name: [{ required: true, message: '请输入KOL Name', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择Platform', trigger: 'change' }],
  project_code: [{ required: true, message: '请选择Project Code', trigger: 'change' }],
  source: [{ required: false, message: '请输入Source', trigger: 'blur' }],
  followers_count: [{ required: true, message: '请输入Followers', trigger: 'blur' }],
}

const handleCreateKol = () => {
  showCreateDialog.value = true
}

const handleBatchEmail = async () => {
  try {
    // 检查是否选择了platform
    const hasPlatformFilter =
      searchForm.platforms.length > 0 ||
      searchForm.platform ||
      searchForm.conditions.some((condition) => condition.field === 'platform')
    if (!hasPlatformFilter) {
      ElMessage.warning('请在高级搜索中先选择平台（Platform）筛选条件，才能进行邮件群发')
      return
    }

    // 检查是否选择了project_code
    const hasProjectCodeFilter =
      searchForm.project_code ||
      searchForm.conditions.some((condition) => condition.field === 'project_code')
    if (!hasProjectCodeFilter) {
      ElMessage.warning('请在高级搜索中先选择项目代码（Project Code）筛选条件，才能进行邮件群发')
      return
    }

    // 检查是否选择了has_email
    const hasEmailFilter =
      searchForm.has_email !== undefined ||
      searchForm.conditions.some((condition) => condition.field === 'has_email')
    if (!hasEmailFilter) {
      ElMessage.warning('请在高级搜索中先选择是否有邮箱（Has Email）筛选条件，才能进行邮件群发')
      return
    }

    // 检查是否有筛选结果
    if (kolList.value.length === 0) {
      ElMessage.warning('当前没有筛选结果，无法进行邮件群发')
      return
    }

    // 获取该项目的邮件模板
    try {
      emailTemplates.value = await fetchEmailTemplatesByProject(searchForm.project_code)
      if (emailTemplates.value.length === 0) {
        ElMessage.warning(`项目 ${searchForm.project_code} 没有可用的邮件模板，请先创建邮件模板`)
        return
      }
    } catch (error) {
      console.error('获取邮件模板失败:', error)
      ElMessage.error('获取邮件模板失败，请重试')
      return
    }

    // 重置选择
    selectedTemplate.value = ''

    // 显示模板选择对话框
    showBatchEmailDialog.value = true
  } catch (error) {
    console.error('邮件群发准备失败:', error)
    ElMessage.error('邮件群发准备失败，请重试')
  }
}

const handleCreateCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消新增KOL吗？未保存的数据将会丢失。', '确认取消', {
      confirmButtonText: '确定取消',
      cancelButtonText: '继续编辑',
      type: 'warning',
    })
    // 用户确认取消
    showCreateDialog.value = false
    createFormRef.value?.resetFields()
  } catch {
    // 用户选择继续编辑，不关闭对话框
    return
  }
}

const handleCreateDialogClose = async (done: () => void) => {
  try {
    await ElMessageBox.confirm('确定要关闭新增KOL页面吗？未保存的数据将会丢失。', '确认关闭', {
      confirmButtonText: '确定关闭',
      cancelButtonText: '继续编辑',
      type: 'warning',
      distinguishCancelAndClose: true,
    })
    // 用户确认关闭
    createFormRef.value?.resetFields()
    done()
  } catch (action) {
    if (action === 'cancel') {
      // 用户选择继续编辑，不关闭对话框
      return
    } else {
      // 用户点击了关闭按钮或按了ESC，也关闭对话框
      createFormRef.value?.resetFields()
      done()
    }
  }
}

const handleCreateSave = async () => {
  try {
    await createFormRef.value?.validate()
    createLoading.value = true

    await createKol({
      social_id: createForm.social_id,
      nick_name: createForm.nick_name,
      platform: createForm.platform,
      project_code: createForm.project_code,
      source: createForm.source || undefined,
      email: createForm.email || undefined,
      bio: createForm.bio || undefined,
      followers_count: createForm.followers_count,
      likes_count: createForm.likes_count || undefined,
    })

    ElMessage.success('创建成功')
    showCreateDialog.value = false
    createFormRef.value?.resetFields()

    // 刷新列表
    await loadData()
  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error('创建失败，请检查输入信息')
  } finally {
    createLoading.value = false
  }
}

const handleSendBatchEmail = async () => {
  try {
    if (!selectedTemplate.value) {
      ElMessage.warning('请选择邮件模板')
      return
    }

    // 确认对话框
    await ElMessageBox.confirm(
      `确定要向当前筛选结果的 ${kolList.value.length} 个KOL发送邮件吗？`,
      '确认邮件群发',
      {
        confirmButtonText: '确定发送',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    batchEmailLoading.value = true

    // 准备批量发送的数据
    const kols: KOLIdentifier[] = kolList.value.map((kol) => ({
      social_id: kol.social_id,
      platform: kol.platform.toUpperCase(),
      project_code: kol.project_code || 'DEFAULT',
    }))

    // 调用批量发送API
    const response = await sendBatchEmailsToKols({
      template_code: selectedTemplate.value,
      kols: kols,
    })

    if (response.success) {
      ElMessage.success(
        `邮件群发成功！成功发送 ${response.sent_count || 0} 个，失败 ${response.failed_count || 0} 个`,
      )
      showBatchEmailDialog.value = false
      selectedTemplate.value = ''
    } else {
      ElMessage.error(`邮件群发失败：${response.message}`)
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('邮件群发失败:', error)
    ElMessage.error('邮件群发失败，请重试')
  } finally {
    batchEmailLoading.value = false
  }
}
</script>

<style scoped>
/* 基础容器样式 */
.kol-list-container {
  padding: 20px;
  min-height: 100vh;
  max-width: 100%;
  overflow-x: hidden;
  /* background: #f8fafc; */
}

/* 容器响应式优化 */
@media (max-width: 768px) {
  .kol-list-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .kol-list-container {
    padding: 12px;
  }
}

/* 页面头部响应式设计 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.header-right .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header-right .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 按钮图标优化 */
.header-right .el-button .el-icon {
  transition: transform 0.3s ease;
}

.header-right .el-button:hover .el-icon {
  transform: scale(1.1);
}

.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.advanced-toggle,
.save-search {
  font-size: 14px;
  color: #667eea;
  transition: all 0.3s ease;
}

.advanced-toggle:hover,
.save-search:hover {
  color: #4c6ef5;
  transform: translateY(-1px);
}

/* 快速搜索栏样式 */
.quick-search-bar {
  margin-bottom: 16px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-mode-indicator {
  display: flex;
  align-items: center;
}

.search-mode-indicator .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.search-mode-tip {
  margin-bottom: 20px;
}

.search-mode-tip .el-alert {
  border-radius: 8px;
}

.search-mode-tip p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 搜索结果统计样式 */
.result-count-tag {
  margin-left: 8px;
  font-weight: 500;
}

/* 搜索进行中状态样式 */
.searching-tag {
  margin-left: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.searching-tag .el-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-state .el-empty {
  padding: 0;
}

.empty-state .el-icon {
  margin-bottom: 16px;
}

/* 筛选条件标签样式优化 */
.filters-label {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.logic-tag,
.result-count-tag {
  font-size: 12px;
  border-radius: 6px;
}

.quick-search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.quick-search-input {
  flex: 1;
  min-width: 0;
}

.search-btn {
  flex-shrink: 0;
  min-width: 100px;
}

/* 统一的快速搜索组件样式 */
.quick-search-input {
  --el-input-border-radius: 12px;
  --el-select-border-radius: 12px;
}

/* 输入框样式 */
.quick-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 选择框样式 */
.quick-search-input :deep(.el-select__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-select__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-select__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-select__selected-item) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

.quick-search-input :deep(.el-select__placeholder) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 图标样式统一 */
.quick-search-input :deep(.el-input__prefix),
.quick-search-input :deep(.el-select__prefix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 32px;
}

.quick-search-input :deep(.el-input__prefix .el-icon),
.quick-search-input :deep(.el-select__prefix .el-icon) {
  font-size: 16px;
  color: #6b7280;
}

/* 清除按钮样式统一 */
.quick-search-input :deep(.el-input__suffix),
.quick-search-input :deep(.el-select__suffix) {
  display: flex;
  align-items: center;
  height: 32px;
}

.quick-search-input :deep(.el-input__clear),
.quick-search-input :deep(.el-select__caret) {
  font-size: 14px;
  color: #9ca3af;
}

.search-suffix {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.search-btn {
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  min-height: 48px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 搜索模板样式 */
.search-templates {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.templates-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 高级搜索样式 */
.advanced-search {
  margin-top: 16px;
}

.advanced-form {
  background: #fafbfc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.search-section {
  margin-bottom: 24px;
}

.search-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* 范围输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

/* 搜索操作栏样式 */
.search-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.actions-left {
  display: flex;
  gap: 12px;
}

.actions-right {
  display: flex;
  gap: 8px;
}

/* 当前筛选条件样式 */
.current-filters {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.filters-label {
  font-size: 14px;
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 12px;
}

.filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-tag {
  background: white;
  border: 1px solid #c7d2fe;
  color: #4338ca;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(67, 56, 202, 0.2);
}

.clear-all {
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.clear-all:hover {
  color: #ef4444;
  background: #fef2f2;
}

/* 表格卡片样式 - 保持原有样式 */
.table-card {
  margin-bottom: 20px;
  /* 防止表格加载时位置跳动 */
  min-height: 200px;
  position: relative;
}

.table-card :deep(.el-table) {
  width: 100% !important;
  table-layout: auto;
  /* 使用transform创建新的渲染层，提高性能 */
  transform: translateZ(0);
  will-change: transform;
}

/* 优化loading遮罩层的过渡效果 */
.table-card :deep(.el-loading-mask) {
  transition: opacity 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 确保表格容器在数据加载前后保持稳定位置 */
.table-card :deep(.el-table__body-wrapper) {
  min-height: 100px;
}

/* 表格容器样式 */
.table-container {
  position: relative;
  min-height: 200px;
  /* 使用transform优化性能 */
  transform: translateZ(0);
  will-change: transform;
}

/* 确保表格在loading状态变化时保持稳定位置 */
.table-container :deep(.el-table) {
  position: relative;
  /* 移除z-index，避免层叠问题 */
}

/* 优化loading遮罩层的过渡效果 */
.table-container :deep(.el-loading-mask) {
  transition: opacity 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 防止表格在数据加载时位置跳动 */
.table-container :deep(.el-table__body) {
  min-height: 100px;
}

/* 优化表格头部，移除可能导致卡顿的粘性定位 */
.table-container :deep(.el-table__header-wrapper) {
  background: white;
  /* 使用transform而不是position: sticky */
  transform: translateZ(0);
}

/* 优化表格行的性能 */
.table-container :deep(.el-table__row) {
  /* 移除transition: none，让Element Plus自然处理 */
  transform: translateZ(0);
}

/* 优化loading动画性能 */
.table-container :deep(.el-loading-spinner) {
  transform: translateZ(0);
  will-change: transform;
}

/* 确保表格内容在loading状态变化时保持稳定 */
.table-container :deep(.el-table__body) {
  transform: translateZ(0);
}

/* 优化表格容器的渲染性能 */
.table-card {
  /* 创建新的渲染层 */
  transform: translateZ(0);
  will-change: transform;
  /* 确保内容不会溢出 */
  overflow: hidden;
}

/* KOL信息样式 - 保持原有样式 */
.kol-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nick-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.nick-name.clickable {
  cursor: pointer;
  color: #409eff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nick-name.clickable:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

/* 分页样式 - 保持原有样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 禁用标签区域的动画 - 保持原有样式 */
.tags-container {
  transition: none !important;
}

.tags-container * {
  transition: none !important;
  animation: none !important;
}

/* 自定义标签样式 - 保持原有样式 */
.custom-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 4px;
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

/* 列设置弹窗样式 - 保持原有样式 */
.column-settings {
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-header span {
  font-size: 14px;
  color: #666;
}

.column-list {
  min-height: 150px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.column-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #909399;
}

.column-item:active {
  cursor: grabbing;
}

.fixed-column {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px 12px !important;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.fixed-label {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 导出弹窗响应式设计 */
.export-content {
  max-height: 60vh;
  overflow-y: auto;
}

.export-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

/* 高级搜索弹窗响应式设计 */
.advanced-search {
  max-height: 70vh;
  overflow-y: auto;
}

.advanced-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.search-section {
  background: #fafbfc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.section-content {
  padding: 0;
}

/* 搜索模板响应式设计 */
.search-templates {
  margin-top: 16px;
  padding: 16px;
  background: #f0f4ff;
  border-radius: 8px;
  border: 1px solid #c7d2fe;
}

.templates-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 当前筛选条件响应式设计 */
.current-filters {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.filters-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.logic-tag {
  font-size: 12px;
}

.filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-tag {
  margin: 0;
}

.clear-all {
  font-size: 12px;
  color: #6b7280;
  padding: 4px 8px;
}

/* 搜索操作栏响应式设计 */
.search-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 16px;
}

.actions-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.actions-right {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 范围输入响应式设计 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.range-separator {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
  white-space: nowrap;
}

/* 平台选项响应式设计 */
.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 弹窗底部按钮响应式设计 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.footer-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.footer-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 编辑对话框响应式设计 */
.edit-dialog {
  width: 900px;
}

.edit-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.edit-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.edit-form-container {
  background: #f8fafc;
  padding: 24px;
}

.edit-section-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.edit-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
  font-size: 18px;
}

.section-title span {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.edit-form {
  padding: 24px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.el-input__wrapper {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  border-color: #667eea;
}

.el-input__wrapper.is-focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.el-select .el-input__wrapper {
  border-radius: 8px;
}

.el-input-number .el-input__wrapper {
  border-radius: 8px;
}

.el-date-editor.el-input {
  border-radius: 8px;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

/* 响应式媒体查询 - 只调整布局，保持原有样式 */
@media (max-width: 1200px) {
  .kol-list-container {
    padding: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    padding: 8px 16px;
    font-size: 14px;
  }
}

@media (max-width: 992px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-right {
    justify-content: flex-start;
  }

  .search-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-actions {
    justify-content: flex-start;
  }

  /* 分页器响应式布局调整 */
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .pagination-info {
    justify-content: center;
  }

  .edit-dialog {
    width: 95vw;
    max-width: 800px;
  }

  .edit-form {
    padding: 16px;
  }

  .section-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }

  .header-left {
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-right {
    justify-content: center;
    gap: 8px;
  }

  .header-right .el-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 13px;
    padding: 8px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 14px;
  }

  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }

  .search-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .templates-list {
    gap: 6px;
  }

  .template-tag {
    font-size: 12px;
  }

  .filters-list {
    gap: 6px;
  }

  .filter-tag {
    font-size: 12px;
  }

  .edit-dialog {
    width: 98vw;
    max-width: none;
  }

  .edit-form-container {
    padding: 16px;
  }

  .edit-form {
    padding: 12px;
  }

  .edit-section-card {
    margin-bottom: 16px;
  }

  .section-content {
    padding: 12px;
  }

  .field-grid {
    grid-template-columns: 1fr;
  }

  .dialog-footer {
    padding: 16px 20px;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    gap: 8px;
  }

  .dialog-footer .el-button {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .page-header {
    padding: 18px 22px;
  }

  .header-right {
    gap: 10px;
  }

  .header-right .el-button {
    font-size: 13px;
    padding: 8px 14px;
  }
}

/* 平板设备优化 */
@media (max-width: 900px) and (min-width: 481px) {
  .page-header {
    padding: 16px 20px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 18px;
  }

  .page-description {
    font-size: 12px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
    min-width: auto;
    justify-content: center;
    font-size: 12px;
    padding: 10px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 13px;
  }

  .search-header {
    gap: 8px;
  }

  .search-title {
    font-size: 14px;
  }

  .search-actions {
    width: 100%;
    justify-content: space-between;
  }

  .quick-search-input {
    font-size: 14px;
  }

  .search-btn {
    font-size: 14px;
    padding: 8px 12px;
  }

  .templates-label {
    font-size: 13px;
  }

  .template-tag {
    font-size: 11px;
    padding: 4px 6px;
  }

  .filters-label {
    font-size: 13px;
  }

  .filter-tag {
    font-size: 11px;
    padding: 4px 6px;
  }

  .clear-all {
    font-size: 11px;
    padding: 2px 6px;
  }

  .edit-dialog {
    width: 100vw;
    margin: 0;
  }

  .edit-dialog :deep(.el-dialog) {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .edit-dialog :deep(.el-dialog__header) {
    border-radius: 0;
    padding: 16px 20px;
  }

  .edit-dialog :deep(.el-dialog__title) {
    font-size: 18px;
  }

  .edit-dialog :deep(.el-dialog__body) {
    max-height: calc(100vh - 120px);
  }

  .edit-form-container {
    padding: 12px;
  }

  .edit-form {
    padding: 8px;
  }

  .edit-section-card {
    margin-bottom: 12px;
  }

  .edit-section-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .edit-section-header span {
    font-size: 13px;
  }

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-form-item__label {
    font-size: 13px;
  }

  .section-content {
    padding: 8px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 12px;
    padding-bottom: 6px;
  }

  .dialog-footer {
    padding: 12px 16px;
    gap: 8px;
  }

  .footer-left,
  .footer-right {
    gap: 6px;
  }

  .dialog-footer .el-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  .form-tip {
    font-size: 11px;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .edit-dialog :deep(.el-dialog) {
    max-height: 95vh;
  }

  .edit-dialog :deep(.el-dialog__body) {
    max-height: calc(95vh - 120px);
  }

  .edit-form-container {
    padding: 16px;
  }

  .edit-form {
    padding: 12px;
  }

  .edit-section-card {
    margin-bottom: 12px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1920px) {
  .kol-list-container {
    padding: 32px;
    max-width: 1800px;
    margin: 0 auto;
  }

  .page-title {
    font-size: 32px;
  }

  .page-description {
    font-size: 18px;
  }

  .edit-dialog {
    width: 1000px;
  }

  .edit-form-container {
    padding: 32px;
  }

  .edit-form {
    padding: 32px;
  }

  .section-content {
    padding: 32px;
  }
}

/* 动态筛选条件样式 */
.global-logic {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.logic-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.logic-select {
  width: 120px;
}

.help-icon {
  color: #6b7280;
  cursor: help;
}

.filter-conditions {
  margin-bottom: 24px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.condition-row:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.field-select {
  width: 180px;
  flex-shrink: 0;
}

.operator-select {
  width: 140px;
  flex-shrink: 0;
}

.value-input {
  flex: 1;
  min-width: 0;
}

.value-input .el-input,
.value-input .el-input-number,
.value-input .el-date-picker,
.value-input .el-select {
  width: 100%;
}

.delete-btn {
  flex-shrink: 0;
  margin-left: 8px;
}

.add-condition {
  margin-bottom: 16px;
}

.add-btn {
  width: 100%;
  height: 48px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.add-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f0f4ff;
}

.save-view {
  text-align: center;
  margin-top: 16px;
}

.save-view .el-link {
  font-size: 14px;
}

/* 高级搜索布局优化 */
.advanced-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.advanced-search-dialog :deep(.el-dialog__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.advanced-search-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 24px;
}

.section-content .el-row {
  margin-bottom: 16px;
}

.section-content .el-row:last-child {
  margin-bottom: 0;
}

.range-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-input .el-input-number {
  flex: 1;
}

/* 打印样式 */
@media print {
  .kol-list-container {
    padding: 0;
    background: white;
  }

  .page-header,
  .search-card,
  .header-right,
  .search-actions,
  .pagination-wrapper {
    display: none;
  }

  .table-card {
    border: none;
    box-shadow: none;
  }

  .el-table {
    border: 1px solid #000;
  }

  .el-table th,
  .el-table td {
    border: 1px solid #000;
  }
}

/* 互动率样式 */
.engagement-excellent {
  color: #67c23a;
  font-weight: 600;
}

.engagement-good {
  color: #409eff;
  font-weight: 600;
}

.engagement-normal {
  color: #e6a23c;
  font-weight: 600;
}

.engagement-low {
  color: #f56c6c;
  font-weight: 600;
}

/* 保存搜索相关样式 */
.saved-searches {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.saved-searches-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saved-searches-label::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #28a745;
  border-radius: 2px;
}

.saved-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.saved-search-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding-right: 24px;
}

.saved-search-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.saved-search-time {
  font-size: 11px;
  color: #6c757d;
  margin-left: 6px;
  opacity: 0.8;
}

.save-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.save-search-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.save-search-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.save-search-dialog :deep(.el-dialog__close) {
  color: white;
}

.save-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.save-search-content {
  padding: 16px 0;
}

.save-search-content .el-form-item {
  margin-bottom: 0;
}

.save-search-content .el-input {
  border-radius: 8px;
}

.save-search-content .el-input :deep(.el-input__inner) {
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
}

.save-search-content .el-input :deep(.el-input__inner):focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 邮件群发对话框样式 */
.batch-email-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.batch-email-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.batch-email-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.batch-email-dialog :deep(.el-dialog__close) {
  color: white;
}

.batch-email-content {
  padding: 20px 0;
}

.template-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.template-name {
  font-weight: 600;
  color: #333;
}

.template-code {
  font-size: 12px;
  color: #666;
}

.email-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  color: #495057;
  font-size: 14px;
}

.email-info .el-icon {
  color: #28a745;
}
</style>
