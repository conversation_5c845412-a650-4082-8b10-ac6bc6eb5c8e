<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Message, Lock, View, Hide } from '@element-plus/icons-vue'

// 定义表单数据类型
interface LoginForm {
  email: string
  password: string
  rememberMe: boolean
}

// 响应式数据
const router = useRouter()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const showPassword = ref(false)

// 表单数据
const loginForm = reactive<LoginForm>({
  email: '',
  password: '',
  rememberMe: false,
})

// 自定义验证函数
const validateEmail = (rule: unknown, value: string, callback: (error?: Error) => void) => {
  // 检查是否为空或只有空格
  if (!value || !value.trim()) {
    callback(new Error('请输入邮箱地址'))
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(value.trim())) {
    callback(new Error('请输入正确的邮箱格式'))
    return
  }

  callback()
}

const validatePassword = (rule: unknown, value: string, callback: (error?: Error) => void) => {
  // 检查是否为空或只有空格
  if (!value || !value.trim()) {
    callback(new Error('请输入密码'))
    return
  }

  // 使用去除空格后的值进行验证
  const trimmedValue = value.trim()

  // 检查密码长度
  if (trimmedValue.length < 6) {
    callback(new Error('密码长度不能少于6位'))
    return
  }

  if (trimmedValue.length > 50) {
    callback(new Error('密码长度不能超过50位'))
    return
  }

  // 检查密码强度（至少包含字母和数字）
  const hasLetter = /[a-zA-Z]/.test(trimmedValue)
  const hasNumber = /\d/.test(trimmedValue)

  if (!hasLetter || !hasNumber) {
    callback(new Error('密码应包含字母和数字'))
    return
  }

  callback()
}

// 表单验证规则
const formRules = reactive<FormRules<LoginForm>>({
  email: [
    {
      validator: validateEmail,
      trigger: ['blur', 'change'],
    },
  ],
  password: [
    {
      validator: validatePassword,
      trigger: ['blur', 'change'],
    },
  ],
})

// 错误状态管理
const errorMessage = ref('')

// 处理登录提交
const handleLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  try {
    // 验证表单 - 包含邮箱格式、密码强度和非空验证
    const valid = await formEl.validate()
    if (!valid) {
      ElMessage.warning('请检查表单输入')
      return
    }

    loading.value = true
    errorMessage.value = ''

    // 简短的加载动画
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 表单验证通过即登录成功
    ElMessage.success({
      message: '登录成功！欢迎回来',
      duration: 3000,
      showClose: true,
    })

    // 保存登录状态到localStorage
    localStorage.setItem('isLoggedIn', 'true')
    localStorage.setItem('loginTime', new Date().toISOString())
    localStorage.setItem('userEmail', loginForm.email.trim())

    // 如果选择了记住我，保存记住我状态
    if (loginForm.rememberMe) {
      localStorage.setItem('rememberMe', 'true')
    } else {
      localStorage.removeItem('rememberMe')
    }

    // 跳转到控制台页面
    await router.push('/dashboard')
  } catch (error) {
    console.error('Login error:', error)

    // 根据错误类型提供更具体的错误信息
    let errorMsg = '登录过程中发生错误'

    if (error instanceof Error) {
      // 路由错误
      if (error.message.includes('route') || error.message.includes('navigation')) {
        errorMsg = '页面跳转失败，请刷新页面后重试'
      }
      // 存储错误
      else if (error.message.includes('localStorage') || error.message.includes('storage')) {
        errorMsg = '浏览器存储异常，请检查浏览器设置'
      }
      // 其他错误
      else {
        errorMsg = `登录失败：${error.message}`
      }
    } else {
      errorMsg = '未知错误，请稍后重试'
    }

    errorMessage.value = errorMsg
    ElMessage.error({
      message: errorMsg,
      duration: 5000,
      showClose: true,
    })
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      <div class="decoration-wave"></div>
    </div>

    <div class="login-wrapper">
      <!-- 品牌标识区域 -->
      <div class="brand-section">
        <div class="brand-logo">
          <div class="logo-icon">
            <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="64" height="64" rx="16" fill="url(#logoGradient)" />
              <path d="M20 44V20h8l8 16 8-16h8v24h-6V28l-6 12h-4l-6-12v16h-6z" fill="white" />
              <defs>
                <linearGradient id="logoGradient" x1="0" y1="0" x2="64" y2="64">
                  <stop offset="0%" stop-color="#667eea" />
                  <stop offset="100%" stop-color="#764ba2" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div class="brand-text">
            <h1 class="brand-title">KOL Hub</h1>
            <p class="brand-subtitle">专业的 KOL 管理平台</p>
          </div>
        </div>
      </div>

      <!-- 登录卡片 -->
      <div class="login-card">
        <div class="card-header">
          <h2 class="welcome-title">欢迎回来</h2>
          <p class="welcome-subtitle">请登录您的账户以继续</p>
        </div>

        <!-- 错误提示区域 -->
        <el-alert
          v-if="errorMessage"
          :title="errorMessage"
          type="error"
          :closable="true"
          show-icon
          class="error-alert"
          @close="errorMessage = ''"
        />

        <!-- 登录表单 -->
        <div class="login-form">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="formRules"
            size="large"
            @submit.prevent="handleLogin(loginFormRef)"
          >
            <el-form-item prop="email" class="form-item">
              <div class="input-wrapper">
                <el-input
                  v-model="loginForm.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  :prefix-icon="Message"
                  clearable
                  autocomplete="email"
                  class="modern-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="password" class="form-item">
              <div class="input-wrapper">
                <el-input
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  :prefix-icon="Lock"
                  clearable
                  autocomplete="current-password"
                  class="modern-input"
                  @keyup.enter="handleLogin(loginFormRef)"
                >
                  <template #suffix>
                    <el-icon class="password-toggle" @click="showPassword = !showPassword">
                      <View v-if="!showPassword" />
                      <Hide v-else />
                    </el-icon>
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <div class="form-options">
              <el-checkbox v-model="loginForm.rememberMe" class="remember-me"> 记住我 </el-checkbox>
            </div>

            <el-button
              type="primary"
              size="large"
              :loading="loading"
              class="login-button"
              @click="handleLogin(loginFormRef)"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form>
        </div>
      </div>

      <!-- 页脚信息 -->
      <div class="footer-section">
        <p class="copyright">&copy; 2024 KOL Hub. 保留所有权利.</p>
        <div class="footer-links">
          <el-link href="#" target="_blank" class="footer-link">隐私政策</el-link>
          <span class="link-separator">•</span>
          <el-link href="#" target="_blank" class="footer-link">服务条款</el-link>
          <span class="link-separator">•</span>
          <el-link href="#" target="_blank" class="footer-link">帮助中心</el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 现代化登录容器 */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;

  /* 现代渐变背景 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 背景动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 背景装饰元素 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  /* 备用背景 - 不支持backdrop-filter的浏览器 */
  background: rgba(255, 255, 255, 0.15);
  animation: float 6s ease-in-out infinite;
}

/* 支持backdrop-filter的浏览器使用毛玻璃效果 */
@supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
  .decoration-circle {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); /* Safari 支持 */
  }
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
}

/* 登录包装器 */
.login-wrapper {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 10;
}

/* 品牌标识区域 */
.brand-section {
  text-align: center;
  margin-bottom: 40px;
  animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.brand-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 64px;
  height: 64px;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.brand-text {
  text-align: center;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
}

/* 现代化登录卡片 */
.login-card {
  /* 备用背景 - 不支持backdrop-filter的浏览器 */
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  padding: 40px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.8s ease-out 0.2s both;
  position: relative;
  overflow: hidden;
}

/* 支持backdrop-filter的浏览器使用毛玻璃效果 */
@supports (backdrop-filter: blur(20px)) or (-webkit-backdrop-filter: blur(20px)) {
  .login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px); /* Safari 支持 */
  }
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.login-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 12px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片头部 */
.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.welcome-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* 错误提示样式 */
.error-alert {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  background: rgba(254, 226, 226, 0.9);
  backdrop-filter: blur(10px);
}

/* 表单样式 */
.login-form {
  margin-bottom: 0;
}

.form-item {
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;
}

.form-item:last-of-type {
  margin-bottom: 0;
}

.input-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* 现代化输入框样式 - 确保一致性 */
.modern-input {
  --el-input-border-radius: 12px;
  --el-input-border-color: rgba(0, 0, 0, 0.1);
  --el-input-focus-border-color: #667eea;
  --el-input-hover-border-color: rgba(102, 126, 234, 0.3);
  width: 100%;
  box-sizing: border-box;
}

.modern-input :deep(.el-input__wrapper) {
  /* 备用背景 - 不支持backdrop-filter的浏览器 */
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  height: 52px;
  min-height: 52px;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  align-items: center;
}

/* 支持backdrop-filter的浏览器使用毛玻璃效果 */
@supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
  .modern-input :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); /* Safari 支持 */
  }
}

.modern-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.modern-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-input :deep(.el-input__inner) {
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 400;
  height: 100%;
  line-height: 1.4;
  padding: 0;
  /* 确保文本输入区域一致 */
  padding-left: 0;
  padding-right: 0;
  flex: 1;
  min-width: 0;
}

.modern-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
  font-weight: 400;
}

/* 确保清除按钮样式一致 */
.modern-input :deep(.el-input__suffix-inner) {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 确保所有图标大小一致 */
.modern-input :deep(.el-icon) {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 确保前缀图标一致性 */
.modern-input :deep(.el-input__prefix) {
  display: flex;
  align-items: center;
  color: #6b7280;
  flex-shrink: 0;
}

/* 确保后缀图标一致性 */
.modern-input :deep(.el-input__suffix) {
  display: flex;
  align-items: center;
  min-width: 60px; /* 确保后缀区域有固定最小宽度 */
  justify-content: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

/* 确保前缀图标区域也有固定宽度 */
.modern-input :deep(.el-input__prefix) {
  display: flex;
  align-items: center;
  min-width: 40px; /* 确保前缀区域有固定宽度 */
  justify-content: center;
  flex-shrink: 0;
}

.password-toggle {
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #3b82f6;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 32px;
}

/* 现代化复选框样式 */
.remember-me {
  color: #4b5563;
  font-weight: 500;
  position: relative;
}

.remember-me :deep(.el-checkbox) {
  display: flex;
  align-items: center;
  gap: 12px;
}

.remember-me :deep(.el-checkbox__input) {
  position: relative;
}

.remember-me :deep(.el-checkbox__inner) {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remember-me :deep(.el-checkbox__inner:hover) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.remember-me :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.remember-me :deep(.el-checkbox__inner::after) {
  content: '';
  width: 5px;
  height: 9px;
  border: 2px solid #ffffff;
  border-left: 0;
  border-top: 0;
  transform: translate(-50%, -50%) rotate(45deg) scale(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  left: 50%;
  top: 50%;
  transform-origin: center;
}

.remember-me :deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
  transform: translate(-50%, -50%) rotate(45deg) scale(1);
}

.remember-me :deep(.el-checkbox__label) {
  color: #4b5563;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.4;
  user-select: none;
}

/* 现代化登录按钮 */
.login-button {
  width: 100%;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  margin-top: 8px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(0);
}

.login-button.is-loading {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* 页脚样式 */
.footer-section {
  text-align: center;
  margin-top: 32px;
  animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.copyright {
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 400;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.footer-link {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: rgba(255, 255, 255, 0.9);
}

.link-separator {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-wrapper {
    max-width: 100%;
    padding: 16px;
  }

  .brand-title {
    font-size: 2rem;
  }

  .brand-subtitle {
    font-size: 1rem;
  }

  .login-card {
    padding: 32px 24px;
    border-radius: 20px;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 4px;
  }

  .link-separator {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-wrapper {
    padding: 12px;
  }

  .brand-section {
    margin-bottom: 32px;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .login-card {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .login-button {
    height: 48px;
    font-size: 15px;
  }

  .circle-1,
  .circle-2,
  .circle-3 {
    display: none;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1200px) {
  .login-wrapper {
    max-width: 520px;
  }

  .login-card {
    padding: 48px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    /* 备用背景 - 不支持backdrop-filter的浏览器 */
    background: rgba(30, 30, 30, 0.98);
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* 支持backdrop-filter的浏览器使用毛玻璃效果 */
  @supports (backdrop-filter: blur(20px)) or (-webkit-backdrop-filter: blur(20px)) {
    .login-card {
      background: rgba(30, 30, 30, 0.95);
    }
  }

  .welcome-title {
    color: #ffffff;
  }

  .welcome-subtitle {
    color: #d1d5db;
  }

  .modern-input :deep(.el-input__wrapper) {
    /* 备用背景 - 不支持backdrop-filter的浏览器 */
    background: rgba(55, 55, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* 支持backdrop-filter的浏览器使用毛玻璃效果 */
  @supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
    .modern-input :deep(.el-input__wrapper) {
      background: rgba(55, 55, 55, 0.8);
    }
  }

  .modern-input :deep(.el-input__inner) {
    color: #ffffff;
  }
}

/* 焦点状态优化 */
.el-input__wrapper:focus-within {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.el-button:focus {
  outline: 2px solid rgba(64, 158, 255, 0.5);
  outline-offset: 2px;
}
</style>
