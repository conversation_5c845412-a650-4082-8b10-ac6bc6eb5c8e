<template>
  <div class="candidates-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">候选人管理</h1>
        <p class="page-description">管理回复邮件的KOL候选人，跟踪合作进度</p>
      </div>
      <div class="header-right">
        <el-button @click="exportSearchResults">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <div class="search-title">
          <el-icon><Search /></el-icon>
          <span>智能搜索</span>
        </div>
        <div class="search-actions">
          <el-button link @click="openAdvancedSearchDialog" class="advanced-toggle">
            <el-icon><Operation /></el-icon>
            高级搜索
          </el-button>
          <el-button link @click="openSaveSearchDialog" class="save-search">
            <el-icon><Star /></el-icon>
            保存搜索
          </el-button>
        </div>
      </div>

      <!-- 快速搜索栏 -->
      <div class="quick-search-bar">
        <div class="quick-search-container">
          <!-- KOL ID搜索 -->
          <el-input
            v-model="searchForm.quickSearchSocialId"
            placeholder="KOL ID"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>

          <!-- Reply Email搜索 -->
          <el-input
            v-model="searchForm.quickSearchEmail"
            placeholder="Reply Email"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>

          <!-- Tracker搜索 -->
          <el-input
            v-model="searchForm.quickSearchTracker"
            placeholder="Tracker"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><UserFilled /></el-icon>
            </template>
          </el-input>
          <!-- Project Code搜索 -->
          <el-select
            v-model="searchForm.quickSearchProject"
            placeholder="Project Code"
            clearable
            filterable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><CollectionTag /></el-icon>
            </template>
            <el-option
              v-for="project in projectList"
              :key="project.code"
              :label="project.code"
              :value="project.code"
            >
              {{ project.code }}
            </el-option>
          </el-select>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            size="large"
            class="search-btn"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 搜索历史和模板 -->
      <div class="search-templates" v-if="searchTemplates.length > 0">
        <div class="templates-label">快速搜索模板：</div>
        <div class="templates-list">
          <el-tag
            v-for="template in searchTemplates"
            :key="template.id"
            class="template-tag"
            type="info"
            effect="plain"
            @click="applySearchTemplate(template)"
            closable
            @close="removeSearchTemplate(template.id)"
          >
            {{ template.name }}
          </el-tag>
        </div>
      </div>

      <!-- 已保存的搜索 -->
      <div class="saved-searches" v-if="savedSearches.length > 0">
        <div class="saved-searches-label">已保存的搜索：</div>
        <div class="saved-searches-list">
          <el-tag
            v-for="savedSearch in savedSearches"
            :key="savedSearch.id"
            class="saved-search-tag"
            type="success"
            effect="plain"
            @click="applySavedSearch(savedSearch)"
            closable
            @close="deleteSavedSearch(savedSearch.id)"
          >
            {{ savedSearch.name }}
            <span class="saved-search-time">{{
              formatSavedSearchTime(savedSearch.createdAt)
            }}</span>
          </el-tag>
        </div>
      </div>

      <!-- 当前搜索条件显示 -->
      <div class="current-filters" v-if="shouldShowCurrentFilters">
        <div class="filters-label">
          当前筛选条件：
          <el-tag
            v-if="hasAdvancedFilters"
            :type="searchForm.searchLogic === 'and' ? 'warning' : 'success'"
            size="small"
            effect="plain"
            class="logic-tag"
          >
            {{ searchForm.searchLogic === 'and' ? 'AND（同时满足）' : 'OR（满足任一）' }}
          </el-tag>

          <!-- 搜索结果统计 - 显示在同一行 -->
          <el-tag
            v-if="hasExecutedSearch && !isSearching && !loading"
            type="info"
            size="small"
            effect="plain"
            class="result-count-tag"
          >
            找到 {{ pagination.total }} 条结果
          </el-tag>

          <!-- 搜索进行中状态 - 显示在同一行 -->
          <el-tag
            v-if="isSearching || (hasExecutedSearch && loading)"
            type="warning"
            size="small"
            effect="plain"
            class="searching-tag"
          >
            <el-icon class="is-loading"><Loading /></el-icon>
            正在搜索...
          </el-tag>
        </div>
        <div class="filters-list">
          <el-tag
            v-for="filter in activeFilters"
            :key="filter.key"
            closable
            @close="removeFilter(filter.key)"
            class="filter-tag"
          >
            {{ filter.label }}: {{ filter.value }}
          </el-tag>
          <el-button type="text" @click="clearAllFilters" class="clear-all">
            <el-icon><Delete /></el-icon>
            清空所有
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearchDialog"
      title="设置筛选条件"
      width="800px"
      class="advanced-search-dialog"
      :before-close="handleAdvancedSearchClose"
      append-to-body
    >
      <div class="advanced-search-content" v-loading="loading">
        <!-- 全局逻辑设置 -->
        <div class="global-logic">
          <span class="logic-label">符合以下所有条件</span>
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </div>

        <!-- 动态筛选条件 -->
        <div class="filter-conditions">
          <div
            v-for="(condition, index) in searchForm.conditions"
            :key="condition.id"
            class="condition-row"
          >
            <!-- 字段选择 -->
            <el-select
              v-model="condition.field"
              placeholder="选择字段"
              size="large"
              class="field-select"
              @change="handleFieldChange(index)"
            >
              <el-option
                v-for="field in availableFields"
                :key="field.key"
                :label="field.label"
                :value="field.key"
              />
            </el-select>

            <!-- 操作符选择 -->
            <el-select
              v-model="condition.operator"
              placeholder="选择操作符"
              size="large"
              class="operator-select"
              @change="handleOperatorChange(index)"
            >
              <el-option
                v-for="operator in getOperatorsForField(condition.field)"
                :key="operator.value"
                :label="operator.label"
                :value="operator.value"
              />
            </el-select>

            <!-- 值输入 -->
            <div class="value-input">
              <!-- 模糊文本输入 -->
              <el-input
                v-if="
                  getFieldType(condition.field) === 'fuzzy_text' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 精确文本输入 -->
              <el-input
                v-else-if="
                  getFieldType(condition.field) === 'text' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="
                  getFieldType(condition.field) === 'number' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                :min="getFieldMin()"
                :max="getFieldMax()"
                :precision="getFieldPrecision()"
                size="large"
                :controls="false"
                style="width: 100%"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="
                  getFieldType(condition.field) === 'date' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                type="date"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />

              <!-- 选择框 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'select' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in getFieldOptions(condition.field)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 布尔选择 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'boolean' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </div>

            <!-- 删除按钮 -->
            <el-button
              type="danger"
              :icon="Close"
              circle
              size="large"
              class="delete-btn"
              @click="removeCondition(index)"
            />
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div class="add-condition">
          <el-button type="primary" :icon="Plus" @click="addCondition" class="add-btn">
            添加条件
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="handleAdvancedSearchClose">取消</el-button>
          </div>
          <div class="footer-right">
            <el-button @click="clearAdvancedSearch">清空</el-button>
            <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="candidatesList"
        v-loading="loading"
        stripe
        fit
        style="width: 100%; table-layout: auto"
        :row-style="{ height: '48px' }"
        :cell-style="{
          padding: '8px 12px',
          'white-space': 'nowrap',
          'text-overflow': 'ellipsis',
          overflow: 'hidden',
        }"
      >
        <!-- 动态生成表格列 -->
        <template v-for="column in visibleColumns" :key="column.key">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.key === 'selection'"
            type="selection"
            :width="column.width"
            :min-width="column.minWidth"
          />

          <!-- KOL ID列 -->
          <el-table-column
            v-else-if="column.key === 'social_id'"
            :fixed="column.fixed"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <div class="social-id-container">
                <span class="social-id">{{ row.social_id || '-' }}</span>
                <!-- 人工校准角标 -->
                <el-tooltip
                  v-if="row.need_review"
                  content="需要人工校准"
                  placement="top"
                  :show-after="200"
                >
                  <el-icon class="review-badge">
                    <Warning />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>

          <!-- KOL昵称列 -->
          <el-table-column
            v-else-if="column.key === 'nick_name'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="nick-name">{{ row.nick_name }}</span>
            </template>
          </el-table-column>

          <!-- 平台列 -->
          <el-table-column
            v-else-if="column.key === 'platform'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <div class="platform-badge">
                <el-icon v-if="row.platform === 'TIKTOK'" color="#ff0050"><VideoPlay /></el-icon>
                <el-icon v-else-if="row.platform === 'INSTAGRAM'" color="#e4405f"
                  ><Picture
                /></el-icon>
                <el-icon v-else-if="row.platform === 'YOUTUBE'" color="#ff0000"
                  ><Monitor
                /></el-icon>
                <span>{{ getPlatformLabel(row.platform) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 跟进状态列 -->
          <el-table-column
            v-else-if="column.key === 'follow_up_status'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <el-tag :type="getFollowUpStatusType(row.follow_up_status)">
                {{ getFollowUpStatusLabel(row.follow_up_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 跟进备注列 -->
          <el-table-column
            v-else-if="column.key === 'follow_up_note'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.follow_up_note || '-' }}</span>
            </template>
          </el-table-column>

          <!-- 更新时间列 -->
          <el-table-column
            v-else-if="column.key === 'updated_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-else-if="column.key === 'actions'"
            :fixed="column.fixed"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="handleView(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </template>
          </el-table-column>

          <!-- 其他普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :show-overflow-tooltip="column.showTooltip !== false"
          />
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[100, 200, 500, 1000]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="500px">
      <div class="column-settings">
        <div class="setting-header">
          <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          <el-button size="small" @click="resetColumns">重置默认</el-button>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :move="checkMoveAllowed"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'nick_name' ||
                  element.key === 'actions',
              }"
            >
              <el-icon
                class="drag-handle"
                :class="{
                  disabled:
                    element.key === 'selection' ||
                    element.key === 'nick_name' ||
                    element.key === 'actions',
                }"
              >
                <Rank />
              </el-icon>
              <el-checkbox
                v-model="element.visible"
                @change="saveColumnSettings"
                :disabled="
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'nick_name'
                "
              >
                {{ element.label }}
              </el-checkbox>
              <span
                v-if="
                  element.key === 'selection' ||
                  element.key === 'nick_name' ||
                  element.key === 'actions'
                "
                class="fixed-label"
              >
                (固定)
              </span>
            </div>
          </template>
        </draggable>
      </div>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      class="export-dialog"
      append-to-body
    >
      <div class="export-content">
        <el-form :model="exportConfig" label-width="120px" class="export-form">
          <!-- 导出范围 -->
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio value="filtered">当前筛选结果</el-radio>
              <el-radio value="current">当前页面数据</el-radio>
              <el-radio value="all">全部数据</el-radio>
            </el-radio-group>
            <div class="form-hint">
              <span v-if="exportConfig.scope === 'filtered'"
                >将导出当前筛选条件下的所有 {{ candidatesList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'current'"
                >将导出当前页面的 {{ candidatesList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'all'"
                >将导出全部 {{ pagination.total }} 条数据（忽略筛选条件）</span
              >
              <span v-else>将导出选中的数据（功能开发中）</span>
            </div>
          </el-form-item>

          <!-- 导出格式 -->
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportConfig.format">
              <el-radio value="xlsx">Excel 文件 (.xlsx)</el-radio>
              <el-radio value="csv">CSV 文件 (.csv)</el-radio>
              <el-radio value="json">JSON 文件 (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件名设置 -->
          <el-form-item label="文件名">
            <el-input
              v-model="exportConfig.filename"
              placeholder="请输入文件名"
              maxlength="50"
              show-word-limit
            >
              <template #suffix>
                <span class="file-extension">.{{ exportConfig.format }}</span>
              </template>
            </el-input>
          </el-form-item>

          <!-- 其他选项 -->
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportConfig.includeHeaders">包含表头</el-checkbox>
          </el-form-item>

          <!-- 选择字段 -->
          <el-form-item label="导出字段" class="field-selection">
            <div class="field-selection-header">
              <span class="selection-info"
                >已选择 {{ exportConfig.fields.length }} / {{ exportableFields.length }} 项</span
              >
              <div class="selection-buttons">
                <el-button type="text" size="small" @click="selectAllFields">全选</el-button>
                <el-button type="text" size="small" @click="clearAllFields">全不选</el-button>
              </div>
            </div>
            <div class="field-list">
              <el-checkbox-group v-model="exportConfig.fields">
                <div class="field-grid">
                  <el-checkbox
                    v-for="field in exportableFields"
                    :key="field.key"
                    :value="field.key"
                    class="field-item"
                  >
                    {{ field.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :disabled="exportConfig.fields.length === 0"
          >
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 保存搜索对话框 -->
    <el-dialog
      v-model="showSaveSearchDialog"
      title="保存搜索"
      width="400px"
      class="save-search-dialog"
    >
      <div class="save-search-content">
        <el-form>
          <el-form-item label="搜索名称" required>
            <el-input
              v-model="saveSearchName"
              placeholder="请输入搜索名称"
              clearable
              @keyup.enter="saveCurrentSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSaveSearchDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentSearch" :disabled="!saveSearchName.trim()">
            <el-icon><Star /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 候选人详情抽屉 -->
    <CandidateDetailDrawer
      v-model="showDetailDrawer"
      :candidate-data="selectedCandidate"
      @edit="handleEditFromDrawer"
      @save="handleSaveFromDrawer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  VideoPlay,
  Picture,
  Monitor,
  Setting,
  Rank,
  View,
  Delete,
  Operation,
  Star,
  Download,
  Plus,
  Close,
  QuestionFilled,
  User,
  Message,
  CollectionTag,
  UserFilled,
  Loading,
  Warning,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  searchCandidates,
  updateCandidate,
  type CandidateItem,
  type PlatformType,
  type UpdateCandidateRequest,
  type CandidateSearchParams,
} from '../services/candidateApi'
import { fetchAllProjects, type Project } from '../services/projectApi'

// 类型定义
interface SearchCondition {
  id: string
  field: string
  operator: string
  value: string | number | boolean | null
}

interface FieldConfig {
  key: string
  label: string
  type: string
  apiField: string
  fuzzy?: boolean // 是否为模糊匹配字段
}

interface SearchForm {
  // 快速搜索
  quickSearchSocialId: string
  quickSearchEmail: string
  quickSearchProject: string
  quickSearchTracker: string
  // 搜索逻辑设置
  searchLogic: 'and' | 'or'
  // 动态筛选条件
  conditions: SearchCondition[]
  // 基本信息
  nick_name: string
  reply_email_addr: string
  project_code: string
  // 平台信息
  platforms: string[]
  // 状态信息
  follow_up_statuses: string[]
  tracker: string
  // 时间范围
  createTimeRange?: [Date, Date] | null
  updateTimeRange?: [Date, Date] | null
}

interface SearchTemplate {
  id: string
  name: string
  conditions: Partial<SearchForm>
  createdAt: string
}

interface SavedSearch {
  id: string
  name: string
  searchForm: SearchForm
  createdAt: string
}

interface ActiveFilter {
  key: string
  label: string
  value: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  minWidth?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
}

interface ExportConfig {
  fields: string[]
  format: 'xlsx' | 'csv' | 'json'
  scope: 'all' | 'current' | 'filtered' | 'selected'
  includeHeaders: boolean
  filename: string
}

interface ExportItem {
  [key: string]: string | number
}

// 响应式数据
const loading = ref(false)
const candidatesList = ref<CandidateItem[]>([])
const showDetailDrawer = ref(false)
const selectedCandidate = ref<CandidateItem | null>(null)
const showColumnSettings = ref(false)

// 搜索相关数据
const showAdvancedSearchDialog = ref(false)
const showExportDialog = ref(false)
const searchTemplates = ref<SearchTemplate[]>([])

// 保存搜索相关数据
const savedSearches = ref<SavedSearch[]>([])
const showSaveSearchDialog = ref(false)
const saveSearchName = ref('')
const SAVED_SEARCHES_KEY = 'candidates-saved-searches'

// 项目列表数据
const projectList = ref<Project[]>([])

// 导出相关数据
const exportConfig = reactive<ExportConfig>({
  fields: [],
  format: 'xlsx',
  scope: 'filtered',
  includeHeaders: true,
  filename: '',
})

const searchForm = reactive<SearchForm>({
  quickSearchSocialId: '',
  quickSearchEmail: '',
  quickSearchProject: '',
  quickSearchTracker: '',
  searchLogic: 'and',
  conditions: [],
  nick_name: '',
  reply_email_addr: '',
  project_code: '',
  platforms: [],
  follow_up_statuses: [],
  tracker: '',
  createTimeRange: null,
  updateTimeRange: null,
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 100,
  total: 0,
})

// 默认列配置 - 按照用户提供的字段映射关系
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true },
  {
    key: 'social_id',
    prop: 'social_id',
    label: 'KOL ID',
    minWidth: 150,
    visible: true,
    fixed: true,
    showTooltip: true,
  },
  {
    key: 'nick_name',
    prop: 'nick_name',
    label: 'KOL Name',
    minWidth: 180,
    visible: true,
    showTooltip: true,
  },
  {
    key: 'project_code',
    prop: 'project_code',
    label: 'Project Code',
    width: 140,
    visible: true,
    showTooltip: true,
  },
  {
    key: 'reply_email_addr',
    prop: 'reply_email_addr',
    label: 'Reply Email',
    minWidth: 150,
    visible: true,
    showTooltip: true,
  },
  {
    key: 'platform',
    prop: 'platform',
    label: 'Platform',
    width: 110,
    visible: true,
  },
  {
    key: 'follow_up_status',
    prop: 'follow_up_status',
    label: 'Follow-up Status',
    width: 160,
    visible: true,
  },
  {
    key: 'tracker',
    prop: 'tracker',
    label: 'Tracker',
    width: 100,
    visible: true,
    showTooltip: true,
  },

  {
    key: 'follow_up_note',
    prop: 'follow_up_note',
    label: 'Follow-up Note',
    minWidth: 200,
    visible: true,
    showTooltip: true,
  },
  { key: 'note', prop: 'note', label: 'Note', minWidth: 120, visible: true, showTooltip: true },
  { key: 'created_at', prop: 'created_at', label: 'Created At', minWidth: 150, visible: true },
  { key: 'updated_at', prop: 'updated_at', label: 'Updated At', minWidth: 150, visible: true },
  { key: 'actions', label: 'Actions', width: 100, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// 计算可见的列
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => column.visible)
})

// 本地存储键名
const COLUMN_SETTINGS_KEY = 'candidates-list-column-settings'

// 字段配置 - 与列设置保持一致
const availableFields: FieldConfig[] = [
  // 精确匹配字段
  { key: 'social_id', label: 'KOL ID', type: 'text', apiField: 'social_id' },
  // 模糊匹配字段
  { key: 'nick_name', label: 'KOL Name', type: 'fuzzy_text', apiField: 'nick_name', fuzzy: true },
  {
    key: 'reply_email_addr',
    label: 'Reply Email',
    type: 'fuzzy_text',
    apiField: 'reply_email',
    fuzzy: true,
  },
  { key: 'project_code', label: 'Project Code', type: 'select', apiField: 'project_code' },
  { key: 'platform', label: 'Platform', type: 'select', apiField: 'platform' },
  { key: 'follow_up_status', label: 'Follow-up Status', type: 'select', apiField: 'status' },
  { key: 'tracker', label: 'Tracker', type: 'fuzzy_text', apiField: 'tracker', fuzzy: true },
  {
    key: 'follow_up_note',
    label: 'Follow-up Note',
    type: 'fuzzy_text',
    apiField: 'follow_up_note',
    fuzzy: true,
  },
  { key: 'note', label: 'Note', type: 'fuzzy_text', apiField: 'note', fuzzy: true },
  { key: 'created_at', label: 'Created At', type: 'date', apiField: 'created_at' },
  { key: 'updated_at', label: 'Updated At', type: 'date', apiField: 'updated_at' },
]

// 操作符配置 - 根据 CandidateSearch schema 设计
const operators = {
  fuzzy_text: [{ value: 'contains', label: '包含' }], // 模糊匹配字段只支持包含操作
  text: [{ value: 'equals', label: '等于' }], // 精确匹配字段只支持等于操作
  select: [{ value: 'equals', label: '等于' }], // 选择字段只支持等于操作
  boolean: [{ value: 'equals', label: '等于' }], // 布尔字段只支持等于操作
}

// 条件映射到API参数的函数 - 符合 CandidateSearch schema
const mapConditionToApiParams = async (
  condition: SearchCondition,
  field: FieldConfig,
  searchParams: CandidateSearchParams,
) => {
  const { operator, value } = condition
  const { apiField, fuzzy } = field

  // 根据字段类型和操作符映射到API参数
  if (fuzzy && operator === 'contains') {
    // 模糊匹配字段
    ;(searchParams as Record<string, unknown>)[apiField] = value
  } else if (!fuzzy && operator === 'equals') {
    // 精确匹配字段
    ;(searchParams as Record<string, unknown>)[apiField] = value
  }
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const skip = (pagination.currentPage - 1) * pagination.pageSize

    // 检查是否有高级搜索条件
    const hasAdvancedSearch = searchForm.conditions.length > 0

    if (hasAdvancedSearch && searchForm.conditions.length > 0) {
      // 使用搜索接口 - 处理动态筛选条件
      const searchParams: CandidateSearchParams = {}

      // 处理动态筛选条件
      for (const condition of searchForm.conditions) {
        if (
          !condition.field ||
          !condition.operator ||
          condition.value === null ||
          condition.value === ''
        ) {
          continue
        }

        const field = availableFields.find((f) => f.key === condition.field)
        if (!field) continue

        // 根据字段类型和操作符映射到API参数
        await mapConditionToApiParams(condition, field, searchParams)
      }

      const response = await searchCandidates(searchParams, { skip, limit: pagination.pageSize })
      candidatesList.value = response.items
      pagination.total = response.total

      console.log('高级搜索完成，结果数量:', response.total)

      // 只有在搜索状态下才标记为已执行搜索
      if (isSearching.value) {
        hasExecutedSearch.value = true
      }
    } else {
      // 使用搜索接口 - 快速搜索也使用POST请求
      const searchParams: CandidateSearchParams = {}

      // 添加快速搜索条件
      if (searchForm.quickSearchSocialId) {
        searchParams.social_id = searchForm.quickSearchSocialId
      }
      if (searchForm.quickSearchEmail) {
        searchParams.reply_email = searchForm.quickSearchEmail
      }
      if (searchForm.quickSearchProject) {
        searchParams.project_code = searchForm.quickSearchProject
      }
      if (searchForm.quickSearchTracker) {
        searchParams.tracker = searchForm.quickSearchTracker
      }

      const listParams = {
        skip,
        limit: pagination.pageSize,
      }

      const response = await searchCandidates(searchParams, listParams)
      candidatesList.value = response.items
      pagination.total = response.total

      console.log('快速搜索完成，结果数量:', response.total)

      // 只有在搜索状态下才标记为已执行搜索
      if (isSearching.value) {
        hasExecutedSearch.value = true
      }
    }
  } catch (error) {
    console.error('加载候选人数据失败:', error)
    ElMessage.error('加载数据失败')
    candidatesList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
    // 重置搜索进行状态
    isSearching.value = false
  }
}

// 防抖搜索函数
const debouncedSearch = (() => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  return (immediate = false) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    if (immediate) {
      executeSearch()
    } else {
      timeoutId = setTimeout(() => {
        executeSearch()
      }, 300)
    }
  }
})()

// 执行搜索的核心函数
const executeSearch = () => {
  pagination.currentPage = 1

  // 检查是否有高级搜索内容，如果有则提示用户
  const hadAdvancedSearchContent = hasAdvancedSearchContent()

  // 清空所有高级搜索条件，确保只有快速搜索生效
  clearAdvancedSearchConditions()

  // 切换到快速搜索模式
  useAdvancedSearch.value = false

  // 提供用户反馈
  if (hadAdvancedSearchContent) {
    ElMessage.info('已切换到快速搜索模式，高级搜索条件已清除')
  }

  // 只有在还没有设置搜索状态时才设置（避免重复设置）
  if (!isSearching.value) {
    isSearching.value = true
  }

  loadData()
}

const handleSearch = () => {
  debouncedSearch(true) // 立即执行搜索
}

// 专门用于删除筛选条件后的搜索
const handleSearchAfterFilterRemoval = () => {
  // 立即设置搜索状态，确保UI立即响应
  isSearching.value = true
  // 立即执行搜索，不使用防抖
  executeSearch()
}

// 调试辅助函数 - 获取当前搜索状态
const getSearchStateDebugInfo = () => {
  return {
    searchMode: useAdvancedSearch.value ? 'advanced' : 'quick',
    hasExecutedSearch: hasExecutedSearch.value,
    isSearching: isSearching.value,
    isRemovingFilter: isRemovingFilter.value,
    loading: loading.value,
    quickSearchFields: {
      socialId: searchForm.quickSearchSocialId,
      email: searchForm.quickSearchEmail,
      tracker: searchForm.quickSearchTracker,
      project: searchForm.quickSearchProject,
    },
    advancedConditionsCount: searchForm.conditions.length,
    activeFiltersCount: activeFilters.value.length,
    totalResults: pagination.total,
  }
}

const handleReset = () => {
  Object.assign(searchForm, {
    quickSearchSocialId: '',
    quickSearchEmail: '',
    quickSearchProject: '',
    quickSearchTracker: '',
    searchLogic: 'and',
    conditions: [],
    nick_name: '',
    reply_email_addr: '',
    project_code: '',
    platforms: [],
    follow_up_statuses: [],
    tracker: '',
    createTimeRange: null,
    updateTimeRange: null,
  })

  // 重置搜索执行状态
  hasExecutedSearch.value = false
  isSearching.value = false
  isRemovingFilter.value = false
  useAdvancedSearch.value = false

  handleSearch()
}

// 工具函数
const getFieldType = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  return field?.type || 'text'
}

const getOperatorsForField = (fieldKey: string) => {
  const fieldType = getFieldType(fieldKey)
  return operators[fieldType as keyof typeof operators] || operators.text
}

const isNoValueOperator = (operator: string): boolean => {
  return ['is_empty', 'is_not_empty'].includes(operator)
}

const getFieldPlaceholder = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  return field ? `请输入${field.label}` : '请输入'
}

const getFieldMin = (): number => {
  return 0
}

const getFieldMax = (): number | undefined => {
  return undefined
}

const getFieldPrecision = (): number => {
  return 0
}

const getFieldOptions = (fieldKey: string) => {
  switch (fieldKey) {
    case 'platform':
      return [
        { label: 'TikTok', value: 'TIKTOK' },
        { label: 'Instagram', value: 'INSTAGRAM' },
        { label: 'YouTube', value: 'YOUTUBE' },
      ]
    case 'follow_up_status':
      return [
        { label: 'Pending', value: 'PENDING' },
        { label: 'Processing', value: 'PROCESSING' },
        { label: 'Drafting', value: 'DRAFTING' },
        { label: 'Completed', value: 'COMPLETED' },
        { label: 'Paid', value: 'PAID' },
        { label: 'Not Target', value: 'NOT_TARGET' },
        { label: 'Off', value: 'OFF' },
      ]
    case 'project_code':
      return projectList.value.map((project) => ({
        label: project.code,
        value: project.code,
      }))
    default:
      return []
  }
}

// 动态筛选条件相关方法
const addCondition = () => {
  const newCondition: SearchCondition = {
    id: Date.now().toString(),
    field: 'social_id', // 默认选择KOL ID字段
    operator: 'equals', // 默认选择等于操作符
    value: null,
  }
  searchForm.conditions.push(newCondition)
}

const removeCondition = (index: number) => {
  searchForm.conditions.splice(index, 1)
}

const handleFieldChange = (index: number) => {
  const condition = searchForm.conditions[index]
  condition.operator = ''
  condition.value = null
}

const handleOperatorChange = (index: number) => {
  const condition = searchForm.conditions[index]
  condition.value = null
}

const clearAdvancedSearch = () => {
  searchForm.conditions = []
  searchForm.searchLogic = 'and'
  showAdvancedSearchDialog.value = false
}

// 高级搜索相关方法
const useAdvancedSearch = ref(false)
const hasExecutedSearch = ref(false) // 跟踪是否已执行过搜索操作
const isSearching = ref(false) // 跟踪搜索进行状态
const isRemovingFilter = ref(false) // 跟踪是否正在删除筛选条件

// 验证搜索条件
const validateSearchConditions = (): boolean => {
  if (searchForm.conditions.length === 0) {
    ElMessage.warning('请至少添加一个搜索条件')
    return false
  }

  for (const condition of searchForm.conditions) {
    if (!condition.field) {
      ElMessage.warning('请选择搜索字段')
      return false
    }
    if (!condition.operator) {
      ElMessage.warning('请选择操作符')
      return false
    }
    if (
      !isNoValueOperator(condition.operator) &&
      (condition.value === null || condition.value === '')
    ) {
      ElMessage.warning('请输入搜索值')
      return false
    }
  }

  return true
}

// 获取搜索条件摘要
const getSearchSummary = (): string => {
  const conditionCount = searchForm.conditions.length
  if (conditionCount === 0) return '搜索条件'

  const firstCondition = searchForm.conditions[0]
  const field = availableFields.find((f) => f.key === firstCondition.field)
  const fieldLabel = field ? field.label : firstCondition.field

  if (conditionCount === 1) {
    return `${fieldLabel}筛选条件`
  } else {
    return `${fieldLabel}等${conditionCount}个筛选条件`
  }
}

// 检查是否有快速搜索内容的辅助函数
const hasQuickSearchContent = (): boolean => {
  return !!(
    searchForm.quickSearchSocialId ||
    searchForm.quickSearchEmail ||
    searchForm.quickSearchTracker ||
    searchForm.quickSearchProject
  )
}

// 清空快速搜索字段
const clearQuickSearchFields = () => {
  searchForm.quickSearchSocialId = ''
  searchForm.quickSearchEmail = ''
  searchForm.quickSearchTracker = ''
  searchForm.quickSearchProject = ''
}

// 检查是否有高级搜索内容的辅助函数
const hasAdvancedSearchContent = (): boolean => {
  return searchForm.conditions.length > 0
}

// 清空高级搜索条件
const clearAdvancedSearchConditions = () => {
  searchForm.conditions = []
}

const handleAdvancedSearch = () => {
  // 验证搜索条件
  if (!validateSearchConditions()) {
    return
  }

  showAdvancedSearchDialog.value = false
  pagination.currentPage = 1

  // 检查是否有快速搜索内容，如果有则提示用户
  const hadQuickSearchContent = hasQuickSearchContent()

  // 清空所有快速搜索字段，确保只有高级搜索生效
  clearQuickSearchFields()

  // 标记已经执行了高级搜索
  useAdvancedSearch.value = true

  // 开始搜索状态，但不立即标记为已执行搜索
  isSearching.value = true

  // 提供用户反馈
  if (hadQuickSearchContent) {
    ElMessage.success(`已切换到高级搜索模式，快速搜索条件已清除`)
  } else {
    ElMessage.success(`已应用${getSearchSummary()}`)
  }

  loadData()
}

const handleAdvancedSearchClose = () => {
  showAdvancedSearchDialog.value = false
  // 如果取消高级搜索，清除未执行的搜索条件
  if (!useAdvancedSearch.value) {
    searchForm.conditions = []
  }
}

// 打开高级搜索弹窗
const openAdvancedSearchDialog = () => {
  showAdvancedSearchDialog.value = true
}

const openSaveSearchDialog = () => {
  saveSearchName.value = ''
  showSaveSearchDialog.value = true
}

const saveCurrentSearch = () => {
  if (!saveSearchName.value.trim()) {
    ElMessage.warning('请输入搜索名称')
    return
  }

  // 检查是否已存在同名搜索
  const existingIndex = savedSearches.value.findIndex(
    (search) => search.name === saveSearchName.value.trim(),
  )

  const newSearch: SavedSearch = {
    id: Date.now().toString(),
    name: saveSearchName.value.trim(),
    searchForm: JSON.parse(JSON.stringify(searchForm)), // 深拷贝
    createdAt: new Date().toISOString(),
  }

  if (existingIndex > -1) {
    // 更新现有搜索
    savedSearches.value[existingIndex] = newSearch
    ElMessage.success('搜索已更新')
  } else {
    // 添加新搜索
    savedSearches.value.push(newSearch)
    ElMessage.success('搜索已保存')
  }

  // 保存到localStorage
  localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))

  // 关闭对话框并清空输入
  showSaveSearchDialog.value = false
  saveSearchName.value = ''
}

const loadSavedSearches = () => {
  try {
    const saved = localStorage.getItem(SAVED_SEARCHES_KEY)
    if (saved) {
      savedSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载保存的搜索失败:', error)
  }
}

// 加载项目列表数据
const loadProjectList = async () => {
  try {
    projectList.value = await fetchAllProjects()
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败，请稍后重试')
  }
}

const applySavedSearch = (savedSearch: SavedSearch) => {
  try {
    // 应用保存的搜索条件
    Object.assign(searchForm, savedSearch.searchForm)

    // 执行搜索
    handleAdvancedSearch()

    ElMessage.success(`已应用搜索: ${savedSearch.name}`)
  } catch (error) {
    console.error('应用保存的搜索失败:', error)
    ElMessage.error('应用搜索失败，请重试')
  }
}

const deleteSavedSearch = (searchId: string) => {
  const index = savedSearches.value.findIndex((search) => search.id === searchId)
  if (index > -1) {
    const searchName = savedSearches.value[index].name
    savedSearches.value.splice(index, 1)
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))
    ElMessage.success(`已删除搜索: ${searchName}`)
  }
}

const formatSavedSearchTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    return '刚刚'
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}小时前`
  } else if (diffInHours < 24 * 7) {
    return `${Math.floor(diffInHours / 24)}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const applySearchTemplate = (template: SearchTemplate) => {
  Object.assign(searchForm, template.conditions)
  handleAdvancedSearch()
}

const removeSearchTemplate = (templateId: string) => {
  const index = searchTemplates.value.findIndex((t) => t.id === templateId)
  if (index > -1) {
    searchTemplates.value.splice(index, 1)
  }
}

const exportSearchResults = () => {
  // 自动生成文件名
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-')
  exportConfig.filename = `Candidates_${timestamp}`

  // 默认选择所有可见列
  exportConfig.fields = visibleColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)

  showExportDialog.value = true
}

const handleExport = () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请至少选择一个字段进行导出')
    return
  }

  try {
    // 获取要导出的数据
    const dataToExport = getExportData()

    // 根据格式导出
    switch (exportConfig.format) {
      case 'xlsx':
        exportToExcel(dataToExport)
        break
      case 'csv':
        exportToCSV(dataToExport)
        break
      case 'json':
        exportToJSON(dataToExport)
        break
    }

    showExportDialog.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getExportData = () => {
  let sourceData: CandidateItem[] = []

  // 根据导出范围获取数据
  switch (exportConfig.scope) {
    case 'all':
      // 获取所有数据，需要重新调用API
      sourceData = candidatesList.value
      break
    case 'current':
      // 当前页面显示的数据
      sourceData = candidatesList.value
      break
    case 'filtered':
      // 当前筛选条件下的数据
      sourceData = candidatesList.value
      break
    case 'selected':
      // 这里需要实现表格选择功能，暂时用当前页数据
      sourceData = candidatesList.value
      break
  }

  // 根据选择的字段过滤数据
  const filteredData = sourceData.map((item) => {
    const exportItem: ExportItem = {}
    exportConfig.fields.forEach((field) => {
      const column = tableColumns.value.find((col) => col.key === field)
      const label = column?.label || field

      switch (field) {
        case 'social_id':
          exportItem[label] = item.social_id || ''
          break
        case 'nick_name':
          exportItem[label] = item.nick_name
          break
        case 'reply_email_addr':
          exportItem[label] = item.reply_email_addr || ''
          break
        case 'project_code':
          exportItem[label] = item.project_code
          break
        case 'platform':
          exportItem[label] = getPlatformLabel(item.platform)
          break
        case 'follow_up_status':
          exportItem[label] = getFollowUpStatusLabel(item.follow_up_status)
          break
        case 'tracker':
          exportItem[label] = item.tracker || ''
          break
        case 'follow_up_note':
          exportItem[label] = item.follow_up_note || ''
          break
        case 'note':
          exportItem[label] = item.note || ''
          break
        case 'created_at':
          exportItem[label] = formatDate(item.created_at)
          break
        case 'updated_at':
          exportItem[label] = formatDate(item.updated_at)
          break
        default:
          exportItem[label] = (item as unknown as Record<string, unknown>)[field]?.toString() || ''
      }
    })
    return exportItem
  })

  return filteredData
}

const exportToExcel = (data: ExportItem[]) => {
  // 这里可以使用 xlsx 库来导出 Excel
  // 为了演示，我们先用 CSV 的方式
  exportToCSV(data)
}

const exportToCSV = (data: ExportItem[]) => {
  if (data.length === 0) return

  const headers = Object.keys(data[0])
  let csvContent = ''

  // 添加表头
  if (exportConfig.includeHeaders) {
    csvContent += headers.join(',') + '\n'
  }

  // 添加数据行
  data.forEach((row) => {
    const values = headers.map((header) => {
      const value = row[header]
      // 处理包含逗号的值，用引号包围
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    })
    csvContent += values.join(',') + '\n'
  })

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const exportToJSON = (data: ExportItem[]) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.json`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 字段选择方法
const selectAllFields = () => {
  exportConfig.fields = tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)
}

const clearAllFields = () => {
  exportConfig.fields = []
}

// 可导出的字段选项
const exportableFields = computed(() => {
  return tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => ({
      key: col.key,
      label: col.label,
      checked: exportConfig.fields.includes(col.key),
    }))
})

// 计算属性 - 活跃的筛选条件
const hasActiveFilters = computed(() => {
  return activeFilters.value.length > 0
})

// 计算属性 - 是否有高级筛选条件（除了快速搜索）
const hasAdvancedFilters = computed(() => {
  return activeFilters.value.some((filter) => filter.key !== 'quickSearch')
})

// 计算属性 - 是否应该显示当前搜索条件卡片
const shouldShowCurrentFilters = computed(() => {
  // 只有在有筛选条件时才显示卡片
  return hasActiveFilters.value
})

const activeFilters = computed(() => {
  const filters: ActiveFilter[] = []

  if (searchForm.quickSearchSocialId) {
    filters.push({
      key: 'quickSearchSocialId',
      label: 'KOL ID',
      value: searchForm.quickSearchSocialId,
    })
  }
  if (searchForm.quickSearchEmail) {
    filters.push({
      key: 'quickSearchEmail',
      label: 'Reply Email',
      value: searchForm.quickSearchEmail,
    })
  }
  if (searchForm.quickSearchProject) {
    filters.push({
      key: 'quickSearchProject',
      label: 'Project Code',
      value: searchForm.quickSearchProject,
    })
  }
  if (searchForm.quickSearchTracker) {
    filters.push({
      key: 'quickSearchTracker',
      label: 'Tracker',
      value: searchForm.quickSearchTracker,
    })
  }

  // 动态筛选条件 - 只有在实际执行了高级搜索后才显示
  if (useAdvancedSearch.value) {
    searchForm.conditions.forEach((condition) => {
      if (condition.field && condition.operator) {
        const field = availableFields.find((f) => f.key === condition.field)
        const operator = getOperatorsForField(condition.field).find(
          (op) => op.value === condition.operator,
        )

        if (field && operator) {
          let value = ''
          if (isNoValueOperator(condition.operator)) {
            value = operator.label
          } else if (condition.value !== null && condition.value !== '') {
            if (condition.field === 'platform') {
              const platformOptions = getFieldOptions('platform')
              const option = platformOptions.find((opt) => opt.value === condition.value)
              value = option ? option.label : String(condition.value)
            } else if (condition.field === 'follow_up_status') {
              const statusOptions = getFieldOptions('follow_up_status')
              const option = statusOptions.find((opt) => opt.value === condition.value)
              value = option ? option.label : String(condition.value)
            } else if (condition.field === 'project_code') {
              const projectOptions = getFieldOptions('project_code')
              const option = projectOptions.find((opt) => opt.value === condition.value)
              value = option ? option.label : String(condition.value)
            } else {
              value = String(condition.value)
            }
          } else {
            value = String(condition.value || '')
          }

          filters.push({
            key: `condition_${condition.id}`,
            label: `${field.label} ${operator.label}`,
            value,
          })
        }
      }
    })
  }

  return filters
})

const removeFilter = (filterKey: string) => {
  // 处理快速搜索条件
  const quickSearchKeys = [
    'quickSearchSocialId',
    'quickSearchEmail',
    'quickSearchProject',
    'quickSearchTracker',
  ]
  if (quickSearchKeys.includes(filterKey)) {
    let removedLabel = ''
    switch (filterKey) {
      case 'quickSearchSocialId':
        removedLabel = 'KOL ID'
        searchForm.quickSearchSocialId = ''
        break
      case 'quickSearchEmail':
        removedLabel = 'Reply Email'
        searchForm.quickSearchEmail = ''
        break
      case 'quickSearchProject':
        removedLabel = 'Project Code'
        searchForm.quickSearchProject = ''
        break
      case 'quickSearchTracker':
        removedLabel = 'Tracker'
        searchForm.quickSearchTracker = ''
        break
    }

    console.log(`删除快速搜索条件: ${removedLabel}`)
    console.log('删除后的搜索状态:', getSearchStateDebugInfo())

    // 删除快速搜索条件后，使用专门的搜索函数确保状态正确
    handleSearchAfterFilterRemoval()
    return
  }

  // 处理高级搜索条件
  if (filterKey.startsWith('condition_')) {
    const conditionId = filterKey.replace('condition_', '')
    const index = searchForm.conditions.findIndex((c) => c.id === conditionId)
    if (index > -1) {
      searchForm.conditions.splice(index, 1)
      handleAdvancedSearch()
      return
    }
  }

  handleSearch()
}

const clearAllFilters = () => {
  // 重置搜索执行状态
  hasExecutedSearch.value = false
  isSearching.value = false
  isRemovingFilter.value = false

  handleReset()

  ElMessage.success('已清空所有搜索条件')
}

const handleView = (row: CandidateItem) => {
  selectedCandidate.value = row
  showDetailDrawer.value = true
}

const handleEditFromDrawer = (candidate: CandidateItem) => {
  // 可以在这里处理编辑事件的额外逻辑
  console.log('开始编辑 KOL:', candidate.nick_name)
}

const handleSaveFromDrawer = async (updatedCandidate: CandidateItem) => {
  try {
    // 构建更新请求参数
    const updateData: UpdateCandidateRequest = {
      nick_name: updatedCandidate.nick_name,
      social_id: updatedCandidate.social_id,
      reply_email_addr: updatedCandidate.reply_email_addr,
      platform: updatedCandidate.platform,
      follow_up_status: updatedCandidate.follow_up_status,
      follow_up_note: updatedCandidate.follow_up_note,
      tracker: updatedCandidate.tracker,
      note: updatedCandidate.note,
    }

    // 调用更新接口
    const response = await updateCandidate(updatedCandidate.id, updateData)

    // 在列表中更新对应的数据
    const index = candidatesList.value.findIndex((item) => item.id === updatedCandidate.id)
    if (index !== -1) {
      candidatesList.value[index] = { ...response }
      selectedCandidate.value = { ...response }
    }

    ElMessage.success(`${updatedCandidate.nick_name} 的信息已更新`)
  } catch (error) {
    console.error('更新候选人信息失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
}

const checkMoveAllowed = (evt: {
  draggedContext: { element: TableColumn }
  relatedContext?: { element: TableColumn }
}) => {
  const { draggedContext, relatedContext } = evt

  // 固定列不能移动
  const fixedColumns = ['selection', 'social_id', 'actions']

  // 如果拖拽的是固定列，不允许移动
  if (fixedColumns.includes(draggedContext.element.key)) {
    return false
  }

  // 如果目标位置是固定列的位置，也不允许移动
  if (relatedContext && fixedColumns.includes(relatedContext.element.key)) {
    return false
  }

  return true
}

// 工具方法

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const getPlatformLabel = (platform: PlatformType) => {
  const labels: Record<string, string> = {
    TIKTOK: 'TikTok',
    INSTAGRAM: 'Instagram',
    YOUTUBE: 'YouTube',
  }
  return platform ? labels[platform] || platform : '-'
}

const getFollowUpStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    PENDING: '待跟进',
    PROCESSING: '跟进中',
    DRAFTING: '起草中',
    COMPLETED: '已完成',
    PAID: '已付款',
    NOT_TARGET: '非目标',
    OFF: '已关闭',
  }
  return labels[status] || status
}

const getFollowUpStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'warning',
    PROCESSING: 'info',
    DRAFTING: 'info',
    COMPLETED: 'success',
    PAID: 'success',
    NOT_TARGET: 'danger',
    OFF: 'danger',
  }
  return types[status] || 'default'
}

// 监听快速搜索字段变化，重置搜索执行状态
watch(
  [
    () => searchForm.quickSearchSocialId,
    () => searchForm.quickSearchEmail,
    () => searchForm.quickSearchTracker,
    () => searchForm.quickSearchProject,
  ],
  (newValues, oldValues) => {
    // 只有在初始化完成后才处理变化
    if (!oldValues) return

    // 检查是否有字段值发生了变化（从有值变为有值，或从空值变为有值）
    const hasChanged = newValues.some((newVal, index) => {
      const oldVal = oldValues[index]
      // 只有当新值不为空，且与旧值不同时，才认为是用户主动修改
      return newVal && newVal !== oldVal
    })

    if (hasChanged && hasExecutedSearch.value && !isSearching.value && !isRemovingFilter.value) {
      console.log('快速搜索字段值发生变化，重置搜索执行状态')
      console.log('变化前:', oldValues)
      console.log('变化后:', newValues)

      // 重置搜索执行状态
      hasExecutedSearch.value = false
    }
  },
  { deep: true },
)

// 监听高级搜索条件变化，重置搜索执行状态
watch(
  () => searchForm.conditions,
  (newConditions, oldConditions) => {
    // 只有在初始化完成后才处理变化
    if (!oldConditions) return

    // 检查是否有条件发生了变化
    const hasChanged =
      newConditions.length !== oldConditions.length ||
      newConditions.some((newCondition, index) => {
        const oldCondition = oldConditions[index]
        return (
          !oldCondition ||
          newCondition.field !== oldCondition.field ||
          newCondition.operator !== oldCondition.operator ||
          newCondition.value !== oldCondition.value
        )
      })

    if (hasChanged && hasExecutedSearch.value && !isSearching.value) {
      console.log('高级搜索条件发生变化，重置搜索执行状态')

      // 重置搜索执行状态
      hasExecutedSearch.value = false
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  loadData()
  loadSavedSearches() // 加载保存的搜索
  loadProjectList() // 加载项目列表
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    tableColumns.value = JSON.parse(savedColumns)
  }
})
</script>

<style scoped>
.candidates-list-container {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

/* 容器响应式优化 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }

  .header-left {
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-right {
    justify-content: center;
    gap: 8px;
  }

  .header-right .el-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 13px;
    padding: 8px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 14px;
  }

  .candidates-list-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 18px;
  }

  .page-description {
    font-size: 12px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
    min-width: auto;
    justify-content: center;
    font-size: 12px;
    padding: 10px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 13px;
  }

  .candidates-list-container {
    padding: 12px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .page-header {
    padding: 18px 22px;
  }

  .header-right {
    gap: 10px;
  }

  .header-right .el-button {
    font-size: 13px;
    padding: 8px 14px;
  }
}

/* 平板设备优化 */
@media (max-width: 900px) and (min-width: 481px) {
  .page-header {
    padding: 16px 20px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 100px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.header-right .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header-right .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 按钮图标优化 */
.header-right .el-button .el-icon {
  transition: transform 0.3s ease;
}

.header-right .el-button:hover .el-icon {
  transform: scale(1.1);
}

.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.advanced-toggle,
.save-search {
  font-size: 14px;
  color: #667eea;
  transition: all 0.3s ease;
}

.advanced-toggle:hover,
.save-search:hover {
  color: #4c6ef5;
  transform: translateY(-1px);
}

/* 快速搜索栏样式 */
.quick-search-bar {
  margin-bottom: 16px;
}

.quick-search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.quick-search-input {
  flex: 1;
  min-width: 0;
}

.search-btn {
  flex-shrink: 0;
  min-width: 100px;
}

/* 统一的快速搜索组件样式 */
.quick-search-input {
  --el-input-border-radius: 12px;
  --el-select-border-radius: 12px;
}

/* 输入框样式 */
.quick-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 选择框样式 */
.quick-search-input :deep(.el-select__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-select__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-select__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-select__selected-item) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

.quick-search-input :deep(.el-select__placeholder) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 图标样式统一 */
.quick-search-input :deep(.el-input__prefix),
.quick-search-input :deep(.el-select__prefix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 32px;
}

.quick-search-input :deep(.el-input__prefix .el-icon),
.quick-search-input :deep(.el-select__prefix .el-icon) {
  font-size: 16px;
  color: #6b7280;
}

/* 清除按钮样式统一 */
.quick-search-input :deep(.el-input__suffix),
.quick-search-input :deep(.el-select__suffix) {
  display: flex;
  align-items: center;
  height: 32px;
}

.quick-search-input :deep(.el-input__clear),
.quick-search-input :deep(.el-select__caret) {
  font-size: 14px;
  color: #9ca3af;
}

.search-suffix {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.search-btn {
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  min-height: 48px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 搜索模板样式 */
.search-templates {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.templates-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 保存搜索相关样式 */
.saved-searches {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.saved-searches-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saved-searches-label::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #28a745;
  border-radius: 2px;
}

.saved-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.saved-search-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding-right: 24px;
}

.saved-search-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.saved-search-time {
  font-size: 11px;
  color: #6c757d;
  margin-left: 6px;
  opacity: 0.8;
}

.save-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.save-search-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.save-search-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.save-search-dialog :deep(.el-dialog__close) {
  color: white;
}

.save-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.save-search-content {
  padding: 16px 0;
}

.save-search-content .el-form-item {
  margin-bottom: 0;
}

.save-search-content .el-input {
  border-radius: 8px;
}

.save-search-content .el-input :deep(.el-input__inner) {
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
}

.save-search-content .el-input :deep(.el-input__inner):focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 高级搜索样式 */
.advanced-search {
  margin-top: 16px;
}

.advanced-form {
  background: #fafbfc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.search-section {
  margin-bottom: 24px;
}

.search-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* 范围输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

/* 搜索操作栏样式 */
.search-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.actions-left {
  display: flex;
  gap: 12px;
}

.actions-right {
  display: flex;
  gap: 8px;
}

/* 当前筛选条件样式 */
.current-filters {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.filters-label {
  font-size: 14px;
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 12px;
}

.search-mode-tag {
  margin-left: 8px;
  background: #e0f2fe;
  border-color: #0284c7;
  color: #0369a1;
}

.search-mode-tag .el-icon {
  margin-right: 4px;
}

/* 搜索结果统计样式 */
.search-stats {
  margin-top: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #0369a1;
  font-weight: 500;
}

.stats-info .el-icon {
  color: #0284c7;
  font-size: 16px;
}

.search-type {
  color: #64748b;
  font-size: 13px;
  font-weight: 400;
}

.filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-tag {
  background: white;
  border: 1px solid #c7d2fe;
  color: #4338ca;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  background: #eef2ff;
  transform: translateY(-1px);
}

.clear-all {
  color: #dc2626;
  font-size: 13px;
  padding: 4px 8px;
  margin-left: 8px;
}

.clear-all:hover {
  background: #fee2e2;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }

  .search-actions-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .actions-left,
  .actions-right {
    justify-content: center;
  }

  .range-input {
    flex-direction: column;
    gap: 4px;
  }

  .range-separator {
    display: none;
  }

  .filters-list {
    justify-content: center;
  }
}

.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-card {
  margin-bottom: 20px;
}

.table-card :deep(.el-table) {
  width: 100% !important;
  table-layout: auto;
}

/* 表格单元格样式 - 确保内容在一行内显示 */
.table-card :deep(.el-table__cell) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 8px 12px !important;
}

/* 表格行高度 */
.table-card :deep(.el-table__row) {
  height: 48px;
}

/* 表格内容样式 */
.table-card :deep(.cell) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 1.5;
}

/* 候选人信息样式 */
.candidate-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.social-id {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 其他样式 */
.text-muted {
  color: #999;
  font-style: italic;
}

.thread-id {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.nick-name {
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 互动率样式 */
.engagement-excellent {
  color: #67c23a;
  font-weight: 600;
}

.engagement-good {
  color: #409eff;
  font-weight: 600;
}

.engagement-normal {
  color: #e6a23c;
  font-weight: 600;
}

.engagement-low {
  color: #f56c6c;
  font-weight: 600;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 禁用标签区域的动画 */
.tags-container {
  transition: none !important;
}

.tags-container * {
  transition: none !important;
  animation: none !important;
}

/* 自定义标签样式 */
.custom-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 4px;
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

/* 列设置弹窗样式 */
.column-settings {
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-header span {
  font-size: 14px;
  color: #666;
}

.column-list {
  min-height: 150px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.column-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #909399;
}

.column-item:active {
  cursor: grabbing;
}

.fixed-column {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px 12px !important;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.fixed-label {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 高级搜索弹窗样式 */
.advanced-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.advanced-search-dialog :deep(.el-dialog__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.advanced-search-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 高级搜索内容区域 */
.advanced-search-content {
  background: #f8fafc;
  padding: 24px;
}

/* 动态筛选条件样式 */
.global-logic {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.logic-label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.help-icon {
  color: #9ca3af;
  cursor: help;
}

.filter-conditions {
  margin-bottom: 24px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.condition-row:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.field-select {
  width: 180px;
  flex-shrink: 0;
}

.operator-select {
  width: 140px;
  flex-shrink: 0;
}

.value-input {
  flex: 1;
  min-width: 0;
}

.value-input .el-input,
.value-input .el-input-number,
.value-input .el-date-picker,
.value-input .el-select {
  width: 100%;
}

.delete-btn {
  flex-shrink: 0;
  margin-left: 8px;
}

.add-condition {
  margin-bottom: 16px;
}

.add-btn {
  width: 100%;
  height: 48px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.add-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f0f4ff;
}

/* 搜索表单分组样式 */
.search-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.search-section:last-child {
  margin-bottom: 0;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding: 24px;
}

/* 表单项样式优化 */
.advanced-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.advanced-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.advanced-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.advanced-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.advanced-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.advanced-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.advanced-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

.advanced-form :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

/* 范围输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* 平台选项样式 */
.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  display: flex;
  gap: 12px;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .advanced-search-dialog {
    width: 95% !important;
    margin: 2.5vh auto !important;
  }

  .advanced-search-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }

  .advanced-search-dialog :deep(.el-dialog__title) {
    font-size: 18px;
  }

  .advanced-search-content {
    padding: 16px;
  }

  .section-content {
    padding: 16px;
  }

  .advanced-form :deep(.el-col) {
    margin-bottom: 16px;
  }

  .dialog-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .dialog-footer .el-button {
    flex: 1;
  }
}

/* 导出弹窗样式 */
.export-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.export-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

.export-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.export-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 16px;
}

.export-content {
  padding: 24px;
}

.export-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.form-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.file-extension {
  color: #9ca3af;
  font-size: 12px;
  padding: 0 8px;
}

.field-selection {
  margin-bottom: 0;
}

.field-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.selection-info {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selection-buttons {
  display: flex;
  gap: 8px;
}

.selection-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
  color: #409eff;
}

.selection-buttons .el-button:hover {
  color: #66b1ff;
}

.field-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background: #f9fafb;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
}

.field-item {
  margin: 0;
  font-size: 13px;
}

.field-item :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #374151;
}

.export-dialog .dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.export-dialog .dialog-footer .el-button {
  border-radius: 6px;
}

/* 搜索逻辑选项样式 */
.logic-radio-group {
  display: flex;
  flex-wrap: wrap; /* 允许内容换行 */
  gap: 16px; /* 增加间距 */
  width: 100%; /* 确保占用全部宽度 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
  min-height: 100px; /* 确保有足够的高度 */
}

.logic-radio {
  flex: 1 1 45%; /* 每个选项最多占 45% 宽度 */
  margin-right: 0 !important;
  margin-bottom: 16px !important;
  min-width: 200px; /* 最小宽度 */
  display: flex; /* 确保内容居中 */
  align-items: center; /* 垂直居中 */
}

.logic-option {
  margin-left: 0;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8fafc;
  cursor: pointer;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column; /* 描述文字在标题下方 */
  gap: 8px; /* 标题和描述间距 */
  overflow: visible; /* 防止内容被剪切 */
  text-align: center; /* 水平居中内容 */
}

.logic-option strong {
  font-size: 16px; /* 增加标题字体大小以突出 */
}

.logic-description {
  font-size: 12px;
  color: #909399;
  margin-top: 0; /* 移除顶部边距 */
  line-height: 1.4;
  text-align: center; /* 水平居中描述 */
}

/* 选中状态样式 */
.logic-radio-group :deep(.el-radio.is-checked) .logic-option {
  border-color: #409eff !important;
  background: #ecf5ff !important;
}

/* 悬停状态样式 */
.logic-radio:hover .logic-option {
  border-color: #c6d1f0;
  background: #f0f4ff;
}

/* 确保选中状态优先级高于悬停状态 */
.logic-radio-group :deep(.el-radio.is-checked):hover .logic-option {
  border-color: #409eff !important;
  background: #ecf5ff !important;
}

/* 确保对话框主体能容纳内容 */
.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto; /* 允许滚动 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .logic-radio-group {
    flex-direction: column; /* 小屏幕上垂直堆叠 */
    gap: 12px;
  }

  .logic-radio {
    flex: 1 1 100%; /* 全宽 */
    min-width: auto; /* 移除最小宽度约束 */
  }

  .logic-option {
    padding: 12px;
  }
}

/* 搜索结果统计样式 */
.result-count-tag {
  margin-left: 8px;
  font-weight: 500;
}

/* 搜索进行中状态样式 */
.searching-tag {
  margin-left: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.searching-tag .el-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 筛选条件标签行样式 */
.filters-label {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* KOL ID 列样式 */
.social-id-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.social-id {
  font-weight: 500;
  color: #1f2937;
}

/* 人工校准角标样式 */
.review-badge {
  color: #f59e0b;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.review-badge:hover {
  color: #d97706;
  transform: scale(1.1);
}
</style>
