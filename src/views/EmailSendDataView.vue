<template>
  <div class="email-send-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">邮件发送数据</h1>
        <p class="page-description">管理和监控邮件发送记录</p>
      </div>
      <div class="header-right">
        <el-button @click="exportSearchResults">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <div class="search-title">
          <el-icon><Search /></el-icon>
          <span>智能搜索</span>
        </div>
      </div>

      <!-- 快速搜索栏 -->
      <div class="quick-search-bar">
        <div class="quick-search-container">
          <!-- KOL ID搜索 -->
          <el-input
            v-model="searchForm.quickSearchKolId"
            placeholder="KOL ID"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>

          <!-- 发件人邮箱搜索 -->
          <el-input
            v-model="searchForm.quickSearchFromEmail"
            placeholder="发件人邮箱"
            clearable
            size="large"
            class="quick-search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>

          <!-- 发送状态搜索 -->
          <el-select
            v-model="searchForm.quickSearchStatus"
            placeholder="发送状态"
            clearable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><Clock /></el-icon>
            </template>
            <el-option label="待发送" value="pending" />
            <el-option label="已发送" value="sent" />
            <el-option label="发送失败" value="failed" />
          </el-select>

          <!-- 项目编码搜索 -->
          <el-select
            v-model="searchForm.quickSearchProject"
            placeholder="选择项目"
            clearable
            filterable
            size="large"
            class="quick-search-input"
          >
            <template #prefix>
              <el-icon><CollectionTag /></el-icon>
            </template>
            <el-option
              v-for="project in projectList"
              :key="project.code"
              :label="project.code"
              :value="project.code"
            >
              {{ project.code }}
            </el-option>
          </el-select>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            size="large"
            class="search-btn"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="emailSendList"
        v-loading="loading"
        stripe
        fit
        style="width: 100%; table-layout: auto"
      >
        <!-- 动态生成表格列 -->
        <template v-for="column in visibleColumns" :key="column.key">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.key === 'selection'"
            type="selection"
            :width="column.width"
            :min-width="column.minWidth"
          />

          <!-- KOL ID列 -->
          <el-table-column
            v-else-if="column.key === 'social_id'"
            :fixed="column.fixed"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <div class="kol-info">
                <span class="kol-id">{{ row.social_id }}</span>
                <div class="platform-badge">
                  <el-icon v-if="row.platform === 'TIKTOK'" color="#ff0050"><VideoPlay /></el-icon>
                  <el-icon v-else-if="row.platform === 'INSTAGRAM'" color="#e4405f"
                    ><Picture
                  /></el-icon>
                  <el-icon v-else-if="row.platform === 'YOUTUBE'" color="#ff0000"
                    ><Monitor
                  /></el-icon>
                  <span>{{ getPlatformLabel(row.platform) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 发送状态列 -->
          <el-table-column
            v-else-if="column.key === 'send_status'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.send_status)">
                <el-icon class="status-icon">
                  <Clock v-if="row.send_status === 'pending'" />
                  <SuccessFilled v-else-if="row.send_status === 'sent'" />
                  <CircleCloseFilled v-else-if="row.send_status === 'failed'" />
                </el-icon>
                {{ getStatusLabel(row.send_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 发送时间列 -->
          <el-table-column
            v-else-if="column.key === 'send_date'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            sortable
          >
            <template #default="{ row }">
              {{ row.send_date ? formatDateTime(row.send_date) : '-' }}
            </template>
          </el-table-column>

          <!-- 更新时间列 -->
          <el-table-column
            v-else-if="column.key === 'updated_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>

          <!-- 创建时间列 -->
          <el-table-column
            v-else-if="column.key === 'created_at'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-else-if="column.key === 'actions'"
            :fixed="column.fixed"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="handleView(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </template>
          </el-table-column>

          <!-- 其他普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :show-overflow-tooltip="column.showTooltip"
          />
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[100, 200, 500, 1000]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="500px">
      <div class="column-settings">
        <div class="setting-header">
          <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          <el-button size="small" @click="resetColumns">重置默认</el-button>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :move="checkMoveAllowed"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions',
              }"
            >
              <el-icon
                class="drag-handle"
                :class="{
                  disabled:
                    element.key === 'selection' ||
                    element.key === 'social_id' ||
                    element.key === 'actions',
                }"
              >
                <Rank />
              </el-icon>
              <el-checkbox
                v-model="element.visible"
                @change="saveColumnSettings"
                :disabled="
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'social_id'
                "
              >
                {{ element.label }}
              </el-checkbox>
              <span
                v-if="
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions'
                "
                class="fixed-label"
              >
                (固定)
              </span>
            </div>
          </template>
        </draggable>
      </div>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      class="export-dialog"
      append-to-body
    >
      <div class="export-content">
        <el-form :model="exportConfig" label-width="120px" class="export-form">
          <!-- 导出范围 -->
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio value="filtered">当前筛选结果</el-radio>
              <el-radio value="current">当前页面数据</el-radio>
              <el-radio value="all">全部数据</el-radio>
            </el-radio-group>
            <div class="form-hint">
              <span v-if="exportConfig.scope === 'filtered'"
                >将导出当前筛选条件下的所有 {{ getFilteredDataCount() }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'current'"
                >将导出当前页面的 {{ emailSendList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'all'">将导出全部数据（忽略筛选条件）</span>
              <span v-else>将导出选中的数据（功能开发中）</span>
            </div>
          </el-form-item>

          <!-- 导出格式 -->
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportConfig.format">
              <el-radio value="xlsx">Excel 文件 (.xlsx)</el-radio>
              <el-radio value="csv">CSV 文件 (.csv)</el-radio>
              <el-radio value="json">JSON 文件 (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件名设置 -->
          <el-form-item label="文件名">
            <el-input
              v-model="exportConfig.filename"
              placeholder="请输入文件名"
              maxlength="50"
              show-word-limit
            >
              <template #suffix>
                <span class="file-extension">.{{ exportConfig.format }}</span>
              </template>
            </el-input>
          </el-form-item>

          <!-- 其他选项 -->
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportConfig.includeHeaders">包含表头</el-checkbox>
          </el-form-item>

          <!-- 选择字段 -->
          <el-form-item label="导出字段" class="field-selection">
            <div class="field-selection-header">
              <span class="selection-info"
                >已选择 {{ exportConfig.fields.length }} / {{ exportableFields.length }} 项</span
              >
              <div class="selection-buttons">
                <el-button type="text" size="small" @click="selectAllFields">全选</el-button>
                <el-button type="text" size="small" @click="clearAllFields">全不选</el-button>
              </div>
            </div>
            <div class="field-list">
              <el-checkbox-group v-model="exportConfig.fields">
                <div class="field-grid">
                  <el-checkbox
                    v-for="field in exportableFields"
                    :key="field.key"
                    :value="field.key"
                    class="field-item"
                  >
                    {{ field.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :disabled="exportConfig.fields.length === 0"
          >
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邮件发送详情抽屉 -->
    <EmailSendDetailDrawer
      v-model="showDetailDrawer"
      :email-send-data="selectedEmailSend"
      @save="handleSaveFromDrawer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  VideoPlay,
  Picture,
  Monitor,
  Setting,
  Rank,
  View,
  Download,
  User,
  Message,
  Clock,
  SuccessFilled,
  CircleCloseFilled,
  CollectionTag,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  searchEmailSends,
  transformEmailSendData,
  type EmailSendSearch,
  type EmailSendListParams,
} from '../services/emailSendApi'
import { fetchAllProjects, type Project } from '../services/projectApi'

// 类型定义 - 前端使用的格式（经过转换后）
interface EmailSendItem {
  id: number
  platform: 'tiktok' | 'instagram' | 'youtube' | null
  social_id: string
  send_status: 'pending' | 'sent' | 'failed'
  send_date: string | null
  project_code: string
  template_code: string
  from_email: string | null
  to_email: string | null
  note: string | null
  created_at: string
  updated_at: string
}

interface SearchForm {
  // 快速搜索
  quickSearchKolId: string
  quickSearchFromEmail: string
  quickSearchStatus: string
  quickSearchProject: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  minWidth?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
}

interface ExportConfig {
  fields: string[]
  format: 'xlsx' | 'csv' | 'json'
  scope: 'all' | 'current' | 'filtered' | 'selected'
  includeHeaders: boolean
  filename: string
}

interface ExportItem {
  [key: string]: string | number
}

// 响应式数据
const loading = ref(false)
const emailSendList = ref<EmailSendItem[]>([])
const showDetailDrawer = ref(false)
const selectedEmailSend = ref<EmailSendItem | null>(null)
const showColumnSettings = ref(false)

// 搜索相关数据
const showExportDialog = ref(false)

// 项目列表
const projectList = ref<Project[]>([])

// 导出相关数据
const exportConfig = reactive<ExportConfig>({
  fields: [],
  format: 'xlsx',
  scope: 'filtered',
  includeHeaders: true,
  filename: '',
})

const searchForm = reactive<SearchForm>({
  quickSearchKolId: '',
  quickSearchFromEmail: '',
  quickSearchStatus: '',
  quickSearchProject: '',
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 100,
  total: 0,
})

// 默认列配置
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true },
  {
    key: 'social_id',
    prop: 'social_id',
    label: 'KOL ID',
    minWidth: 180,
    visible: true,
    fixed: true,
  },
  { key: 'send_status', prop: 'send_status', label: '发送状态', width: 120, visible: true },
  { key: 'send_date', prop: 'send_date', label: '发送时间', minWidth: 170, visible: true },
  { key: 'project_code', prop: 'project_code', label: '项目编码', minWidth: 120, visible: true },
  { key: 'template_code', prop: 'template_code', label: '模板编码', minWidth: 120, visible: true },
  { key: 'to_email', prop: 'to_email', label: '收件人邮箱', minWidth: 200, visible: true },
  { key: 'from_email', prop: 'from_email', label: '发件人邮箱', minWidth: 200, visible: true },
  { key: 'note', prop: 'note', label: '备注', minWidth: 200, visible: true, showTooltip: true },
  { key: 'created_at', prop: 'created_at', label: '创建时间', minWidth: 170, visible: true },
  { key: 'updated_at', prop: 'updated_at', label: '更新时间', minWidth: 170, visible: true },
  { key: 'actions', label: '操作', width: 100, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// 计算可见的列
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => column.visible)
})

// 本地存储键名
const COLUMN_SETTINGS_KEY = 'email-send-column-settings'

// 移除模拟数据，现在使用真实API

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 构建搜索参数
    const searchParams: EmailSendSearch = {}

    // 添加搜索条件
    if (searchForm.quickSearchKolId) {
      searchParams.social_id = searchForm.quickSearchKolId
    }
    if (searchForm.quickSearchFromEmail) {
      searchParams.from_email = searchForm.quickSearchFromEmail
    }
    if (searchForm.quickSearchStatus) {
      searchParams.status = searchForm.quickSearchStatus.toUpperCase() as
        | 'PENDING'
        | 'SENT'
        | 'FAILED'
    }
    if (searchForm.quickSearchProject) {
      searchParams.project_code = searchForm.quickSearchProject
    }

    // 构建列表参数
    const listParams: EmailSendListParams = {
      skip: (pagination.currentPage - 1) * pagination.pageSize,
      limit: pagination.pageSize,
    }

    // 调用搜索API
    const response = await searchEmailSends(searchParams, listParams)

    // 转换数据格式
    emailSendList.value = response.items.map(transformEmailSendData)
    pagination.total = response.total
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const projects = await fetchAllProjects()
    projectList.value = projects
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 获取筛选后的数据数量 - 现在直接从API获取，不需要本地过滤
const getFilteredDataCount = () => {
  return pagination.total
}

const exportSearchResults = () => {
  // 自动生成文件名
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-')
  exportConfig.filename = `邮件发送数据_${timestamp}`

  // 默认选择所有可见列
  exportConfig.fields = visibleColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)

  showExportDialog.value = true
}

const handleExport = () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请至少选择一个字段进行导出')
    return
  }

  try {
    // 获取要导出的数据
    const dataToExport = getExportData()

    // 根据格式导出
    switch (exportConfig.format) {
      case 'xlsx':
        exportToExcel(dataToExport)
        break
      case 'csv':
        exportToCSV(dataToExport)
        break
      case 'json':
        exportToJSON(dataToExport)
        break
    }

    showExportDialog.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getExportData = () => {
  let sourceData: EmailSendItem[] = []

  // 根据导出范围获取数据
  switch (exportConfig.scope) {
    case 'all':
      // 对于全部数据，需要重新调用API获取所有数据
      sourceData = emailSendList.value // 暂时使用当前数据，实际应该调用API获取全部
      break
    case 'current':
      sourceData = emailSendList.value
      break
    case 'filtered':
      sourceData = emailSendList.value // 当前显示的就是筛选后的数据
      break
    case 'selected':
      sourceData = emailSendList.value
      break
  }

  // 根据选择的字段过滤数据
  const filteredData = sourceData.map((item) => {
    const exportItem: ExportItem = {}
    exportConfig.fields.forEach((field) => {
      const column = tableColumns.value.find((col) => col.key === field)
      const label = column?.label || field

      switch (field) {
        case 'social_id':
          exportItem[label] = item.social_id
          break
        case 'platform':
          exportItem[label] = getPlatformLabel(item.platform)
          break
        case 'send_status':
          exportItem[label] = getStatusLabel(item.send_status)
          break
        case 'send_date':
          exportItem[label] = item.send_date ? formatDateTime(item.send_date) : ''
          break
        case 'project_code':
          exportItem[label] = item.project_code || ''
          break
        case 'template_code':
          exportItem[label] = item.template_code || ''
          break
        case 'from_email':
          exportItem[label] = item.from_email || ''
          break
        case 'to_email':
          exportItem[label] = item.to_email || ''
          break
        case 'note':
          exportItem[label] = item.note || ''
          break
        case 'created_at':
          exportItem[label] = formatDateTime(item.created_at)
          break
        case 'updated_at':
          exportItem[label] = formatDateTime(item.updated_at)
          break
        default:
          exportItem[label] = (item as unknown as Record<string, unknown>)[field]?.toString() || ''
      }
    })
    return exportItem
  })

  return filteredData
}

const exportToExcel = (data: ExportItem[]) => {
  // 这里可以使用 xlsx 库来导出 Excel
  // 为了演示，我们先用 CSV 的方式
  exportToCSV(data)
}

const exportToCSV = (data: ExportItem[]) => {
  if (data.length === 0) return

  const headers = Object.keys(data[0])
  let csvContent = ''

  // 添加表头
  if (exportConfig.includeHeaders) {
    csvContent += headers.join(',') + '\n'
  }

  // 添加数据行
  data.forEach((row) => {
    const values = headers.map((header) => {
      const value = row[header]
      // 处理包含逗号的值，用引号包围
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    })
    csvContent += values.join(',') + '\n'
  })

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const exportToJSON = (data: ExportItem[]) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.json`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 字段选择方法
const selectAllFields = () => {
  exportConfig.fields = tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)
}

const clearAllFields = () => {
  exportConfig.fields = []
}

// 可导出的字段选项
const exportableFields = computed(() => {
  return tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => ({
      key: col.key,
      label: col.label,
      checked: exportConfig.fields.includes(col.key),
    }))
})

const handleView = (row: EmailSendItem) => {
  selectedEmailSend.value = row
  showDetailDrawer.value = true
}

const handleSaveFromDrawer = (updatedEmailSend: EmailSendItem) => {
  // 在列表中更新对应的数据
  const index = emailSendList.value.findIndex((item) => item.id === updatedEmailSend.id)
  if (index !== -1) {
    emailSendList.value[index] = { ...updatedEmailSend }
    selectedEmailSend.value = { ...updatedEmailSend }
  }
  ElMessage.success(`${updatedEmailSend.social_id} 的邮件记录已更新`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
}

const checkMoveAllowed = (evt: {
  draggedContext: { element: TableColumn }
  relatedContext?: { element: TableColumn }
}) => {
  const { draggedContext, relatedContext } = evt

  // 固定列不能移动
  const fixedColumns = ['selection', 'social_id', 'actions']

  // 如果拖拽的是固定列，不允许移动
  if (fixedColumns.includes(draggedContext.element.key)) {
    return false
  }

  // 如果目标位置是固定列的位置，也不允许移动
  if (relatedContext && fixedColumns.includes(relatedContext.element.key)) {
    return false
  }

  return true
}

// 工具方法
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getPlatformLabel = (platform: string | null) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
  }
  return labels[platform] || platform
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待发送',
    sent: '已发送',
    failed: '发送失败',
  }
  return labels[status] || status
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    sent: 'success',
    failed: 'danger',
  }
  return types[status] || ''
}

// 生命周期
onMounted(() => {
  loadData()
  loadProjectList()
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    tableColumns.value = JSON.parse(savedColumns)
  }
})
</script>

<style scoped>
.email-send-container {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

/* 容器响应式优化 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }

  .header-left {
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-right {
    justify-content: center;
    gap: 8px;
  }

  .header-right .el-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 13px;
    padding: 8px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 14px;
  }

  .email-send-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 18px;
  }

  .page-description {
    font-size: 12px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
    min-width: auto;
    justify-content: center;
    font-size: 12px;
    padding: 10px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 13px;
  }

  .email-send-container {
    padding: 12px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .page-header {
    padding: 18px 22px;
  }

  .header-right {
    gap: 10px;
  }

  .header-right .el-button {
    font-size: 13px;
    padding: 8px 14px;
  }
}

/* 平板设备优化 */
@media (max-width: 900px) and (min-width: 481px) {
  .page-header {
    padding: 16px 20px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 100px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.header-right .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header-right .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 按钮图标优化 */
.header-right .el-button .el-icon {
  transition: transform 0.3s ease;
}

.header-right .el-button:hover .el-icon {
  transform: scale(1.1);
}

.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.advanced-toggle,
.save-search {
  font-size: 14px;
  color: #667eea;
  transition: all 0.3s ease;
}

.advanced-toggle:hover,
.save-search:hover {
  color: #4c6ef5;
  transform: translateY(-1px);
}

/* 快速搜索栏样式 */
.quick-search-bar {
  margin-bottom: 16px;
}

.quick-search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.quick-search-input {
  flex: 1;
  min-width: 0;
}

.search-btn {
  flex-shrink: 0;
  min-width: 100px;
}

/* 统一的快速搜索组件样式 */
.quick-search-input {
  --el-input-border-radius: 12px;
  --el-select-border-radius: 12px;
}

/* 输入框样式 */
.quick-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 选择框样式 */
.quick-search-input :deep(.el-select__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 8px 16px;
  min-height: 48px;
  box-sizing: border-box;
}

.quick-search-input :deep(.el-select__wrapper:hover) {
  border-color: #667eea;
}

.quick-search-input :deep(.el-select__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quick-search-input :deep(.el-select__selected-item) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

.quick-search-input :deep(.el-select__placeholder) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 图标样式统一 */
.quick-search-input :deep(.el-input__prefix),
.quick-search-input :deep(.el-select__prefix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 32px;
}

.quick-search-input :deep(.el-input__prefix .el-icon),
.quick-search-input :deep(.el-select__prefix .el-icon) {
  font-size: 16px;
  color: #6b7280;
}

/* 清除按钮样式统一 */
.quick-search-input :deep(.el-input__suffix),
.quick-search-input :deep(.el-select__suffix) {
  display: flex;
  align-items: center;
  height: 32px;
}

.quick-search-input :deep(.el-input__clear),
.quick-search-input :deep(.el-select__caret) {
  font-size: 14px;
  color: #9ca3af;
}

.search-suffix {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.search-btn {
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  min-height: 48px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 搜索模板样式 */
.search-templates {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.templates-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 保存搜索相关样式 */
.saved-searches {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.saved-searches-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saved-searches-label::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #28a745;
  border-radius: 2px;
}

.saved-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.saved-search-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding-right: 24px;
}

.saved-search-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.saved-search-time {
  font-size: 11px;
  color: #6c757d;
  margin-left: 4px;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }

  .quick-search-container {
    flex-direction: column;
    gap: 8px;
  }

  .quick-search-input {
    width: 100%;
  }

  .search-btn {
    width: 100%;
    min-width: auto;
  }

  .filters-list {
    justify-content: center;
  }

  .filters-label {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .quick-search-container {
    gap: 6px;
  }

  .search-btn {
    padding: 6px 16px;
    font-size: 13px;
  }
}

.table-card {
  margin-bottom: 20px;
}

.table-card :deep(.el-table) {
  width: 100% !important;
  table-layout: auto;
}

/* KOL 信息样式 */
.kol-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.kol-id {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

/* 发送状态样式 */
.status-icon {
  margin-right: 4px;
}

/* 发送状态样式 */
.status-icon {
  margin-right: 4px;
}

.status-pending {
  color: #e6a23c;
}

.status-sent {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 禁用标签区域的动画 */
.tags-container {
  transition: none !important;
}

.tags-container * {
  transition: none !important;
  animation: none !important;
}

/* 自定义标签样式 */
.custom-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 4px;
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

/* 列设置弹窗样式 */
.column-settings {
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-header span {
  font-size: 14px;
  color: #666;
}

.column-list {
  min-height: 150px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.column-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #909399;
}

.column-item:active {
  cursor: grabbing;
}

.fixed-column {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px 12px !important;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.fixed-label {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 导出弹窗样式 */
.export-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.export-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

.export-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.export-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 16px;
}

.export-content {
  padding: 24px;
}

.export-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.form-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.file-extension {
  color: #9ca3af;
  font-size: 12px;
  padding: 0 8px;
}

.field-selection {
  margin-bottom: 0;
}

.field-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.selection-info {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selection-buttons {
  display: flex;
  gap: 8px;
}

.selection-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
  color: #409eff;
}

.selection-buttons .el-button:hover {
  color: #66b1ff;
}

.field-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background: #f9fafb;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
}

.field-item {
  margin: 0;
  font-size: 13px;
}

.field-item :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #374151;
}

.export-dialog .dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.export-dialog .dialog-footer .el-button {
  border-radius: 6px;
}
</style>
