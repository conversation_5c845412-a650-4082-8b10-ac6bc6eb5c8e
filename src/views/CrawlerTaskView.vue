<template>
  <div class="crawler-task-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">爬虫任务管理</h1>
        <p class="page-description">创建和管理数据采集任务（支持 Modash 和 TTOne 数据源）</p>
      </div>
      <!-- 移除header-right按钮区 -->
    </div>

    <!-- 任务统计信息 -->
    <el-skeleton :loading="statsLoading" animated>
      <template #template>
        <div class="task-stats-row">
          <el-card class="stat-card total"
            ><div class="stat-label">总任务数</div>
            <el-skeleton-item variant="text" style="width: 60px; height: 32px; margin: 0 auto"
          /></el-card>
          <el-card class="stat-card pending"
            ><div class="stat-label">待执行</div>
            <el-skeleton-item variant="text" style="width: 60px; height: 32px; margin: 0 auto"
          /></el-card>
          <el-card class="stat-card running"
            ><div class="stat-label">执行中</div>
            <el-skeleton-item variant="text" style="width: 60px; height: 32px; margin: 0 auto"
          /></el-card>
          <el-card class="stat-card completed"
            ><div class="stat-label">已完成</div>
            <el-skeleton-item variant="text" style="width: 60px; height: 32px; margin: 0 auto"
          /></el-card>
          <el-card class="stat-card failed"
            ><div class="stat-label">失败</div>
            <el-skeleton-item variant="text" style="width: 60px; height: 32px; margin: 0 auto"
          /></el-card>
        </div>
      </template>
      <template #default>
        <div class="task-stats-row">
          <el-card class="stat-card total">
            <div class="stat-label">总任务数</div>
            <div class="stat-value">{{ stats.total }}</div>
          </el-card>
          <el-card class="stat-card pending">
            <div class="stat-label">待执行</div>
            <div class="stat-value">{{ stats.pending }}</div>
          </el-card>
          <el-card class="stat-card running">
            <div class="stat-label">执行中</div>
            <div class="stat-value">{{ stats.running }}</div>
          </el-card>
          <el-card class="stat-card completed">
            <div class="stat-label">已完成</div>
            <div class="stat-value">{{ stats.completed }}</div>
          </el-card>
          <el-card class="stat-card failed">
            <div class="stat-label">失败</div>
            <div class="stat-value">{{ stats.failed }}</div>
          </el-card>
        </div>
      </template>
    </el-skeleton>

    <!-- 任务列表展示区域 -->
    <el-card class="task-list-card" shadow="never">
      <template #header>
        <div class="list-header">
          <div class="header-title">
            <el-icon><List /></el-icon>
            <span>任务列表</span>
          </div>
          <div
            class="header-actions"
            style="display: flex; flex-direction: row; gap: 12px; align-items: center"
          >
            <el-button @click="refreshTasks">
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              创建任务
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <div class="filter-header">
          <el-icon><Filter /></el-icon>
          <span>筛选条件</span>
          <span class="filter-tip">请选择筛选条件后点击搜索按钮</span>
        </div>
        <div class="filter-content">
          <el-row :gutter="16">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="filter-item">
                <label class="filter-label">任务名称</label>
                <el-input
                  v-model="searchKeyword"
                  placeholder="请输入任务名称"
                  clearable
                  size="default"
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="4">
              <div class="filter-item">
                <label class="filter-label">平台</label>
                <el-select
                  v-model="searchParams.platform"
                  placeholder="选择平台"
                  size="default"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <el-option label="TikTok" value="TIKTOK" />
                  <el-option label="Instagram" value="INSTAGRAM" />
                  <el-option label="YouTube" value="YOUTUBE" />
                </el-select>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="4">
              <div class="filter-item">
                <label class="filter-label">状态</label>
                <el-select
                  v-model="searchParams.status"
                  placeholder="选择状态"
                  size="default"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <el-option label="待执行" value="PENDING" />
                  <el-option label="执行中" value="RUNNING" />
                  <el-option label="已完成" value="COMPLETED" />
                  <el-option label="失败" value="FAILED" />
                  <el-option label="已暂停" value="PAUSED" />
                </el-select>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="filter-item">
                <label class="filter-label">项目代码</label>
                <el-select
                  v-model="searchParams.project_code"
                  placeholder="请选择项目"
                  size="default"
                  clearable
                  filterable
                  @keyup.enter="handleSearch"
                >
                  <el-option
                    v-for="project in projects"
                    :key="project.code"
                    :label="project.code"
                    :value="project.code"
                  >
                    {{ project.code }}
                  </el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="4">
              <div class="filter-item">
                <label class="filter-label">&nbsp;</label>
                <div class="filter-actions">
                  <el-button @click="handleSearch" type="primary" size="default">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="clearSearch" size="default" plain>
                    <el-icon><Refresh /></el-icon>
                    清空
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 任务表格 -->
      <el-table :data="taskList" v-loading="loading" stripe style="width: 100%">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="taskName" label="任务名称" min-width="220" fixed>
          <template #default="{ row }">
            <div class="task-name-cell">
              <span class="task-name">{{ row.taskName }}</span>
              <el-tag v-if="row.autoStart" size="small" type="success" effect="plain">自动</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="platform" label="平台" width="110">
          <template #default="{ row }">
            <el-tag
              type="info"
              disable-transitions
              size="small"
              style="font-weight: 600; letter-spacing: 1px; background: #f4f6fa; border: none"
            >
              <template v-if="row.platform === 'TIKTOK'">
                <el-icon style="color: #ff0050; margin-right: 2px"><VideoPlay /></el-icon> TikTok
              </template>
              <template v-else-if="row.platform === 'INSTAGRAM'">
                <el-icon style="color: #e4405f; margin-right: 2px"><Picture /></el-icon> Instagram
              </template>
              <template v-else-if="row.platform === 'YOUTUBE'">
                <el-icon style="color: #ff0000; margin-right: 2px"><Monitor /></el-icon> YouTube
              </template>
              <template v-else>
                {{ row.platform }}
              </template>
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="dataSource" label="数据源" width="95">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getDataSourceLabel(row.dataSource) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="projectCode" label="项目代码" width="150">
          <template #default="{ row }">
            <span class="project-code">{{ row.projectCode || '未配置' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="90">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="progress" label="进度" width="100">
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.status)"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="duration" label="耗时" width="85">
          <template #default="{ row }">
            <span class="duration">{{ formatDuration(row.duration) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="140">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column prop="updatedAt" label="更新时间" width="140">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'RUNNING'"
              link
              type="danger"
              size="small"
              @click="cancelTask(row)"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>

            <el-button
              v-if="row.status === 'FAILED'"
              link
              type="warning"
              size="small"
              @click="retryTask(row)"
            >
              <el-icon><RefreshLeft /></el-icon>
              重试
            </el-button>

            <el-button link type="primary" size="small" @click="viewTaskDetail(row)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>

            <el-button link type="danger" size="small" @click="deleteTask(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 个任务</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 30, 50]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务详情抽屉 -->
    <el-drawer
      v-model="showDetailDrawer"
      :title="null"
      direction="rtl"
      size="50%"
      class="task-detail-drawer"
    >
      <div v-if="selectedTask" class="task-header">
        <div class="platform-icon">
          <el-icon :size="32" color="#6b7280">
            <Monitor />
          </el-icon>
        </div>
        <div class="header-main">
          <div class="task-id-row">
            <span class="task-id">{{ selectedTask.taskName }}</span>
            <el-tag :type="getStatusType(selectedTask.status)" class="status-tag">
              {{ getStatusLabel(selectedTask.status) }}
            </el-tag>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            v-if="selectedTask.status === 'RUNNING'"
            type="danger"
            @click="cancelTask(selectedTask)"
            size="large"
            class="control-main-button"
          >
            <el-icon><Close /></el-icon>
            取消任务
          </el-button>
        </div>
      </div>

      <div v-if="selectedTask" class="content-scroll-area">
        <!-- 核心数据摘要卡片 -->
        <el-card class="summary-card" shadow="hover">
          <div class="summary-grid">
            <div v-for="item in summaryList" :key="item.key" class="summary-item">
              <el-icon :size="28" :style="{ color: item.color }">
                <component :is="item.icon" />
              </el-icon>
              <div class="summary-value">{{ item.value }}</div>
              <div class="summary-label">{{ item.label }}</div>
            </div>
          </div>
        </el-card>

        <!-- 基本信息卡片 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions
            :column="2"
            border
            size="large"
            :label-style="{ width: '120px', textAlign: 'right' }"
          >
            <el-descriptions-item label="任务名称" :span="2">
              {{ selectedTask.taskName }}
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusType(selectedTask.status)" size="large">
                {{ getStatusLabel(selectedTask.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="目标平台">
              <div class="platform-display">
                <span>{{ selectedTask.platform }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="数据来源">
              <el-tag type="info" size="large">
                {{ getDataSourceLabel(selectedTask.dataSource) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="项目代码">
              {{ selectedTask.projectCode || '未配置' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 配置信息卡片 -->
        <el-card class="config-card" shadow="hover">
          <template #header>
            <h3>配置信息</h3>
          </template>
          <el-tabs v-model="activeConfigTab" class="config-tabs">
            <el-tab-pane label="Cookie 配置" name="cookie">
              <div class="config-content-wrapper">
                <div class="config-header">
                  <el-icon><Key /></el-icon>
                  <span>平台认证 Cookie</span>
                </div>
                <pre class="config-content">{{ selectedTask.cookies || '未配置' }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane label="请求 Body" name="body">
              <div class="config-content-wrapper">
                <div class="config-header">
                  <el-icon><Document /></el-icon>
                  <span>API 请求体</span>
                </div>
                <pre class="config-content">{{
                  JSON.stringify(selectedTask.filters, null, 2) || '未配置'
                }}</pre>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 执行日志卡片 -->
        <el-card class="logs-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>执行日志</h3>
              <el-button type="primary" size="small" plain @click="refreshLogs">
                刷新日志
              </el-button>
            </div>
          </template>
          <div class="log-container">
            <div class="log-content">
              <pre class="log-message">{{ selectedTask.logMsg || '暂无日志信息' }}</pre>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>

    <!-- 创建任务弹窗 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建爬虫任务"
      width="1000px"
      class="create-task-dialog"
      append-to-body
      :before-close="handleCreateDialogClose"
    >
      <div class="create-form-container" v-loading="creating">
        <el-form
          :model="taskForm"
          :rules="taskRules"
          ref="taskFormRef"
          label-width="120px"
          class="create-form"
        >
          <!-- 基本配置 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Setting /></el-icon>
                基本配置
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="任务名称" prop="taskName" size="large">
                    <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" clearable />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="目标平台" prop="platform" size="large">
                    <el-select
                      v-model="taskForm.platform"
                      placeholder="选择平台"
                      style="width: 100%"
                    >
                      <el-option label="TikTok" value="TIKTOK">
                        <div class="platform-option">
                          <el-icon color="#ff0050"><VideoPlay /></el-icon>
                          <span>TikTok</span>
                        </div>
                      </el-option>
                      <el-option label="Instagram" value="INSTAGRAM">
                        <div class="platform-option">
                          <el-icon color="#e4405f"><Picture /></el-icon>
                          <span>Instagram</span>
                        </div>
                      </el-option>
                      <el-option label="YouTube" value="YOUTUBE">
                        <div class="platform-option">
                          <el-icon color="#ff0000"><Monitor /></el-icon>
                          <span>YouTube</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="数据来源" prop="dataSource" size="large">
                    <el-select
                      v-model="taskForm.dataSource"
                      placeholder="选择数据源"
                      style="width: 100%"
                    >
                      <el-option label="Modash" value="MODASH">
                        <div class="source-option">
                          <el-icon color="#4f46e5"><DataAnalysis /></el-icon>
                          <span>Modash</span>
                        </div>
                      </el-option>
                      <el-option label="TTOne" value="TTONE">
                        <div class="source-option">
                          <el-icon color="#ff6b35"><TrendCharts /></el-icon>
                          <span>TTOne</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="项目代码" prop="projectCode" size="large">
                    <el-select
                      v-model="taskForm.projectCode"
                      placeholder="请选择项目"
                      style="width: 100%"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="project in projects"
                        :key="project.code"
                        :label="project.code"
                        :value="project.code"
                      >
                        {{ project.code }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 认证配置 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Key /></el-icon>
                认证配置
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="平台 Cookie" prop="platformCookie" size="large">
                    <el-input
                      v-model="taskForm.platformCookie"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入平台的 Cookie 信息"
                    />
                    <div class="form-tip">
                      <el-icon><InfoFilled /></el-icon>
                      包含身份验证的完整 Cookie 信息
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="请求 Body" prop="requestBody" size="large">
                    <el-input
                      v-model="taskForm.requestBody"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入请求体数据 (JSON 格式)"
                    />
                    <div class="form-tip">
                      <el-icon><InfoFilled /></el-icon>
                      API 请求的 JSON 格式数据
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="resetForm" :disabled="creating">
              <el-icon><RefreshLeft /></el-icon>
              重置表单
            </el-button>
            <el-button @click="saveAsDraft" :disabled="creating">
              <el-icon><Document /></el-icon>
              保存草稿
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="showCreateDialog = false" :disabled="creating">取消</el-button>
            <el-button type="primary" @click="createTaskFromDialog" :loading="creating">
              <el-icon><Position /></el-icon>
              创建任务
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  VideoPlay,
  Picture,
  Monitor,
  Setting,
  List,
  View,
  Delete,
  Refresh,
  Position,
  DataAnalysis,
  TrendCharts,
  Key,
  InfoFilled,
  RefreshLeft,
  Document,
  Calendar,
  Filter,
  Close,
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import {
  fetchCrawlerTasks,
  searchCrawlerTasks,
  cancelCrawlerTask,
  deleteCrawlerTask,
  createCrawlerTask,
  fetchCrawlerTaskStats,
} from '../services/crawlerTaskApi'
import type {
  CrawlerTask,
  SearchParams,
  CreateTaskRequest,
  CreateTaskResponse,
  CrawlerTaskStats,
} from '../services/crawlerTaskApi'
import { fetchAllProjects } from '../services/projectApi'
import type { Project } from '../services/projectApi'

// 类型定义见 services/crawlerTaskApi.ts
interface TaskForm {
  taskName: string
  platform: string
  dataSource: string
  platformCookie: string
  requestBody: string
  projectCode: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showDetailDrawer = ref(false)
const showCreateDialog = ref(false)
const selectedTask = ref<CrawlerTask | null>(null)
const searchKeyword = ref('')
const activeConfigTab = ref('cookie')
const ws = ref<WebSocket | null>(null)
const wsConnected = ref(false)
const taskWsMap = new Map<number, WebSocket>()
const stats = ref<CrawlerTaskStats>({ total: 0, pending: 0, running: 0, completed: 0, failed: 0 })
const statsLoading = ref(false)
const projects = ref<Project[]>([])

function connectTaskWebSocket(taskId: number | string) {
  disconnectTaskWebSocket()
  const wsUrl = `ws://172.20.10.207:8000/api/v1/ws/tasks/${taskId}`
  ws.value = new WebSocket(wsUrl)
  ws.value.onopen = () => {
    wsConnected.value = true
    console.log(`[WebSocket] 连接成功: ${wsUrl}`)
    ws.value?.send(JSON.stringify({ type: 'get_status' }))
  }
  ws.value.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      console.log('[WebSocket] 收到消息:', data)
      if (selectedTask.value && data.task_id === selectedTask.value.id) {
        selectedTask.value = {
          ...selectedTask.value,
          progress:
            typeof data.task_progress === 'number'
              ? data.task_progress
              : selectedTask.value.progress,
          status: typeof data.status === 'string' ? data.status : selectedTask.value.status,
          logMsg: typeof data.log_msg === 'string' ? data.log_msg : selectedTask.value.logMsg,
          duration:
            typeof data.total_duration === 'number'
              ? data.total_duration
              : selectedTask.value.duration,
        }
      }
    } catch (err) {
      console.warn('[WebSocket] 消息解析失败', event.data, err)
    }
  }
  ws.value.onclose = (event) => {
    wsConnected.value = false
    ws.value = null
    console.log('[WebSocket] 连接关闭', event)
  }
  ws.value.onerror = (event) => {
    wsConnected.value = false
    ws.value = null
    console.error('[WebSocket] 连接出错', event)
  }
}

function disconnectTaskWebSocket() {
  if (ws.value) {
    ws.value.close()
    ws.value = null
    wsConnected.value = false
  }
}

watch(
  () => showDetailDrawer.value,
  (open) => {
    if (open && selectedTask.value) {
      connectTaskWebSocket(selectedTask.value.id)
    } else {
      disconnectTaskWebSocket()
    }
  },
)

onUnmounted(() => {
  disconnectTaskWebSocket()
  for (const ws of taskWsMap.values()) ws.close()
  taskWsMap.clear()
})

// 搜索参数
const searchParams = reactive<SearchParams>({
  task_name: '',
  platform: '',
  status: '',
  project_code: '',
})

const taskFormRef = ref<FormInstance>()

// 任务表单
const taskForm = reactive<TaskForm>({
  taskName: '',
  platform: '',
  dataSource: '',
  platformCookie: '',
  requestBody: '',
  projectCode: '',
})

// 分页
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 验证规则
const taskRules: FormRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择目标平台', trigger: 'change' }],
  dataSource: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
  projectCode: [{ required: true, message: '请输入项目代码', trigger: 'blur' }],
  platformCookie: [{ required: true, message: '请输入平台 Cookie', trigger: 'blur' }],
  requestBody: [{ required: true, message: '请输入请求 Body', trigger: 'blur' }],
}

// 模拟任务数据
const taskList = ref<CrawlerTask[]>([])

function connectTaskRowWebSocket(task: CrawlerTask) {
  const wsUrl = `ws://172.20.10.207:8000/api/v1/ws/tasks/${task.id}`
  const ws = new WebSocket(wsUrl)
  taskWsMap.set(task.id, ws)

  ws.onopen = () => {
    ws.send(JSON.stringify({ type: 'get_status' }))
    console.log(`[RowWS] 连接成功: ${wsUrl}`)
  }
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      const t = taskList.value.find((t) => t.id === data.task_id)
      if (t) {
        t.progress = typeof data.task_progress === 'number' ? data.task_progress : t.progress
        t.status = typeof data.status === 'string' ? data.status : t.status
        t.duration = typeof data.total_duration === 'number' ? data.total_duration : t.duration
        t.logMsg = typeof data.log_msg === 'string' ? data.log_msg : t.logMsg
        if (['COMPLETED', 'FAILED', 'CANCELLED', 'PAUSED'].includes(t.status)) {
          ws.close()
          taskWsMap.delete(t.id)
        }
      }
    } catch (err) {
      console.warn('[RowWS] 消息解析失败', event.data, err)
    }
  }
  ws.onclose = (event) => {
    console.log('[RowWS] 连接关闭', event)
    taskWsMap.delete(task.id)
  }
  ws.onerror = (event) => {
    console.error('[RowWS] 连接出错', event)
    taskWsMap.delete(task.id)
  }
}

function setupTaskWebSockets() {
  // 断开所有旧连接
  for (const ws of taskWsMap.values()) ws.close()
  taskWsMap.clear()
  for (const task of taskList.value) {
    if (!['COMPLETED', 'FAILED', 'CANCELLED', 'PAUSED'].includes(task.status)) {
      connectTaskRowWebSocket(task)
    }
  }
}

// 加载任务数据
const loadTasks = async () => {
  loading.value = true
  try {
    // 过滤掉空值的搜索参数
    const filteredSearchParams: SearchParams = {}
    if (searchParams.task_name?.trim()) {
      filteredSearchParams.task_name = searchParams.task_name.trim()
    }
    if (searchParams.platform?.trim()) {
      filteredSearchParams.platform = searchParams.platform.trim()
    }
    if (searchParams.status?.trim()) {
      filteredSearchParams.status = searchParams.status.trim()
    }
    if (searchParams.project_code?.trim()) {
      filteredSearchParams.project_code = searchParams.project_code.trim()
    }

    // 检查是否有搜索条件
    const hasSearchParams = Object.keys(filteredSearchParams).length > 0

    let result
    if (hasSearchParams) {
      // 使用搜索接口
      result = await searchCrawlerTasks(filteredSearchParams, {
        skip: (pagination.currentPage - 1) * pagination.pageSize,
        limit: pagination.pageSize,
      })
    } else {
      // 使用普通列表接口
      result = await fetchCrawlerTasks({
        skip: (pagination.currentPage - 1) * pagination.pageSize,
        limit: pagination.pageSize,
      })
    }

    taskList.value = result.items
    pagination.total = result.total
    setupTaskWebSockets()
  } catch (error) {
    console.error('任务列表加载失败:', error)
    // 检查是否是后端不可用的错误
    if (
      error instanceof Error &&
      (error.message.includes('Network Error') ||
        error.message.includes('ERR_NETWORK') ||
        error.message === 'BACKEND_UNAVAILABLE')
    ) {
      console.warn('后端服务不可用，使用空数据继续渲染页面')
      // 静默处理，确保页面仍然可以正常显示
      taskList.value = []
      pagination.total = 0
    } else {
      ElMessage.error('任务列表加载失败')
    }
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  statsLoading.value = true
  try {
    stats.value = await fetchCrawlerTaskStats()
  } catch (error) {
    console.warn('统计数据加载失败，但不影响页面使用:', error)
    // 静默处理统计数据加载失败，不影响页面正常使用
  } finally {
    statsLoading.value = false
  }
}

// 加载项目列表
const loadProjects = async () => {
  try {
    projects.value = await fetchAllProjects()
  } catch (error) {
    console.warn('加载项目列表失败，但不影响页面使用:', error)
    // 静默处理项目列表加载失败
  }
}

// 清空搜索条件
const clearSearch = () => {
  searchKeyword.value = ''
  Object.assign(searchParams, {
    task_name: '',
    platform: '',
    status: '',
    project_code: '',
  })
  pagination.currentPage = 1
  loadTasks()
  ElMessage.success('搜索条件已清空')
}

// 生命周期加载
onMounted(() => {
  loadStats()
  loadTasks()
  loadProjects()
  pagination.total = taskList.value.length
})

// 监听分页变化自动加载
watch([() => pagination.currentPage, () => pagination.pageSize], loadTasks)

// 核心数据摘要
const summaryList = computed(() => {
  if (!selectedTask.value) return []
  return [
    {
      key: 'progress',
      label: '完成进度',
      value: `${selectedTask.value.progress}%`,
      icon: TrendCharts,
      color: '#409eff',
    },
    {
      key: 'duration',
      label: '执行时长',
      value: formatDuration(selectedTask.value.duration),
      icon: DataAnalysis,
      color: '#67c23a',
    },
    {
      key: 'created',
      label: '创建时间',
      value: formatDate(selectedTask.value.createdAt).split(' ')[0], // 只显示日期
      icon: Calendar,
      color: '#e6a23c',
    },
    {
      key: 'updated',
      label: '最后更新',
      value: formatDate(selectedTask.value.updatedAt).split(' ')[0], // 只显示日期
      icon: Refresh,
      color: '#f56c6c',
    },
  ]
})

// 方法
const resetForm = () => {
  taskFormRef.value?.resetFields()
  Object.assign(taskForm, {
    taskName: '',
    platform: '',
    dataSource: '',
    platformCookie: '',
    requestBody: '',
    projectCode: '',
  })
}

const saveAsDraft = () => {
  ElMessage.success('草稿已保存')
}

const createTaskFromDialog = async () => {
  if (!taskFormRef.value) return

  try {
    await taskFormRef.value.validate()
    creating.value = true

    // 构建创建任务的请求数据
    const taskData: CreateTaskRequest = {
      task_name: taskForm.taskName,
      source: taskForm.dataSource,
      platform: taskForm.platform,
      project_code: taskForm.projectCode,
      filters: taskForm.requestBody ? JSON.parse(taskForm.requestBody) : {},
      cookies: taskForm.platformCookie || '',
    }

    // 调用创建任务API
    const response: CreateTaskResponse = await createCrawlerTask(taskData)

    // 创建新任务对象并添加到列表
    const newTask: CrawlerTask = {
      id: response.task_id,
      taskName: taskForm.taskName,
      platform: taskForm.platform,
      dataSource: taskForm.dataSource,
      status: response.status,
      progress: 0,
      duration: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      cookies: taskForm.platformCookie,
      projectCode: taskForm.projectCode,
      filters: taskForm.requestBody ? JSON.parse(taskForm.requestBody) : {},
      logMsg: '',
    }

    // 添加到任务列表开头
    taskList.value.unshift(newTask)
    pagination.total = taskList.value.length

    ElMessage.success(response.message || `任务 "${taskForm.taskName}" 创建成功`)
    showCreateDialog.value = false
    resetForm()
    setupTaskWebSockets()
    refreshStatsAndTasks() // 在创建任务后刷新统计
  } catch (error) {
    console.error('创建任务失败:', error)
    if (error instanceof Error) {
      ElMessage.error(`创建任务失败: ${error.message}`)
    } else {
      ElMessage.error('创建任务失败')
    }
  } finally {
    creating.value = false
  }
}

const handleCreateDialogClose = () => {
  if (creating.value) {
    ElMessage.warning('任务创建中，请稍候...')
    return false
  }
  showCreateDialog.value = false
}

const cancelTask = async (task: CrawlerTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.taskName}" 吗？此操作不可撤销。`,
      '取消确认',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用取消任务API
    await cancelCrawlerTask(task.id)

    // 更新本地状态
    task.status = 'CANCELLED'
    ElMessage.success(`任务 "${task.taskName}" 已取消`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const deleteTask = async (task: CrawlerTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.taskName}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用删除任务API
    await deleteCrawlerTask(task.id)

    // 从本地列表中移除
    const index = taskList.value.findIndex((t) => t.id === task.id)
    if (index > -1) {
      taskList.value.splice(index, 1)
      pagination.total = taskList.value.length
      ElMessage.success('任务删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const retryTask = async (task: CrawlerTask) => {
  try {
    await ElMessageBox.confirm(`确定要重试任务 "${task.taskName}" 吗？`, '重试确认', {
      confirmButtonText: '确定重试',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 构建重试任务的请求数据
    const taskData: CreateTaskRequest = {
      task_name: `${task.taskName}_retry_${Date.now()}`,
      source: task.dataSource,
      platform: task.platform,
      project_code: task.projectCode || '',
      filters: task.filters || {},
      cookies: task.cookies || '',
    }

    // 调用创建任务API
    const response: CreateTaskResponse = await createCrawlerTask(taskData)

    // 创建新任务对象并添加到列表
    const newTask: CrawlerTask = {
      id: response.task_id,
      taskName: taskData.task_name,
      platform: task.platform,
      dataSource: task.dataSource,
      status: response.status,
      progress: 0,
      duration: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      cookies: task.cookies,
      projectCode: task.projectCode,
      filters: task.filters,
      logMsg: '',
    }

    // 添加到任务列表开头
    taskList.value.unshift(newTask)
    pagination.total = taskList.value.length

    ElMessage.success(`任务 "${taskData.task_name}" 重试成功`)
    setupTaskWebSockets()
    refreshStatsAndTasks() // 在重试任务后刷新统计
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试任务失败:', error)
      if (error instanceof Error) {
        ElMessage.error(`重试任务失败: ${error.message}`)
      } else {
        ElMessage.error('重试任务失败')
      }
    }
  }
}

const viewTaskDetail = (task: CrawlerTask) => {
  selectedTask.value = task
  showDetailDrawer.value = true
}

const refreshTasks = () => {
  loadStats()
  loadTasks()
  ElMessage.success('列表已刷新')
}

const handleSearch = () => {
  // 将搜索关键词同步到搜索参数中
  searchParams.task_name = searchKeyword.value
  pagination.currentPage = 1
  loadTasks()

  // 检查是否有搜索条件
  const hasSearchParams =
    searchParams.task_name ||
    searchParams.platform ||
    searchParams.status ||
    searchParams.project_code
  if (hasSearchParams) {
    ElMessage.success('搜索完成')
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 工具函数
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    PENDING: '待执行',
    RUNNING: '执行中',
    COMPLETED: '已完成',
    FAILED: '失败',
    CANCELLED: '已取消',
    PAUSED: '已暂停',
  }
  return labels[status] || status
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'info',
    RUNNING: 'warning',
    COMPLETED: 'success',
    FAILED: 'danger',
    PAUSED: '',
    CANCELLED: '',
  }
  return types[status] || ''
}

const getDataSourceLabel = (dataSource: string) => {
  const labels: Record<string, string> = {
    MODASH: 'Modash',
    TTONE: 'TTOne',
  }
  return labels[dataSource] || dataSource
}

const getProgressColor = (status: string) => {
  const colors: Record<string, string> = {
    RUNNING: '#409eff',
    COMPLETED: '#67c23a',
    FAILED: '#f56c6c',
    PAUSED: '#e6a23c',
    CANCELLED: '#909399',
  }
  return colors[status] || '#909399'
}

const formatDuration = (seconds: number | null | undefined) => {
  if (seconds === null || seconds === undefined || seconds === 0) {
    return '未结束'
  }
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const refreshLogs = () => {
  ElMessage.success('日志已刷新')
}

// 在任务操作后自动刷新统计
const refreshStatsAndTasks = () => {
  loadStats()
  loadTasks()
}

// 生命周期
onMounted(() => {
  pagination.total = taskList.value.length
})
</script>

<style scoped>
.crawler-task-container {
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 任务列表样式 */
.task-list-card {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.count-tag {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格单元格样式 */
.task-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  font-weight: 500;
  color: #333;
}

.platform-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 32px;
}

.records-count,
.emails-count {
  font-weight: 500;
  color: #333;
}

.duration {
  font-size: 13px;
  color: #666;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 任务详情抽屉样式 */
.task-detail-drawer :deep(.el-drawer) {
  border-radius: 12px 0 0 12px;
  background: #f8fafc;
}

.task-detail-drawer :deep(.el-drawer__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  margin-bottom: 0;
  border-radius: 12px 0 0 0;
}

.task-detail-drawer :deep(.el-drawer__body) {
  padding: 0;
  background: #f8fafc;
}

/* 抽屉头部 */
.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 32px;
  background: #f8fafc;
  color: #1f2937;
  border-radius: 12px 0 0 0;
  min-height: 60px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.platform-icon {
  width: 48px;
  height: 48px;
  min-width: 32px;
  min-height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  box-sizing: border-box;
}

.header-main {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-id-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.task-id {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.status-tag {
  font-size: 12px;
  font-weight: 600;
  border-radius: 6px;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  color: #6366f1;
  padding: 4px 8px;
}

.sub-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 15px;
  color: #6b7280;
}

.header-actions {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 160px;
}

.control-main-button {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #6366f1;
  font-weight: 600;
  border-radius: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.control-main-button:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.06);
}

/* 内容滚动区域 */
.content-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px 32px 32px;
}

/* 各个区块的间距 */
.content-scroll-area > div {
  margin-bottom: 20px;
}

.content-scroll-area > div:last-child {
  margin-bottom: 0;
}

/* 卡片通用样式 */
.el-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

/* 核心数据摘要卡片 */
.summary-card {
  margin-bottom: 20px;
}

.summary-grid {
  display: flex;
  gap: 32px;
  justify-content: space-between;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.summary-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 基本信息卡片 */
.info-card {
  margin-bottom: 20px;
}

.info-card :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #374151;
  width: 120px;
  text-align: right;
}

.info-card :deep(.el-descriptions__content) {
  text-align: left;
}

.platform-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  width: fit-content;
}

/* 数据指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-progress {
  width: 100%;
}

.metric-item.progress .metric-content {
  width: 100%;
}

/* 配置信息卡片 */
.config-card {
  margin-bottom: 20px;
}

.config-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #f8fafc;
  border-radius: 8px 8px 0 0;
}

.config-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 16px;
}

.config-tabs :deep(.el-tabs__content) {
  padding: 20px;
}

.config-content-wrapper {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.config-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.config-header .el-icon {
  color: #667eea;
}

.config-content {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  border: 1px solid #e5e7eb;
  color: #374151;
  max-height: 200px;
  overflow-y: auto;
}

.template-info {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.template-item:last-child {
  border-bottom: none;
}

.template-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.template-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

/* 日志信息卡片 */
.logs-card {
  margin-bottom: 20px;
}

.log-container {
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.log-content {
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  color: #374151;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
  background: #ffffff;
  transition: background-color 0.2s ease;
}

.log-item:hover {
  background: #f8fafc;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6b7280;
  font-family: monospace;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
}

.log-message {
  color: #374151;
  flex: 1;
  line-height: 1.4;
}

.log-empty {
  padding: 40px 20px;
}

/* 创建任务弹窗样式 */
.create-task-dialog :deep(.el-dialog) {
  border-radius: 12px;
  max-height: 90vh;
  overflow: hidden;
}

.create-task-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

.create-task-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.create-task-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 16px;
}

.create-task-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.create-form-container {
  padding: 24px;
  background: #f8fafc;
}

.create-form {
  background: white;
  border-radius: 8px;
  padding: 24px;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding-top: 16px;
}

/* 表单项样式优化 */
.create-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.create-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.create-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.create-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.create-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.create-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

/* 平台和数据源选项样式 */
.platform-option,
.source-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.template-name {
  font-weight: 500;
  color: #333;
}

.template-desc {
  font-size: 12px;
  color: #666;
}

.execution-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 表单提示样式 */
.form-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
}

.form-tip .el-icon {
  color: #9ca3af;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 20px;
}

/* 筛选条件区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.filter-header .el-icon {
  color: #667eea;
  font-size: 18px;
}

.filter-tip {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
  margin-left: auto;
}

.filter-content {
  /* No specific styles needed here, as the layout is handled by el-row and el-col */
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 13px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 4px;
}

.filter-actions {
  display: flex;
  align-items: flex-end;
  height: 100%;
  gap: 8px;
}

.filter-actions .el-button {
  flex: 1;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-detail-drawer {
    --el-drawer-size: 80% !important;
  }

  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filter-section .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .task-detail-drawer {
    --el-drawer-size: 100% !important;
  }

  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    min-height: auto;
  }

  .content-scroll-area {
    padding: 16px 20px 24px;
  }

  .summary-grid {
    flex-direction: column;
    gap: 16px;
  }

  .task-id-row {
    font-size: 18px;
    width: 100%;
    justify-content: space-between;
  }

  .header-actions {
    width: 100%;
    align-items: stretch;
  }

  .control-main-button {
    width: 100%;
  }

  .filter-section {
    padding: 16px;
  }

  .filter-section .el-col {
    width: 100% !important;
    margin-bottom: 12px;
  }

  .filter-item {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  .filter-label {
    min-width: 80px;
    margin-bottom: 0;
  }

  .filter-actions {
    align-items: center;
  }
  .platform-icon {
    width: 40px;
    height: 40px;
    min-width: 28px;
    min-height: 28px;
    margin-right: 12px;
  }
}

@media (max-width: 480px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .metric-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metric-icon {
    margin: 0 auto;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    padding: 10px 12px;
  }

  .log-time {
    min-width: auto;
    font-size: 11px;
  }
}

.project-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.task-stats-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}
.stat-card {
  flex: 1;
  text-align: center;
  padding: 16px 0;
}
.stat-label {
  font-size: 14px;
  color: #888;
  margin-bottom: 6px;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
}
.stat-card.pending .stat-value {
  color: #409eff;
}
.stat-card.running .stat-value {
  color: #e6a23c;
}
.stat-card.completed .stat-value {
  color: #67c23a;
}
.stat-card.failed .stat-value {
  color: #f56c6c;
}
.stat-card.total .stat-value {
  color: #6366f1;
}
.project-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.project-name {
  font-weight: 500;
  color: #333;
}

.project-code {
  font-size: 12px;
  color: #666;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
