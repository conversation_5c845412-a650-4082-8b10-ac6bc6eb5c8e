<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">控制台</h1>
        <p class="page-description">欢迎回来，{{ userEmail }}！</p>
      </div>
      <div class="header-right">
        <el-button type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon kol-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">1,234</h3>
            <p class="stat-label">总红人数</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon project-icon">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">89</h3>
            <p class="stat-label">进行中项目</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon revenue-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">¥2.5M</h3>
            <p class="stat-label">本月收入</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon growth-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <h3 class="stat-number">+15.3%</h3>
            <p class="stat-label">增长率</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-left">
        <!-- 最近活动 -->
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button text type="primary">查看全部</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-avatar">
                <el-avatar :size="40">{{ activity.user.charAt(0) }}</el-avatar>
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.text }}</p>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <div class="content-right">
        <!-- 快速操作 -->
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button class="action-btn" @click="navigateTo('/kol/list')">
              <el-icon><UserFilled /></el-icon>
              <span>红人管理</span>
            </el-button>
            <el-button class="action-btn" @click="navigateTo('/cooperation/projects')">
              <el-icon><Folder /></el-icon>
              <span>合作项目</span>
            </el-button>
            <el-button class="action-btn" @click="navigateTo('/analytics/overview')">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据分析</span>
            </el-button>
            <el-button class="action-btn" @click="navigateTo('/system/users')">
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </el-button>
          </div>
        </el-card>

        <!-- 系统状态 -->
        <el-card class="status-card">
          <template #header>
            <span>系统状态</span>
          </template>
          <div class="status-list">
            <div class="status-item">
              <span class="status-label">服务器状态</span>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">数据库连接</span>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">缓存服务</span>
              <el-tag type="warning">警告</el-tag>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Refresh,
  UserFilled,
  Folder,
  Money,
  TrendCharts,
  DataAnalysis,
  Setting,
} from '@element-plus/icons-vue'

// 类型定义
interface Activity {
  id: number
  user: string
  text: string
  time: string
}

// 响应式数据
const router = useRouter()
const userEmail = ref('')

// 最近活动数据
const recentActivities = ref<Activity[]>([
  {
    id: 1,
    user: '李美美',
    text: '更新了个人资料信息',
    time: '2 分钟前',
  },
  {
    id: 2,
    user: '时尚达人小王',
    text: '完成了新的合作项目',
    time: '15 分钟前',
  },
  {
    id: 3,
    user: '科技小哥',
    text: '上传了新的作品集',
    time: '1 小时前',
  },
])

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

// 生命周期
onMounted(() => {
  // 获取用户信息
  userEmail.value = localStorage.getItem('userEmail') || '管理员'
})
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.kol-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.project-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.growth-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0 0 4px 0;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 活动卡片 */
.activity-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #333;
  margin: 0 0 4px 0;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

/* 快速操作卡片 */
.quick-actions-card {
  border: 1px solid #e4e7ed;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border: 1px solid #e4e7ed;
  background: #fafafa;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.action-btn span {
  font-size: 12px;
}

/* 系统状态卡片 */
.status-card {
  border: 1px solid #e4e7ed;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 14px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
