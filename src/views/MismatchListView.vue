<template>
  <div class="kol-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">Mismatch</h1>
        <p class="page-description">管理和查看 KOL 红人信息数据不匹配情况</p>
      </div>
      <div class="header-right">
        <!-- 批量 Mismatch 按钮（带角标） -->
        <div class="mismatch-button-wrapper">
          <el-button @click="handleMismatch">
            <el-icon><DataAnalysis /></el-icon>
            批量 Mismatch
          </el-button>
          <!-- 任务数量角标（带悬停详情） -->
          <el-popover
            v-if="mismatchTaskCount > 0"
            placement="bottom-end"
            :width="320"
            trigger="hover"
            popper-class="mismatch-badge-popover"
          >
            <template #reference>
              <el-badge :value="mismatchTaskCount" class="mismatch-task-badge" />
            </template>

            <!-- 悬停时显示的任务详情 -->
            <div class="badge-task-details">
              <div class="badge-details-header">
                <span>正在进行的 Mismatch 任务</span>
                <el-tag size="small" type="primary">{{ mismatchTaskCount }}</el-tag>
              </div>

              <div class="badge-details-list">
                <div v-for="[taskId, task] in mismatchTasks" :key="taskId" class="badge-task-item">
                  <div class="badge-task-header">
                    <span class="badge-task-id">任务 {{ taskId.substring(0, 8) }}...</span>
                    <span class="badge-task-status" :class="getTaskStatusClass(task.status)">
                      {{ getTaskStatusText(task.status) }}
                    </span>
                  </div>

                  <div class="badge-task-progress">
                    <el-progress
                      :percentage="Math.round((task.processed_count / task.total_count) * 100)"
                      :status="task.status === 'failed' ? 'exception' : undefined"
                      :stroke-width="4"
                      :show-text="false"
                    />
                    <span class="badge-progress-text">
                      {{ task.processed_count }}/{{ task.total_count }}
                      <span v-if="task.success_count > 0" class="success-count">
                        (成功: {{ task.success_count }})
                      </span>
                      <span v-if="task.failed_count > 0" class="failed-count">
                        (失败: {{ task.failed_count }})
                      </span>
                    </span>
                  </div>
                </div>
              </div>

              <div class="badge-details-footer">
                <el-text size="small" type="info"> 点击"批量 Mismatch"按钮可启动新任务 </el-text>
              </div>
            </div>
          </el-popover>
        </div>
        <!-- 批量 Nano 按钮（带角标） -->
        <div class="nano-button-wrapper">
          <el-button @click="handleNano">
            <el-icon><CollectionTag /></el-icon>
            批量 Nano
          </el-button>
          <!-- Nano 任务数量角标（带悬停详情） -->
          <el-popover
            v-if="nanoTaskCount > 0"
            placement="bottom-end"
            :width="320"
            trigger="hover"
            popper-class="nano-badge-popover"
          >
            <template #reference>
              <el-badge :value="nanoTaskCount" class="nano-task-badge" />
            </template>

            <!-- 悬停时显示的任务详情 -->
            <div class="badge-task-details">
              <div class="badge-details-header">
                <span>正在进行的 Nano 任务</span>
                <el-tag size="small" type="primary">{{ nanoTaskCount }}</el-tag>
              </div>

              <div class="badge-details-list">
                <div v-for="[taskId, task] in nanoTasks" :key="taskId" class="badge-task-item">
                  <div class="badge-task-header">
                    <span class="badge-task-id">任务 {{ taskId.substring(0, 8) }}...</span>
                    <span class="badge-task-status" :class="getTaskStatusClass(task.status)">
                      {{ getTaskStatusText(task.status) }}
                    </span>
                  </div>

                  <div class="badge-task-progress">
                    <el-progress
                      :percentage="Math.round((task.processed_count / task.total_count) * 100)"
                      :status="task.status === 'failed' ? 'exception' : undefined"
                      :stroke-width="4"
                      :show-text="false"
                    />
                    <span class="badge-progress-text">
                      {{ task.processed_count }}/{{ task.total_count }}
                      <span v-if="task.success_count > 0" class="success-count">
                        (成功: {{ task.success_count }})
                      </span>
                      <span v-if="task.failed_count > 0" class="failed-count">
                        (失败: {{ task.failed_count }})
                      </span>
                    </span>
                  </div>
                </div>
              </div>

              <div class="badge-details-footer">
                <el-text size="small" type="info"> 点击"批量 Nano"按钮可启动新任务 </el-text>
              </div>
            </div>
          </el-popover>
        </div>
        <el-button @click="exportSearchResults">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showColumnSettings = true">
          <el-icon><Setting /></el-icon>
          列设置
        </el-button>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <div class="search-title">
          <el-icon><Search /></el-icon>
          <span>智能搜索</span>
        </div>
        <div class="search-actions">
          <el-button link @click="openAdvancedSearchDialog" class="advanced-toggle">
            <el-icon><Operation /></el-icon>
            高级搜索
          </el-button>
          <el-button link @click="openSaveSearchDialog" class="save-search">
            <el-icon><Star /></el-icon>
            保存搜索
          </el-button>
        </div>
      </div>

      <!-- 搜索历史和模板 -->
      <div class="search-templates" v-if="searchTemplates.length > 0">
        <div class="templates-label">快速搜索模板：</div>
        <div class="templates-list">
          <el-tag
            v-for="template in searchTemplates"
            :key="template.id"
            class="template-tag"
            type="info"
            effect="plain"
            @click="applySearchTemplate(template)"
            closable
            @close="removeSearchTemplate(template.id)"
          >
            {{ template.name }}
          </el-tag>
        </div>
      </div>

      <!-- 已保存的搜索 -->
      <div class="saved-searches" v-if="savedSearches.length > 0">
        <div class="saved-searches-label">已保存的搜索：</div>
        <div class="saved-searches-list">
          <el-tag
            v-for="savedSearch in savedSearches"
            :key="savedSearch.id"
            class="saved-search-tag"
            type="success"
            effect="plain"
            @click="applySavedSearch(savedSearch)"
            closable
            @close="deleteSavedSearch(savedSearch.id)"
          >
            {{ savedSearch.name }}
            <span class="saved-search-time">{{
              formatSavedSearchTime(savedSearch.createdAt)
            }}</span>
          </el-tag>
        </div>
      </div>

      <!-- 当前搜索条件显示 - 只在高级搜索模式下显示 -->
      <div class="current-filters" v-if="shouldShowCurrentFilters">
        <div class="filters-label">
          当前筛选条件：
          <el-tag
            v-if="hasAdvancedFilters"
            :type="searchForm.searchLogic === 'and' ? 'warning' : 'success'"
            size="small"
            effect="plain"
            class="logic-tag"
          >
            {{ searchForm.searchLogic === 'and' ? 'AND（同时满足）' : 'OR（满足任一）' }}
          </el-tag>

          <!-- 搜索结果统计 - 显示在同一行 -->
          <el-tag
            v-if="hasExecutedSearch && !isSearching && !loading"
            type="info"
            size="small"
            effect="plain"
            class="result-count-tag"
          >
            找到 {{ pagination.total }} 条结果
          </el-tag>

          <!-- 搜索进行中状态 - 显示在同一行 -->
          <el-tag
            v-if="isSearching || (hasExecutedSearch && loading)"
            type="warning"
            size="small"
            effect="plain"
            class="searching-tag"
          >
            <el-icon class="is-loading"><Loading /></el-icon>
            正在搜索...
          </el-tag>
        </div>
        <div class="filters-list">
          <el-tag
            v-for="filter in activeFilters"
            :key="filter.key"
            closable
            @close="removeFilter(filter.key)"
            class="filter-tag"
          >
            {{ filter.label }}: {{ filter.value }}
          </el-tag>
          <el-button link @click="clearAllFilters" class="clear-all">
            <el-icon><Delete /></el-icon>
            清空所有
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearchDialog"
      title="设置筛选条件"
      width="800px"
      class="advanced-search-dialog"
      :before-close="handleAdvancedSearchClose"
      append-to-body
    >
      <div class="advanced-search-content" v-loading="loading">
        <!-- 全局逻辑设置 -->
        <div class="global-logic">
          <span class="logic-label">符合以下所有条件</span>
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </div>

        <!-- 动态筛选条件 -->
        <div class="filter-conditions">
          <div
            v-for="(condition, index) in searchForm.conditions"
            :key="condition.id"
            class="condition-row"
          >
            <!-- 字段选择 -->
            <el-select
              v-model="condition.field"
              placeholder="选择字段"
              size="large"
              class="field-select"
              @change="handleFieldChange(index)"
            >
              <el-option
                v-for="field in availableFields"
                :key="field.key"
                :label="field.label"
                :value="field.key"
              />
            </el-select>

            <!-- 操作符选择 -->
            <el-select
              v-model="condition.operator"
              placeholder="选择操作符"
              size="large"
              class="operator-select"
              @change="handleOperatorChange(index)"
            >
              <el-option
                v-for="operator in getOperatorsForField(condition.field)"
                :key="operator.value"
                :label="operator.label"
                :value="operator.value"
              />
            </el-select>

            <!-- 值输入 -->
            <div class="value-input">
              <!-- 文本输入 -->
              <el-input
                v-if="
                  getFieldType(condition.field) === 'text' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="
                  getFieldType(condition.field) === 'number' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                :min="getFieldMin(condition.field)"
                :max="getFieldMax(condition.field)"
                :precision="getFieldPrecision(condition.field)"
                size="large"
                :controls="false"
                style="width: 100%"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="
                  getFieldType(condition.field) === 'date' && !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                type="date"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />

              <!-- 选择框 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'select' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in getFieldOptions(condition.field)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 布尔选择 -->
              <el-select
                v-else-if="
                  getFieldType(condition.field) === 'boolean' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
                style="width: 100%"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>

              <!-- 范围数字输入 -->
              <el-input-number
                v-else-if="
                  getFieldType(condition.field) === 'range' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                :min="getFieldMin(condition.field)"
                :max="getFieldMax(condition.field)"
                :precision="getFieldPrecision(condition.field)"
                size="large"
                :controls="false"
                style="width: 100%"
              />

              <!-- 数组输入 -->
              <el-input
                v-else-if="
                  (getFieldType(condition.field) === 'array' ||
                    getFieldType(condition.field) === 'fuzzy_array') &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                clearable
              />

              <!-- 日期范围输入 -->
              <el-date-picker
                v-else-if="
                  getFieldType(condition.field) === 'date_range' &&
                  !isNoValueOperator(condition.operator)
                "
                v-model="condition.value"
                type="date"
                :placeholder="getFieldPlaceholder(condition.field)"
                size="large"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>

            <!-- 删除按钮 -->
            <el-button
              type="danger"
              :icon="Close"
              circle
              size="large"
              class="delete-btn"
              @click="removeCondition(index)"
            />
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div class="add-condition">
          <el-button type="primary" :icon="Plus" @click="addCondition" class="add-btn">
            添加条件
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="handleAdvancedSearchClose">取消</el-button>
          </div>
          <div class="footer-right">
            <el-button @click="clearAdvancedSearch">清空</el-button>
            <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- Mismatch 弹框 -->
    <el-dialog
      v-model="showMismatchDialog"
      title="Mismatch 评估"
      width="600px"
      class="mismatch-dialog"
      :before-close="handleMismatchDialogClose"
      append-to-body
    >
      <div class="mismatch-content" v-loading="mismatchLoading">
        <!-- 处理信息 -->
        <div class="mismatch-info">
          <el-alert
            :title="`即将对 ${mismatchTarget.length} 条记录进行 Mismatch 评估`"
            type="info"
            :closable="false"
            show-icon
          >
            <template #title>
              <div class="mismatch-title">
                <span>即将对 {{ mismatchTarget.length }} 条记录进行 Mismatch 评估</span>
                <el-tag
                  v-if="mismatchMode === 'single'"
                  type="primary"
                  size="small"
                  style="margin-left: 8px"
                >
                  单个处理
                </el-tag>
                <el-tag
                  v-else-if="selectedRows.length > 0"
                  type="success"
                  size="small"
                  style="margin-left: 8px"
                >
                  已选择 {{ selectedRows.length }} 条
                </el-tag>
                <el-tag v-else type="warning" size="small" style="margin-left: 8px">
                  处理全部数据
                </el-tag>
              </div>
            </template>
            <template #default>
              <div class="mismatch-target-list">
                <div v-for="item in mismatchTarget.slice(0, 3)" :key="item.id" class="target-item">
                  <el-icon><User /></el-icon>
                  <span>{{ item.social_id }}</span>
                  <el-tag size="small" :type="getPlatformType(item.platform)">
                    {{ getPlatformLabel(item.platform) }}
                  </el-tag>
                </div>
                <div v-if="mismatchTarget.length > 3" class="target-more">
                  还有 {{ mismatchTarget.length - 3 }} 条记录...
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- Prompt 输入 -->
        <div class="prompt-section">
          <div class="section-title">
            <el-icon><Edit /></el-icon>
            <span>评估提示词</span>
          </div>
          <div class="prompt-input-wrapper">
            <el-input
              v-model="mismatchPrompt"
              type="textarea"
              :rows="6"
              placeholder="请输入评估提示词，用于AI评估KOL的匹配度..."
              :maxlength="null"
              show-word-limit
              resize="vertical"
              class="prompt-textarea"
            />
            <div class="prompt-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>提示：您可以描述具体的评估标准、匹配条件或特殊要求</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="handleMismatchDialogClose">取消</el-button>
          </div>
          <div class="footer-right">
            <el-button
              type="primary"
              @click="confirmMismatch"
              :loading="mismatchLoading"
              :disabled="!mismatchPrompt.trim()"
            >
              确认 Mismatch ({{ mismatchTarget.length }} 条)
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-container">
        <el-table
          :data="kolList"
          v-loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <!-- 动态生成表格列 -->
          <template v-for="column in visibleColumns" :key="column.key">
            <!-- 选择列 -->
            <el-table-column
              v-if="column.key === 'selection'"
              type="selection"
              :width="column.width"
            />

            <!-- KOL ID列 -->
            <el-table-column
              v-else-if="column.key === 'social_id'"
              :fixed="column.fixed"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <div class="kol-info">
                  <el-tooltip
                    v-if="getPlatformUrl(row.platform, row.social_id)"
                    :content="getPlatformUrl(row.platform, row.social_id)"
                    placement="top"
                    :show-after="200"
                  >
                    <span
                      class="nick-name clickable"
                      @click="openPlatformUrl(row.platform, row.social_id)"
                    >
                      {{ row.social_id || '-' }}
                    </span>
                  </el-tooltip>
                  <span v-else class="nick-name">{{ row.social_id || '-' }}</span>
                  <div class="platform-badge">
                    <el-icon v-if="row.platform === 'TIKTOK'" color="#ff0050"
                      ><VideoPlay
                    /></el-icon>
                    <el-icon v-else-if="row.platform === 'INSTAGRAM'" color="#e4405f"
                      ><Picture
                    /></el-icon>
                    <el-icon v-else-if="row.platform === 'YOUTUBE'" color="#ff0000"
                      ><Monitor
                    /></el-icon>
                    <span>{{ getPlatformLabel(row.platform) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 分级列 -->
            <el-table-column
              v-else-if="column.key === 'tier'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tag :type="getTierType(row.tier)">{{ getTierLabel(row.tier) }}</el-tag>
              </template>
            </el-table-column>

            <!-- 平台列 -->
            <el-table-column
              v-else-if="column.key === 'platform'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tag :type="getPlatformType(row.platform)">{{
                  getPlatformLabel(row.platform)
                }}</el-tag>
              </template>
            </el-table-column>

            <!-- 粉丝数列 -->
            <el-table-column
              v-else-if="column.key === 'followers_count'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatNumber(row.followers_count) }}
              </template>
            </el-table-column>

            <!-- 互动率列 -->
            <el-table-column
              v-else-if="column.key === 'engagement_rate'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                <span
                  v-if="row.engagement_rate && parseFloat(row.engagement_rate) > 0"
                  :class="getEngagementClass(parseFloat(row.engagement_rate) * 100)"
                >
                  {{ (parseFloat(row.engagement_rate) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 平均观看数列 -->
            <el-table-column
              v-else-if="column.key === 'mean_views_k'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatViewsNumber(row.mean_views_k) }}
              </template>
            </el-table-column>

            <!-- 中位数观看数列 -->
            <el-table-column
              v-else-if="column.key === 'median_views_k'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatViewsNumber(row.median_views_k) }}
              </template>
            </el-table-column>

            <!-- 点赞数列 -->
            <el-table-column
              v-else-if="column.key === 'likes_count'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatNumber(row.likes_count) }}
              </template>
            </el-table-column>

            <!-- 邮箱列 -->
            <el-table-column
              v-else-if="column.key === 'email'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <span v-if="row.email && row.email.trim()">{{ row.email }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 简介列 -->
            <el-table-column
              v-else-if="column.key === 'bio'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span v-if="row.bio && row.bio.trim()">{{ row.bio }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 备注列 -->
            <el-table-column
              v-else-if="column.key === 'note'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.note && row.note.trim() && row.note.length > 20"
                  :content="row.note"
                  placement="top"
                  :show-after="200"
                >
                  <span>{{
                    row.note.length > 20 ? row.note.substring(0, 20) + '...' : row.note
                  }}</span>
                </el-tooltip>
                <span v-else-if="row.note && row.note.trim()">{{ row.note }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- AI评分列 -->
            <el-table-column
              v-else-if="column.key === 'ai_score'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                <span
                  v-if="row.ai_score !== null && row.ai_score !== undefined && row.ai_score !== 0"
                  >{{ row.ai_score }}</span
                >
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- Is Mismatch 布尔状态列 -->
            <el-table-column
              v-else-if="column.key === 'ai_scored_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <div class="boolean-status">
                  <el-icon v-if="row.ai_scored_at" class="status-icon success">
                    <Check />
                  </el-icon>
                  <el-icon v-else class="status-icon failed">
                    <Close />
                  </el-icon>
                  <span class="status-text" :class="row.ai_scored_at ? 'success' : 'failed'">
                    {{ row.ai_scored_at ? 'True' : 'False' }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <!-- Is Nano 布尔状态列 -->
            <el-table-column
              v-else-if="column.key === 'nano_email_fetched_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <div class="boolean-status">
                  <el-icon v-if="row.nano_email_fetched_at" class="status-icon success">
                    <Check />
                  </el-icon>
                  <el-icon v-else class="status-icon failed">
                    <Close />
                  </el-icon>
                  <span
                    class="status-text"
                    :class="row.nano_email_fetched_at ? 'success' : 'failed'"
                  >
                    {{ row.nano_email_fetched_at ? 'True' : 'False' }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <!-- 标题列 -->
            <el-table-column
              v-else-if="column.key === 'captions'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.captions && row.captions.length > 2"
                  :content="row.captions.join(', ')"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(caption, index) in row.captions.slice(0, 2)"
                      :key="`${row.id}-caption-${index}-${caption}`"
                      class="custom-tag"
                    >
                      {{ caption }}
                    </span>
                    <span class="more-tags">+{{ row.captions.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.captions && row.captions.length" class="tags-container">
                  <span
                    v-for="(caption, index) in row.captions"
                    :key="`${row.id}-caption-${index}-${caption}`"
                    class="custom-tag"
                  >
                    {{ caption }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 话题列 -->
            <el-table-column
              v-else-if="column.key === 'topics'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.topics && row.topics.length > 2"
                  :content="row.topics.join(', ')"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(topic, index) in row.topics.slice(0, 2)"
                      :key="`${row.id}-topic-${index}-${topic}`"
                      class="custom-tag"
                    >
                      {{ topic }}
                    </span>
                    <span class="more-tags">+{{ row.topics.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.topics && row.topics.length" class="tags-container">
                  <span
                    v-for="(topic, index) in row.topics"
                    :key="`${row.id}-topic-${index}-${topic}`"
                    class="custom-tag"
                  >
                    {{ topic }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 创建时间列 -->
            <el-table-column
              v-else-if="column.key === 'created_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>

            <!-- 标签列 -->
            <el-table-column
              v-else-if="column.key === 'hashtags'"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.hashtags && row.hashtags.length > 2"
                  :content="formatHashtags(row.hashtags)"
                  placement="top"
                  :show-after="200"
                >
                  <div class="tags-container">
                    <span
                      v-for="(tag, index) in row.hashtags.slice(0, 2)"
                      :key="`${row.id}-${index}-${tag}`"
                      class="custom-tag"
                    >
                      #{{ tag }}
                    </span>
                    <span class="more-tags">+{{ row.hashtags.length - 2 }}</span>
                  </div>
                </el-tooltip>
                <div v-else-if="row.hashtags && row.hashtags.length" class="tags-container">
                  <span
                    v-for="(tag, index) in row.hashtags"
                    :key="`${row.id}-${index}-${tag}`"
                    class="custom-tag"
                  >
                    #{{ tag }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <!-- 更新时间列 -->
            <el-table-column
              v-else-if="column.key === 'updated_at'"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              sortable
            >
              <template #default="{ row }">
                {{ formatDate(row.updated_at) }}
              </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
              v-else-if="column.key === 'actions'"
              :fixed="column.fixed"
              :label="column.label"
              :width="column.width"
            >
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button link type="primary" size="small" @click="handleView(row)">
                    <el-icon><View /></el-icon>
                    查看详情
                  </el-button>
                  <el-button link type="warning" size="small" @click="handleMismatchSingle(row)">
                    <el-icon><DataAnalysis /></el-icon>
                    Mismatch
                  </el-button>
                  <el-button link type="success" size="small" @click="handleNanoSingle(row)">
                    <el-icon><CollectionTag /></el-icon>
                    Nano
                  </el-button>
                </div>
              </template>
            </el-table-column>

            <!-- 其他普通列 -->
            <el-table-column
              v-else
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :show-overflow-tooltip="column.showTooltip"
            >
              <template #default="{ row }">
                {{
                  column.prop &&
                  row[column.prop] !== null &&
                  row[column.prop] !== undefined &&
                  row[column.prop] !== 0 &&
                  row[column.prop] !== ''
                    ? row[column.prop]
                    : '-'
                }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="total-info">共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[100, 200, 500, 1000]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 列设置弹窗 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="500px">
      <div class="column-settings">
        <div class="setting-header">
          <span>拖拽调整列顺序，勾选控制显示/隐藏</span>
          <el-button size="small" @click="resetColumns">重置默认</el-button>
        </div>

        <draggable
          v-model="tableColumns"
          item-key="key"
          @end="saveColumnSettings"
          class="column-list"
          :move="checkMoveAllowed"
        >
          <template #item="{ element }">
            <div
              class="column-item"
              :class="{
                'fixed-column':
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions',
              }"
            >
              <el-icon
                class="drag-handle"
                :class="{
                  disabled:
                    element.key === 'selection' ||
                    element.key === 'social_id' ||
                    element.key === 'actions',
                }"
              >
                <Rank />
              </el-icon>
              <el-checkbox
                v-model="element.visible"
                @change="saveColumnSettings"
                :disabled="
                  element.key === 'selection' ||
                  element.key === 'actions' ||
                  element.key === 'social_id'
                "
              >
                {{ element.label }}
              </el-checkbox>
              <span
                v-if="
                  element.key === 'selection' ||
                  element.key === 'social_id' ||
                  element.key === 'actions'
                "
                class="fixed-label"
              >
                (固定)
              </span>
            </div>
          </template>
        </draggable>
      </div>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      class="export-dialog"
      append-to-body
    >
      <div class="export-content">
        <el-form :model="exportConfig" label-width="120px" class="export-form">
          <!-- 导出范围 -->
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio value="filtered">当前筛选结果</el-radio>
              <el-radio value="current">当前页面数据</el-radio>
              <el-radio value="all">全部数据</el-radio>
            </el-radio-group>
            <div class="form-hint">
              <span v-if="exportConfig.scope === 'filtered'"
                >将导出当前筛选条件下的所有 {{ getFilteredDataCount() }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'current'"
                >将导出当前页面的 {{ kolList.length }} 条数据</span
              >
              <span v-else-if="exportConfig.scope === 'all'">将导出全部数据（忽略筛选条件）</span>
              <span v-else>将导出选中的数据（功能开发中）</span>
            </div>
          </el-form-item>

          <!-- 导出格式 -->
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportConfig.format">
              <el-radio value="xlsx">Excel 文件 (.xlsx)</el-radio>
              <el-radio value="csv">CSV 文件 (.csv)</el-radio>
              <el-radio value="json">JSON 文件 (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件名设置 -->
          <el-form-item label="文件名">
            <el-input
              v-model="exportConfig.filename"
              placeholder="请输入文件名"
              maxlength="50"
              show-word-limit
            >
              <template #suffix>
                <span class="file-extension">.{{ exportConfig.format }}</span>
              </template>
            </el-input>
          </el-form-item>

          <!-- 其他选项 -->
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportConfig.includeHeaders">包含表头</el-checkbox>
          </el-form-item>

          <!-- 选择字段 -->
          <el-form-item label="导出字段" class="field-selection">
            <div class="field-selection-header">
              <span class="selection-info"
                >已选择 {{ exportConfig.fields.length }} / {{ exportableFields.length }} 项</span
              >
              <div class="selection-buttons">
                <el-button link size="small" @click="selectAllFields">全选</el-button>
                <el-button link size="small" @click="clearAllFields">全不选</el-button>
              </div>
            </div>
            <div class="field-list">
              <el-checkbox-group v-model="exportConfig.fields">
                <div class="field-grid">
                  <el-checkbox
                    v-for="field in exportableFields"
                    :key="field.key"
                    :value="field.key"
                    class="field-item"
                  >
                    {{ field.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :disabled="exportConfig.fields.length === 0"
          >
            <el-icon><Download /></el-icon>
            开始导出
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 保存搜索对话框 -->
    <el-dialog
      v-model="showSaveSearchDialog"
      title="保存搜索"
      width="400px"
      class="save-search-dialog"
    >
      <div class="save-search-content">
        <el-form>
          <el-form-item label="搜索名称" required>
            <el-input
              v-model="saveSearchName"
              placeholder="请输入搜索名称"
              clearable
              @keyup.enter="saveCurrentSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSaveSearchDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentSearch" :disabled="!saveSearchName.trim()">
            <el-icon><Star /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- KOL 详情抽屉 -->
    <KolDetailDrawer
      v-model="showDetailDrawer"
      :kol-data="selectedKol"
      @edit="handleEditFromDrawer"
      @save="handleSaveFromDrawer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Search,
  VideoPlay,
  Picture,
  Monitor,
  Setting,
  Rank,
  View,
  Delete,
  Operation,
  Star,
  Download,
  DataAnalysis,
  CollectionTag,
  Plus,
  Close,
  Check,
  QuestionFilled,
  User,
  Edit,
  InfoFilled,
  Loading,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  searchKols,
  batchEvaluateKols,
  getMatchTaskStatus,
  batchNanoKols,
  getNanoTaskStatus,
  fetchNanoEmail,
  type KolItem,
  type KolSearchParams,
  type BatchEvaluateRequest,
  type BatchEvaluateTaskResponse,
  type BatchNanoRequest,
  type BatchNanoTaskResponse,
  type TaskStatus,
  type NanoEmailFetchRequest,
} from '../services/kolApi'
import { fetchAllProjects, type Project } from '../services/projectApi'

// 类型定义

interface SearchCondition {
  id: string
  field: string
  operator: string
  value: string | number | boolean | null
}

interface FieldConfig {
  key: string
  label: string
  type: string
  apiField: string
  minField?: string
  maxField?: string
  afterField?: string
  beforeField?: string
}

interface SearchForm {
  // 搜索逻辑设置
  searchLogic: 'and' | 'or'
  // 动态筛选条件
  conditions: SearchCondition[]
  // 基本信息
  nick_name: string
  social_id: string
  project_code: string
  // 平台信息（改为数组支持多选）
  platforms: string[]
  tiers: string[]
  // 保留向后兼容
  platform: string
  tier: string
  // 数据指标范围
  followersMin?: number
  followersMax?: number
  engagementMin?: number
  engagementMax?: number
  viewsMin?: number
  viewsMax?: number
  // 内容标签
  hashtags: string[]
  topics: string[]
  captions: string[]
  // 时间范围
  createTimeRange?: [Date, Date] | null
  updateTimeRange?: [Date, Date] | null
}

interface SearchTemplate {
  id: string
  name: string
  conditions: Partial<SearchForm>
  createdAt: string
}

interface SavedSearch {
  id: string
  name: string
  searchForm: SearchForm
  createdAt: string
}

interface ActiveFilter {
  key: string
  label: string
  value: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface TableColumn {
  key: string
  prop?: string
  label: string
  width?: number
  visible: boolean
  fixed?: string | boolean
  showTooltip?: boolean
}

interface ExportConfig {
  fields: string[]
  format: 'xlsx' | 'csv' | 'json'
  scope: 'all' | 'current' | 'filtered' | 'selected'
  includeHeaders: boolean
  filename: string
}

interface ExportItem {
  [key: string]: string | number
}

// 响应式数据
const loading = ref(false)
const kolList = ref<KolItem[]>([])
const selectedRows = ref<KolItem[]>([])
const showDetailDrawer = ref(false)
const selectedKol = ref<KolItem | null>(null)
const showColumnSettings = ref(false)

// Mismatch 弹框相关数据
const showMismatchDialog = ref(false)
const mismatchLoading = ref(false)
const mismatchPrompt = ref('')
const mismatchTarget = ref<KolItem[]>([])
const mismatchMode = ref<'batch' | 'single'>('batch')

// 搜索相关数据
const showAdvancedSearchDialog = ref(false)
const showExportDialog = ref(false)
const searchTemplates = ref<SearchTemplate[]>([])

// 保存搜索相关数据
const savedSearches = ref<SavedSearch[]>([])
const showSaveSearchDialog = ref(false)
const saveSearchName = ref('')
const SAVED_SEARCHES_KEY = 'mismatch-saved-searches'

// 项目列表数据
const projectList = ref<Project[]>([])

// 扩展任务状态接口，添加任务类型
interface ExtendedTaskStatus extends TaskStatus {
  task_type: 'mismatch' | 'nano'
}

// 任务状态管理
const activeTasks = ref<Map<string, ExtendedTaskStatus>>(new Map())
const taskPollingIntervals = ref<Map<string, number>>(new Map())
const POLLING_INTERVAL = 3000 // 3秒轮询一次
const TASK_STORAGE_KEY = 'mismatch-active-tasks'

// 导出相关数据
const exportConfig = reactive<ExportConfig>({
  fields: [],
  format: 'xlsx',
  scope: 'filtered',
  includeHeaders: true,
  filename: '',
})

// 字段配置 - 与列设置顺序完全一致
const availableFields: FieldConfig[] = [
  // 按照列设置顺序排列
  { key: 'social_id', label: 'KOL ID', type: 'text', apiField: 'social_id' },
  { key: 'nick_name', label: 'KOL Name', type: 'text', apiField: 'nick_name' },
  { key: 'email', label: 'Email', type: 'text', apiField: 'email' },
  { key: 'bio', label: 'Bio', type: 'text', apiField: 'bio' },
  { key: 'tier', label: 'Tier', type: 'select', apiField: 'tier' },
  { key: 'platform', label: 'Platform', type: 'select', apiField: 'platform' },
  {
    key: 'followers_count',
    label: 'Followers',
    type: 'range',
    apiField: 'followers_count',
    minField: 'min_followers',
    maxField: 'max_followers',
  },
  {
    key: 'likes_count',
    label: 'Likes',
    type: 'range',
    apiField: 'likes_count',
    minField: 'min_likes',
    maxField: 'max_likes',
  },
  {
    key: 'engagement_rate',
    label: 'Engagement Rate',
    type: 'range',
    apiField: 'engagement_rate',
    minField: 'min_engagement_rate',
    maxField: 'max_engagement_rate',
  },
  {
    key: 'mean_views_k',
    label: 'Mean Views',
    type: 'range',
    apiField: 'mean_views_k',
    minField: 'min_mean_views_k',
    maxField: 'max_mean_views_k',
  },
  {
    key: 'median_views_k',
    label: 'Median Views',
    type: 'range',
    apiField: 'median_views_k',
    minField: 'min_median_views_k',
    maxField: 'max_median_views_k',
  },
  { key: 'hashtags', label: 'Hashtags', type: 'fuzzy_array', apiField: 'hashtags_fuzzy' },
  { key: 'captions', label: 'Captions', type: 'fuzzy_array', apiField: 'captions_fuzzy' },
  { key: 'topics', label: 'Topics', type: 'fuzzy_array', apiField: 'topics_fuzzy' },
  { key: 'source', label: 'Source', type: 'text', apiField: 'source' },
  { key: 'project_code', label: 'Project Code', type: 'select', apiField: 'project_code' },
  {
    key: 'ai_score',
    label: 'AI Score',
    type: 'range',
    apiField: 'ai_score',
    minField: 'min_ai_score',
    maxField: 'max_ai_score',
  },
  // 移除隐藏字段：AI Matched, Email Fetch Status, Bio Parsed At, Bio Extracted Email, Nano Extracted Email
  // 布尔状态字段：Is Mismatch（基于 ai_scored_at 是否有值）
  {
    key: 'ai_scored_at',
    label: 'Is Mismatch',
    type: 'boolean',
    apiField: 'ai_scored_at',
  },
  // 布尔状态字段：Is Nano（基于 nano_email_fetched_at 是否有值）
  {
    key: 'nano_email_fetched_at',
    label: 'Is Nano',
    type: 'boolean',
    apiField: 'nano_email_fetched_at',
  },
  { key: 'note', label: 'Note', type: 'text', apiField: 'note' },
  {
    key: 'created_at',
    label: 'Created At',
    type: 'date_range',
    apiField: 'created_at',
    afterField: 'created_after',
    beforeField: 'created_before',
  },
  {
    key: 'updated_at',
    label: 'Updated At',
    type: 'date_range',
    apiField: 'updated_at',
    afterField: 'updated_after',
    beforeField: 'updated_before',
  },
]

// 操作符配置 - 与 KolListView.vue 保持完全一致
const operators = {
  text: [{ value: 'equals', label: '等于' }],
  number: [{ value: 'equals', label: '等于' }],
  range: [
    { value: 'greater_than', label: '大于' },
    { value: 'less_than', label: '小于' },
  ],
  select: [{ value: 'equals', label: '等于' }],
  boolean: [{ value: 'equals', label: '等于' }],
  array: [{ value: 'equals', label: '等于' }],
  fuzzy_array: [{ value: 'contains', label: '包含' }],
  date_range: [
    { value: 'greater_than', label: '晚于' },
    { value: 'less_than', label: '早于' },
  ],
}

const searchForm = reactive<SearchForm>({
  searchLogic: 'and',
  conditions: [],
  nick_name: '',
  social_id: '',
  project_code: '',
  platforms: [],
  tiers: [],
  platform: '',
  tier: '',
  hashtags: [],
  topics: [],
  captions: [],
  createTimeRange: null,
  updateTimeRange: null,
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 100,
  total: 0,
})

// 默认列配置 - 按照用户要求重新配置，与KolListView保持一致
const defaultColumns: TableColumn[] = [
  { key: 'selection', label: '选择', width: 55, visible: true },
  { key: 'social_id', prop: 'social_id', label: 'KOL ID', width: 150, visible: true, fixed: true },
  { key: 'nick_name', prop: 'nick_name', label: 'KOL Name', width: 150, visible: true },
  { key: 'email', prop: 'email', label: 'Email', width: 200, visible: true },
  { key: 'bio', prop: 'bio', label: 'Bio', width: 300, visible: true, showTooltip: true },
  { key: 'tier', prop: 'tier', label: 'Tier', width: 100, visible: true },
  { key: 'platform', prop: 'platform', label: 'Platform', width: 110, visible: true },
  {
    key: 'followers_count',
    prop: 'followers_count',
    label: 'Followers',
    width: 110,
    visible: true,
  },
  { key: 'likes_count', prop: 'likes_count', label: 'Likes', width: 110, visible: true },
  {
    key: 'engagement_rate',
    prop: 'engagement_rate',
    label: 'Engagement Rate',
    width: 170,
    visible: true,
  },
  { key: 'mean_views_k', prop: 'mean_views_k', label: 'Mean Views', width: 130, visible: true },
  {
    key: 'median_views_k',
    prop: 'median_views_k',
    label: 'Median Views',
    width: 140,
    visible: true,
  },
  { key: 'hashtags', label: 'Hashtags', width: 200, visible: true },
  { key: 'captions', label: 'Captions', width: 200, visible: true },
  { key: 'topics', label: 'Topics', width: 200, visible: true },
  { key: 'source', prop: 'source', label: 'Source', width: 120, visible: true },
  { key: 'project_code', prop: 'project_code', label: 'Project Code', width: 120, visible: true },
  { key: 'ai_score', prop: 'ai_score', label: 'AI Score', width: 110, visible: true },
  // 布尔状态字段：Is Mismatch（基于 ai_scored_at 字段）
  {
    key: 'ai_scored_at',
    prop: 'ai_scored_at',
    label: 'Is Mismatch',
    width: 120,
    visible: true,
  },
  // 布尔状态字段：Is Nano（基于 nano_email_fetched_at 字段）
  {
    key: 'nano_email_fetched_at',
    prop: 'nano_email_fetched_at',
    label: 'Is Nano',
    width: 120,
    visible: true,
  },
  { key: 'note', prop: 'note', label: 'Note', width: 150, visible: true },
  { key: 'created_at', prop: 'created_at', label: 'Created At', width: 150, visible: true },
  { key: 'updated_at', prop: 'updated_at', label: 'Updated At', width: 150, visible: true },
  { key: 'actions', label: 'Actions', width: 100, visible: true, fixed: 'right' },
]

// 表格列配置
const tableColumns = ref<TableColumn[]>([...defaultColumns])

// Mismatch 页面允许的字段白名单
const allowedMismatchFields = new Set([
  'selection',
  'social_id',
  'nick_name',
  'email',
  'bio',
  'tier',
  'followers_count',
  'likes_count',
  'engagement_rate',
  'mean_views_k',
  'median_views_k',
  'hashtags',
  'captions',
  'topics',
  'source',
  'project_code',
  'note',
  'ai_score',
  'ai_scored_at',
  'nano_email_fetched_at',
  'created_at',
  'updated_at',
  'actions',
])

// 计算可见的列（添加字段白名单验证）
const visibleColumns = computed(() => {
  return tableColumns.value.filter((column) => {
    // 只显示在白名单中的字段
    if (!allowedMismatchFields.has(column.key)) {
      console.warn(`检测到不属于 Mismatch 页面的字段: ${column.key}，已自动过滤`)
      return false
    }
    return column.visible
  })
})

// 本地存储键名 - 使用唯一标识符避免与其他页面冲突
const COLUMN_SETTINGS_KEY = 'mismatch-list-column-settings'

// API基础URL - 已移至services

// 模拟数据（用于开发测试）
const mockData: KolItem[] = [
  {
    id: 36,
    platform: 'TIKTOK',
    nick_name: 'knb4evaa',
    social_id: 'knb4evaa',
    project_code: 'OOG120',
    email: null,
    bio: null,
    followers_count: null,
    likes_count: null,
    source: 'gmail_sync',
    engagement_rate: null,
    mean_views_k: null,
    median_views_k: null,
    tier: 'NANO',
    hashtags: [],
    captions: [],
    topics: [],
    crawler_task_id: null,
    note: null,
    bio_parsed_at: null,
    bio_extracted_email: null,
    ai_score: null,
    ai_matched: null,
    ai_scored_at: null,
    nano_email_fetched_at: null,
    nano_extracted_email: null,
    email_fetch_status: 'PENDING',
    created_at: '2025-07-23T19:09:48.137500+08:00',
    updated_at: '2025-07-23T19:09:48.137500+08:00',
  },
]

// 条件映射到API参数的函数 - 与 KolListView.vue 保持完全一致
const mapConditionToApiParams = async (
  condition: SearchCondition,
  field: FieldConfig,
  searchParams: KolSearchParams,
) => {
  const { operator, value } = condition
  const { type, apiField, minField, maxField, afterField, beforeField } = field

  switch (type) {
    case 'text':
    case 'number':
    case 'select':
    case 'boolean':
      if (operator === 'equals') {
        ;(searchParams as Record<string, unknown>)[apiField] = value
      }
      break

    case 'range':
      if (operator === 'greater_than' && minField) {
        ;(searchParams as Record<string, unknown>)[minField] = value
      } else if (operator === 'less_than' && maxField) {
        ;(searchParams as Record<string, unknown>)[maxField] = value
      }
      break

    case 'array':
      if (operator === 'equals') {
        ;(searchParams as Record<string, unknown>)[apiField] = Array.isArray(value)
          ? value
          : [value]
      }
      break

    case 'fuzzy_array':
      if (operator === 'contains') {
        ;(searchParams as Record<string, unknown>)[apiField] = Array.isArray(value)
          ? value
          : [value]
      }
      break

    case 'date_range':
      if (operator === 'greater_than' && afterField) {
        ;(searchParams as Record<string, unknown>)[afterField] = value
      } else if (operator === 'less_than' && beforeField) {
        ;(searchParams as Record<string, unknown>)[beforeField] = value
      }
      break
  }
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 计算分页参数
    const skip = (pagination.currentPage - 1) * pagination.pageSize

    if (useAdvancedSearch.value && searchForm.conditions.length > 0) {
      // 使用高级搜索API - 处理动态筛选条件
      const searchParams: KolSearchParams = {}

      // 处理动态筛选条件
      for (const condition of searchForm.conditions) {
        if (
          !condition.field ||
          !condition.operator ||
          condition.value === null ||
          condition.value === ''
        ) {
          continue
        }

        const field = availableFields.find((f) => f.key === condition.field)
        if (!field) continue

        // 根据字段类型和操作符映射到API参数
        await mapConditionToApiParams(condition, field, searchParams)
      }

      // 调用高级搜索API
      const data = await searchKols(searchParams, {
        skip,
        limit: pagination.pageSize,
      })

      // 更新数据
      kolList.value = data.items
      pagination.total = data.total

      console.log('高级搜索完成，结果数量:', data.total)

      // 只有在搜索状态下才标记为已执行搜索
      if (isSearching.value) {
        hasExecutedSearch.value = true
      }
    } else {
      // 使用普通搜索API
      const data = await searchKols(
        {},
        {
          skip,
          limit: pagination.pageSize,
        },
      )

      // 更新数据
      kolList.value = data.items
      pagination.total = data.total
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    if (error instanceof Error) {
      console.error('错误详情:', error.message)
    }

    // 检查是否是后端不可用的错误
    if (
      error instanceof Error &&
      (error.message.includes('Network Error') ||
        error.message.includes('ERR_NETWORK') ||
        error.message === 'BACKEND_UNAVAILABLE')
    ) {
      console.warn('后端服务不可用，使用空数据继续渲染页面')
      // 静默处理，不显示错误消息，让用户知道页面可以正常使用
    } else {
      ElMessage.error('加载数据失败')
    }

    // 设置空数据，确保页面仍然可以正常显示
    kolList.value = []
    pagination.total = 0
    // 如果API调用失败，使用模拟数据
    // kolList.value = mockData
    // pagination.total = mockData.length
  } finally {
    loading.value = false
    // 重置搜索进行状态
    isSearching.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  // MismatchListView 只有高级搜索，不需要切换模式
  loadData()
}

// 工具函数
const getFieldType = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  return field?.type || 'text'
}

const getOperatorsForField = (fieldKey: string) => {
  const fieldType = getFieldType(fieldKey)
  return operators[fieldType as keyof typeof operators] || operators.text
}

const isNoValueOperator = (operator: string): boolean => {
  return ['is_empty', 'is_not_empty'].includes(operator)
}

const getFieldPlaceholder = (fieldKey: string): string => {
  const field = availableFields.find((f) => f.key === fieldKey)
  if (!field) return '请输入'

  // 为 select 类型字段提供更合适的占位符
  if (field.type === 'select') {
    return `请选择${field.label}`
  }

  return `请输入${field.label}`
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getFieldMin = (_fieldKey: string): number => {
  return 0
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getFieldMax = (_fieldKey: string): number | undefined => {
  return undefined
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getFieldPrecision = (_fieldKey: string): number => {
  return 0
}

const getFieldOptions = (fieldKey: string) => {
  switch (fieldKey) {
    case 'platform':
      return [
        { label: 'TikTok', value: 'TIKTOK' },
        { label: 'Instagram', value: 'INSTAGRAM' },
        { label: 'YouTube', value: 'YOUTUBE' },
      ]
    case 'tier':
      return [
        { label: 'NANO', value: 'NANO' },
        { label: 'MICRO', value: 'MICRO' },
        { label: 'MID', value: 'MID' },
        { label: 'MACRO', value: 'MACRO' },
        { label: 'MEGA', value: 'MEGA' },
      ]
    case 'email_fetch_status':
      return [
        { label: '待处理', value: 'PENDING' },
        { label: '已解析', value: 'BIO_PARSED' },
        { label: 'AI评分', value: 'AI_SCORED' },
        { label: '已获取', value: 'NANO_FETCHED' },
        { label: '已完成', value: 'COMPLETED' },
      ]
    case 'project_code':
      return projectList.value.map((project) => ({
        label: project.code,
        value: project.code,
      }))
    default:
      return []
  }
}

const clearAdvancedSearch = () => {
  searchForm.conditions = []
  searchForm.searchLogic = 'and'
}

// 动态筛选条件相关方法
const addCondition = () => {
  const newCondition: SearchCondition = {
    id: Date.now().toString(),
    field: 'social_id', // 默认选择KOL ID字段
    operator: 'equals', // 默认选择等于操作符
    value: null,
  }
  searchForm.conditions.push(newCondition)
}

const removeCondition = (index: number) => {
  searchForm.conditions.splice(index, 1)
}

const handleFieldChange = (index: number) => {
  const condition = searchForm.conditions[index]
  condition.operator = ''
  condition.value = null
}

const handleOperatorChange = (index: number) => {
  const condition = searchForm.conditions[index]
  condition.value = null
}

// 高级搜索相关方法
const useAdvancedSearch = ref(false)
const hasExecutedSearch = ref(false) // 跟踪是否已执行过搜索操作
const isSearching = ref(false) // 跟踪搜索进行状态

const handleAdvancedSearch = async () => {
  // 验证搜索条件
  if (!validateSearchConditions()) {
    return
  }

  showAdvancedSearchDialog.value = false
  pagination.currentPage = 1

  // 标记已经执行了高级搜索
  useAdvancedSearch.value = true

  // 开始搜索状态，但不立即标记为已执行搜索
  isSearching.value = true

  // 提供用户反馈
  ElMessage.success(`已应用${getSearchSummary()}`)

  await loadData()
}

const handleAdvancedSearchClose = () => {
  showAdvancedSearchDialog.value = false
  // 如果取消高级搜索，清除未执行的搜索条件
  if (!useAdvancedSearch.value) {
    searchForm.conditions = []
  }
}

// 验证搜索条件
const validateSearchConditions = (): boolean => {
  if (searchForm.conditions.length === 0) {
    ElMessage.warning('请至少添加一个搜索条件')
    return false
  }

  for (const condition of searchForm.conditions) {
    if (!condition.field) {
      ElMessage.warning('请选择搜索字段')
      return false
    }
    if (!condition.operator) {
      ElMessage.warning('请选择操作符')
      return false
    }
    if (
      !isNoValueOperator(condition.operator) &&
      (condition.value === null || condition.value === '')
    ) {
      ElMessage.warning('请输入搜索值')
      return false
    }
  }

  return true
}

// 获取搜索条件摘要
const getSearchSummary = (): string => {
  const conditionCount = searchForm.conditions.length
  if (conditionCount === 0) return '搜索条件'

  const firstCondition = searchForm.conditions[0]
  const field = availableFields.find((f) => f.key === firstCondition.field)
  const fieldLabel = field ? field.label : firstCondition.field

  if (conditionCount === 1) {
    return `${fieldLabel}筛选条件`
  } else {
    return `${fieldLabel}等${conditionCount}个筛选条件`
  }
}

// 打开高级搜索弹窗
const openAdvancedSearchDialog = () => {
  showAdvancedSearchDialog.value = true
}

const handleReset = () => {
  Object.assign(searchForm, {
    searchLogic: 'and',
    conditions: [],
    nick_name: '',
    social_id: '',
    project_code: '',
    platforms: [],
    tiers: [],
    platform: '',
    tier: '',
    followersMin: undefined,
    followersMax: undefined,
    engagementMin: undefined,
    engagementMax: undefined,
    viewsMin: undefined,
    viewsMax: undefined,
    hashtags: [],
    topics: [],
    captions: [],
    createTimeRange: null,
    updateTimeRange: null,
  })

  // 重置搜索执行状态
  hasExecutedSearch.value = false
  isSearching.value = false
  useAdvancedSearch.value = false

  handleSearch()
}

// 根据当前搜索条件过滤数据
const getFilteredData = (data: KolItem[]) => {
  return data.filter((item) => {
    // 收集所有高级搜索条件的匹配结果
    const conditionResults: boolean[] = []

    // 昵称匹配
    if (searchForm.nick_name) {
      conditionResults.push(
        item.nick_name.toLowerCase().includes(searchForm.nick_name.toLowerCase()),
      )
    }

    // 社交ID匹配
    if (searchForm.social_id) {
      conditionResults.push(
        item.social_id.toLowerCase().includes(searchForm.social_id.toLowerCase()),
      )
    }

    // 项目代码匹配
    if (searchForm.project_code) {
      conditionResults.push(
        item.project_code.toLowerCase().includes(searchForm.project_code.toLowerCase()),
      )
    }

    // 平台匹配
    if (searchForm.platforms.length > 0) {
      conditionResults.push(searchForm.platforms.includes(item.platform))
    }

    // 等级匹配
    if (searchForm.tiers.length > 0) {
      conditionResults.push(searchForm.tiers.includes(item.tier))
    }

    // 粉丝数量范围
    if (searchForm.followersMin !== undefined || searchForm.followersMax !== undefined) {
      if (item.followers_count !== null) {
        const minMatch =
          searchForm.followersMin === undefined || item.followers_count >= searchForm.followersMin
        const maxMatch =
          searchForm.followersMax === undefined || item.followers_count <= searchForm.followersMax
        conditionResults.push(minMatch && maxMatch)
      } else {
        conditionResults.push(false)
      }
    }

    // 互动率范围
    if (searchForm.engagementMin !== undefined || searchForm.engagementMax !== undefined) {
      if (item.engagement_rate !== null) {
        const minMatch =
          searchForm.engagementMin === undefined || item.engagement_rate >= searchForm.engagementMin
        const maxMatch =
          searchForm.engagementMax === undefined || item.engagement_rate <= searchForm.engagementMax
        conditionResults.push(minMatch && maxMatch)
      } else {
        conditionResults.push(false)
      }
    }

    // 平均观看数范围
    if (searchForm.viewsMin !== undefined || searchForm.viewsMax !== undefined) {
      if (item.mean_views_k !== null) {
        const minMatch =
          searchForm.viewsMin === undefined || item.mean_views_k >= searchForm.viewsMin
        const maxMatch =
          searchForm.viewsMax === undefined || item.mean_views_k <= searchForm.viewsMax
        conditionResults.push(minMatch && maxMatch)
      } else {
        conditionResults.push(false)
      }
    }

    // 标签匹配
    if (searchForm.hashtags.length > 0) {
      const hasHashtagMatch = searchForm.hashtags.some((tag) => item.hashtags?.includes(tag))
      conditionResults.push(hasHashtagMatch)
    }

    // 话题匹配
    if (searchForm.topics.length > 0) {
      const hasTopicMatch = searchForm.topics.some((topic) => item.topics?.includes(topic))
      conditionResults.push(hasTopicMatch)
    }

    // 创建时间范围匹配
    if (searchForm.createTimeRange && searchForm.createTimeRange.length === 2) {
      const [start, end] = searchForm.createTimeRange
      const itemDate = new Date(item.created_at)
      conditionResults.push(itemDate >= start && itemDate <= end)
    }

    // 更新时间范围匹配
    if (searchForm.updateTimeRange && searchForm.updateTimeRange.length === 2) {
      const [start, end] = searchForm.updateTimeRange
      const itemDate = new Date(item.updated_at)
      conditionResults.push(itemDate >= start && itemDate <= end)
    }

    // 如果没有设置任何高级搜索条件，则显示所有结果
    if (conditionResults.length === 0) {
      return true
    }

    // 根据搜索逻辑决定如何处理条件
    if (searchForm.searchLogic === 'and') {
      // AND逻辑：所有条件都必须满足
      return conditionResults.every((result) => result === true)
    } else {
      // OR逻辑：满足任意一个条件即可
      return conditionResults.some((result) => result === true)
    }
  })
}

// 获取筛选后的数据数量
const getFilteredDataCount = () => {
  return getFilteredData(mockData).length
}

const applySearchTemplate = (template: SearchTemplate) => {
  Object.assign(searchForm, template.conditions)
  handleAdvancedSearch()
}

const removeSearchTemplate = (templateId: string) => {
  const index = searchTemplates.value.findIndex((t) => t.id === templateId)
  if (index > -1) {
    searchTemplates.value.splice(index, 1)
  }
}

const exportSearchResults = () => {
  // 自动生成文件名
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-')
  exportConfig.filename = `KOL数据_${timestamp}`

  // 默认选择所有可见列
  exportConfig.fields = visibleColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)

  showExportDialog.value = true
}

const handleExport = () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请至少选择一个字段进行导出')
    return
  }

  try {
    // 获取要导出的数据
    const dataToExport = getExportData()

    // 根据格式导出
    switch (exportConfig.format) {
      case 'xlsx':
        exportToExcel(dataToExport)
        break
      case 'csv':
        exportToCSV(dataToExport)
        break
      case 'json':
        exportToJSON(dataToExport)
        break
    }

    showExportDialog.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getExportData = () => {
  let sourceData: KolItem[] = []

  // 根据导出范围获取数据
  switch (exportConfig.scope) {
    case 'all':
      // 获取所有原始数据，不应用任何筛选条件
      sourceData = mockData
      break
    case 'current':
      // 当前页面显示的数据
      sourceData = kolList.value
      break
    case 'filtered':
      // 当前筛选条件下的所有数据
      sourceData = getFilteredData(mockData)
      break
    case 'selected':
      // 这里需要实现表格选择功能，暂时用当前页数据
      sourceData = kolList.value
      break
  }

  // 根据选择的字段过滤数据
  const filteredData = sourceData.map((item) => {
    const exportItem: ExportItem = {}
    exportConfig.fields.forEach((field) => {
      const column = tableColumns.value.find((col) => col.key === field)
      const label = column?.label || field

      switch (field) {
        case 'nick_name':
          exportItem[label] = item.nick_name
          break
        case 'social_id':
          exportItem[label] = item.social_id || ''
          break
        case 'project_code':
          exportItem[label] = item.project_code || ''
          break
        case 'platform':
          exportItem[label] = getPlatformLabel(item.platform)
          break
        case 'tier':
          exportItem[label] = getTierLabel(item.tier)
          break
        case 'followers_count':
          exportItem[label] = item.followers_count || 0
          break
        case 'engagement_rate':
          exportItem[label] = item.engagement_rate
            ? (item.engagement_rate * 100).toFixed(2) + '%'
            : '-'
          break
        case 'mean_views_k':
          exportItem[label] = (item.mean_views_k || 0) + 'K'
          break
        case 'median_views_k':
          exportItem[label] = (item.median_views_k || 0) + 'K'
          break
        case 'likes_count':
          exportItem[label] = item.likes_count || 0
          break
        case 'email':
          exportItem[label] = item.email || ''
          break
        case 'bio':
          exportItem[label] = item.bio || ''
          break
        case 'crawler_task_id':
          exportItem[label] = item.crawler_task_id || ''
          break
        case 'note':
          exportItem[label] = item.note || ''
          break
        case 'bio_parsed_at':
          exportItem[label] = item.bio_parsed_at ? formatDate(item.bio_parsed_at) : ''
          break
        case 'bio_extracted_email':
          exportItem[label] = item.bio_extracted_email || ''
          break
        case 'ai_score':
          exportItem[label] = item.ai_score || ''
          break
        case 'ai_matched':
          exportItem[label] =
            item.ai_matched === true ? '是' : item.ai_matched === false ? '否' : ''
          break
        case 'ai_scored_at':
          exportItem[label] = item.ai_scored_at ? formatDate(item.ai_scored_at) : ''
          break
        case 'nano_email_fetched_at':
          exportItem[label] = item.nano_email_fetched_at
            ? formatDate(item.nano_email_fetched_at)
            : ''
          break
        case 'nano_extracted_email':
          exportItem[label] = item.nano_extracted_email || ''
          break
        case 'hashtags':
          exportItem[label] = item.hashtags?.join(', ') || ''
          break
        case 'captions':
          exportItem[label] = item.captions?.join(', ') || ''
          break
        case 'topics':
          exportItem[label] = item.topics?.join(', ') || ''
          break
        case 'created_at':
          exportItem[label] = formatDate(item.created_at)
          break
        case 'updated_at':
          exportItem[label] = formatDate(item.updated_at)
          break
        default:
          exportItem[label] = (item as unknown as Record<string, unknown>)[field]?.toString() || ''
      }
    })
    return exportItem
  })

  return filteredData
}

const exportToExcel = (data: ExportItem[]) => {
  // 这里可以使用 xlsx 库来导出 Excel
  // 为了演示，我们先用 CSV 的方式
  exportToCSV(data)
}

const exportToCSV = (data: ExportItem[]) => {
  if (data.length === 0) return

  const headers = Object.keys(data[0])
  let csvContent = ''

  // 添加表头
  if (exportConfig.includeHeaders) {
    csvContent += headers.join(',') + '\n'
  }

  // 添加数据行
  data.forEach((row) => {
    const values = headers.map((header) => {
      const value = row[header]
      // 处理包含逗号的值，用引号包围
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    })
    csvContent += values.join(',') + '\n'
  })

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const exportToJSON = (data: ExportItem[]) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${exportConfig.filename}.json`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 字段选择方法
const selectAllFields = () => {
  exportConfig.fields = tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => col.key)
}

const clearAllFields = () => {
  exportConfig.fields = []
}

// 可导出的字段选项
const exportableFields = computed(() => {
  return tableColumns.value
    .filter((col) => col.key !== 'selection' && col.key !== 'actions')
    .map((col) => ({
      key: col.key,
      label: col.label,
      checked: exportConfig.fields.includes(col.key),
    }))
})

// 计算属性 - 是否有高级筛选条件（除了快速搜索）
const hasAdvancedFilters = computed(() => {
  return activeFilters.value.length > 0
})

// 计算属性 - 是否应该显示当前搜索条件卡片（MismatchListView 只有高级搜索）
const shouldShowCurrentFilters = computed(() => {
  // 只有在使用高级搜索且有筛选条件时才显示卡片
  return useAdvancedSearch.value && hasAdvancedFilters.value
})

// 计算属性 - Mismatch 任务相关
const mismatchTasks = computed(() => {
  const tasks = new Map<string, ExtendedTaskStatus>()
  activeTasks.value.forEach((task, taskId) => {
    if (task.task_type === 'mismatch') {
      tasks.set(taskId, task)
    }
  })
  return tasks
})

const mismatchTaskCount = computed(() => mismatchTasks.value.size)

// 计算属性 - Nano 任务相关
const nanoTasks = computed(() => {
  const tasks = new Map<string, ExtendedTaskStatus>()
  activeTasks.value.forEach((task, taskId) => {
    if (task.task_type === 'nano') {
      tasks.set(taskId, task)
    }
  })
  return tasks
})

const nanoTaskCount = computed(() => nanoTasks.value.size)

const activeFilters = computed(() => {
  const filters: ActiveFilter[] = []

  // 添加动态筛选条件 - 只有在实际执行了高级搜索后才显示
  if (useAdvancedSearch.value) {
    searchForm.conditions.forEach((condition, index) => {
      if (condition.field && condition.operator) {
        const field = availableFields.find((f) => f.key === condition.field)
        const fieldLabel = field ? field.label : condition.field

        let operatorLabel = ''
        const operators = getOperatorsForField(condition.field)
        const operator = operators.find((op) => op.value === condition.operator)
        operatorLabel = operator ? operator.label : condition.operator

        let value = ''
        if (isNoValueOperator(condition.operator)) {
          value = operatorLabel
        } else if (condition.value !== null && condition.value !== '') {
          if (condition.field === 'platform') {
            const platformOptions = getFieldOptions('platform')
            const option = platformOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'tier') {
            const tierOptions = getFieldOptions('tier')
            const option = tierOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'email_fetch_status') {
            const statusOptions = getFieldOptions('email_fetch_status')
            const option = statusOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'project_code') {
            const projectOptions = getFieldOptions('project_code')
            const option = projectOptions.find((opt) => opt.value === condition.value)
            value = option ? option.label : String(condition.value)
          } else if (condition.field === 'ai_matched') {
            value = condition.value ? '是' : '否'
          } else {
            value = String(condition.value)
          }
        } else {
          value = operatorLabel
        }

        filters.push({
          key: `condition_${index}`,
          label: `${fieldLabel} ${operatorLabel}`,
          value: value,
        })
      }
    })
  }

  if (searchForm.nick_name) {
    filters.push({ key: 'nick_name', label: '昵称', value: searchForm.nick_name })
  }
  if (searchForm.social_id) {
    filters.push({ key: 'social_id', label: '社交ID', value: searchForm.social_id })
  }
  if (searchForm.project_code) {
    filters.push({ key: 'project_code', label: '项目代码', value: searchForm.project_code })
  }
  if (searchForm.platforms.length > 0) {
    filters.push({ key: 'platforms', label: '平台', value: searchForm.platforms.join(', ') })
  }
  if (searchForm.tiers.length > 0) {
    filters.push({ key: 'tiers', label: '等级', value: searchForm.tiers.join(', ') })
  }

  // 数据指标范围筛选条件
  if (searchForm.followersMin !== undefined || searchForm.followersMax !== undefined) {
    const min = searchForm.followersMin || 0
    const max = searchForm.followersMax || '∞'
    filters.push({ key: 'followersRange', label: '粉丝数量', value: `${min} - ${max}` })
  }
  if (searchForm.engagementMin !== undefined || searchForm.engagementMax !== undefined) {
    const min = searchForm.engagementMin || 0
    const max = searchForm.engagementMax || '∞'
    filters.push({ key: 'engagementRange', label: '互动率', value: `${min}% - ${max}%` })
  }
  if (searchForm.viewsMin !== undefined || searchForm.viewsMax !== undefined) {
    const min = searchForm.viewsMin || 0
    const max = searchForm.viewsMax || '∞'
    filters.push({ key: 'viewsRange', label: '平均观看数', value: `${min}K - ${max}K` })
  }

  if (searchForm.hashtags.length > 0) {
    filters.push({ key: 'hashtags', label: '标签', value: searchForm.hashtags.join(', ') })
  }
  if (searchForm.topics.length > 0) {
    filters.push({ key: 'topics', label: '话题', value: searchForm.topics.join(', ') })
  }

  // 时间范围筛选条件
  if (searchForm.createTimeRange && searchForm.createTimeRange.length === 2) {
    const [start, end] = searchForm.createTimeRange
    const startDate = start.toLocaleDateString('zh-CN')
    const endDate = end.toLocaleDateString('zh-CN')
    filters.push({ key: 'createTimeRange', label: '创建时间', value: `${startDate} 至 ${endDate}` })
  }
  if (searchForm.updateTimeRange && searchForm.updateTimeRange.length === 2) {
    const [start, end] = searchForm.updateTimeRange
    const startDate = start.toLocaleDateString('zh-CN')
    const endDate = end.toLocaleDateString('zh-CN')
    filters.push({ key: 'updateTimeRange', label: '更新时间', value: `${startDate} 至 ${endDate}` })
  }

  return filters
})

const removeFilter = (filterKey: string) => {
  // 处理动态筛选条件
  if (filterKey.startsWith('condition_')) {
    const index = parseInt(filterKey.replace('condition_', ''))
    if (!isNaN(index) && index >= 0 && index < searchForm.conditions.length) {
      searchForm.conditions.splice(index, 1)
      handleAdvancedSearch()
      return
    }
  }

  switch (filterKey) {
    case 'nick_name':
      searchForm.nick_name = ''
      break
    case 'social_id':
      searchForm.social_id = ''
      break
    case 'project_code':
      searchForm.project_code = ''
      break
    case 'platforms':
      searchForm.platforms = []
      break
    case 'tiers':
      searchForm.tiers = []
      break
    case 'followersRange':
      searchForm.followersMin = undefined
      searchForm.followersMax = undefined
      break
    case 'engagementRange':
      searchForm.engagementMin = undefined
      searchForm.engagementMax = undefined
      break
    case 'viewsRange':
      searchForm.viewsMin = undefined
      searchForm.viewsMax = undefined
      break
    case 'hashtags':
      searchForm.hashtags = []
      break
    case 'topics':
      searchForm.topics = []
      break
    case 'createTimeRange':
      searchForm.createTimeRange = null
      break
    case 'updateTimeRange':
      searchForm.updateTimeRange = null
      break
  }
  handleAdvancedSearch()
}

const clearAllFilters = () => {
  // 重置搜索执行状态
  hasExecutedSearch.value = false
  isSearching.value = false

  handleReset()

  ElMessage.success('已清空所有搜索条件')
}

const handleView = (row: KolItem) => {
  selectedKol.value = row
  showDetailDrawer.value = true
}

// 选择变化处理函数
const handleSelectionChange = (selection: KolItem[]) => {
  selectedRows.value = selection
}

// Mismatch 按钮处理函数
const handleMismatch = () => {
  // 如果用户选择了记录，使用选中的记录；否则使用当前表格的所有数据
  const targetData = selectedRows.value.length > 0 ? selectedRows.value : kolList.value

  // 设置批量模式并显示弹框
  mismatchMode.value = 'batch'
  mismatchTarget.value = [...targetData]
  mismatchPrompt.value = ''
  showMismatchDialog.value = true
}

// 单个 Mismatch 解锁处理函数
const handleMismatchSingle = (row: KolItem) => {
  // 设置单个模式并显示弹框
  mismatchMode.value = 'single'
  mismatchTarget.value = [row]
  mismatchPrompt.value = ''
  showMismatchDialog.value = true
}

// Mismatch 弹框关闭处理
const handleMismatchDialogClose = () => {
  showMismatchDialog.value = false
  mismatchPrompt.value = ''
  mismatchTarget.value = []
}

// 确认 Mismatch 操作
const confirmMismatch = async () => {
  if (!mismatchPrompt.value.trim()) {
    ElMessage.warning('请输入评估提示词')
    return
  }

  try {
    mismatchLoading.value = true
    const kolIds = mismatchTarget.value.map((row) => row.id)
    const request: BatchEvaluateRequest = {
      kol_ids: kolIds,
      prompt: mismatchPrompt.value.trim(),
    }

    // 调用批量评估接口，获取任务ID
    const response: BatchEvaluateTaskResponse = await batchEvaluateKols(request)

    // 将任务添加到活跃任务列表
    const initialStatus: ExtendedTaskStatus = {
      task_id: response.task_id,
      task_type: 'mismatch',
      status: 'running',
      progress: 0,
      total_count: kolIds.length,
      processed_count: 0,
      success_count: 0,
      failed_count: 0,
      message: response.message,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    activeTasks.value.set(response.task_id, initialStatus)
    saveActiveTasksToStorage()

    // 开始轮询任务状态
    startTaskPolling(response.task_id, 'mismatch')

    // 显示任务启动成功消息
    ElMessage.success(`批量 Mismatch 任务已启动！任务ID: ${response.task_id.substring(0, 8)}...`)

    // 显示持续通知，告知用户任务正在进行
    ElNotification({
      title: '批量 Mismatch 任务进行中',
      message: `正在评估 ${kolIds.length} 个KOL，任务完成后将自动通知您`,
      type: 'info',
      duration: 5000,
      position: 'top-right',
    })

    // 关闭弹框
    handleMismatchDialogClose()
  } catch (error) {
    console.error('Mismatch评估任务启动失败:', error)
    ElMessage.error('Mismatch评估任务启动失败，请重试')
  } finally {
    mismatchLoading.value = false
  }
}

// Nano 按钮处理函数
const handleNano = () => {
  // 如果用户选择了记录，使用选中的记录；否则使用当前表格的所有数据
  const targetData = selectedRows.value.length > 0 ? selectedRows.value : kolList.value

  if (targetData.length === 0) {
    ElMessage.warning('没有可处理的数据')
    return
  }

  // 显示确认对话框
  ElMessageBox.confirm(
    `确定要对 ${targetData.length} 条记录执行批量 Nano 邮箱获取吗？`,
    '批量 Nano 确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      try {
        loading.value = true
        const kolIds = targetData.map((row) => row.id)
        const request: BatchNanoRequest = {
          kol_ids: kolIds,
        }

        // 调用批量 Nano 接口，获取任务ID
        const response: BatchNanoTaskResponse = await batchNanoKols(request)

        // 将任务添加到活跃任务列表
        const initialStatus: ExtendedTaskStatus = {
          task_id: response.task_id,
          task_type: 'nano',
          status: 'running',
          progress: 0,
          total_count: kolIds.length,
          processed_count: 0,
          success_count: 0,
          failed_count: 0,
          message: response.message,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }

        activeTasks.value.set(response.task_id, initialStatus)
        saveActiveTasksToStorage()

        // 开始轮询任务状态
        startTaskPolling(response.task_id, 'nano')

        // 显示任务启动成功消息
        ElMessage.success(`批量 Nano 任务已启动！任务ID: ${response.task_id.substring(0, 8)}...`)

        // 显示持续通知，告知用户任务正在进行
        ElNotification({
          title: '批量 Nano 任务进行中',
          message: `正在处理 ${kolIds.length} 个KOL，任务完成后将自动通知您`,
          type: 'info',
          duration: 5000,
          position: 'top-right',
        })
      } catch (error) {
        console.error('批量Nano邮箱获取请求发送失败:', error)
        ElMessage.error('批量Nano邮箱获取请求发送失败，请重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 单个 Nano 解锁处理函数
const handleNanoSingle = async (row: KolItem) => {
  try {
    loading.value = true
    const request: NanoEmailFetchRequest = {
      kol_id: row.id,
    }

    const response = await fetchNanoEmail(request)

    if (response.success) {
      ElMessage.success(`Nano邮箱获取成功！邮箱: ${response.email}`)
      // 刷新数据
      await loadData()
    } else {
      ElMessage.error(`Nano邮箱获取失败: ${response.message}`)
    }
  } catch (error) {
    console.error('Nano邮箱获取失败:', error)
    ElMessage.error('Nano邮箱获取操作失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleEditFromDrawer = (kol: KolItem) => {
  // 可以在这里处理编辑事件的额外逻辑
  console.log('开始编辑 KOL:', kol.nick_name)
}

const handleSaveFromDrawer = (updatedKol: KolItem) => {
  // 在列表中更新对应的数据
  const index = kolList.value.findIndex((item) => item.id === updatedKol.id)
  if (index !== -1) {
    kolList.value[index] = { ...updatedKol }
    selectedKol.value = { ...updatedKol }
  }
  ElMessage.success(`${updatedKol.nick_name} 的信息已更新`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const saveColumnSettings = () => {
  localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(tableColumns.value))
}

const resetColumns = () => {
  tableColumns.value = [...defaultColumns]
  saveColumnSettings()
}

const checkMoveAllowed = (evt: {
  draggedContext: { element: TableColumn }
  relatedContext?: { element: TableColumn }
}) => {
  const { draggedContext, relatedContext } = evt

  // 固定列不能移动
  const fixedColumns = ['selection', 'nick_name', 'actions']

  // 如果拖拽的是固定列，不允许移动
  if (fixedColumns.includes(draggedContext.element.key)) {
    return false
  }

  // 如果目标位置是固定列的位置，也不允许移动
  if (relatedContext && fixedColumns.includes(relatedContext.element.key)) {
    return false
  }

  return true
}

// 工具方法
const formatNumber = (num: number | null | undefined) => {
  if (num === null || num === undefined || num === 0) {
    return '-'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatViewsNumber = (num: number | null | undefined) => {
  if (num === null || num === undefined || num === 0) {
    return '-'
  }
  return num + 'K'
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '-'
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const getPlatformLabel = (platform: string) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    TIKTOK: 'TikTok',
    INSTAGRAM: 'Instagram',
    YOUTUBE: 'YouTube',
  }
  return labels[platform] || platform
}

const getPlatformType = (platform: string) => {
  const types: Record<string, string> = {
    TIKTOK: 'danger',
    INSTAGRAM: 'warning',
    YOUTUBE: 'info',
  }
  return types[platform] || 'info'
}

const getTierLabel = (tier: string) => {
  if (!tier) return '-'
  const labels: Record<string, string> = {
    NANO: 'NANO',
    MICRO: 'MICRO',
    MID: 'MID',
    MACRO: 'MACRO',
    MEGA: 'MEGA',
  }
  return labels[tier] || tier
}

const getTierType = (tier: string) => {
  if (!tier) return 'info'
  const types: Record<string, string> = {
    NANO: 'info',
    MICRO: 'warning',
    MID: 'success',
    MACRO: 'danger',
    MEGA: 'danger',
  }
  return types[tier] || 'info'
}

const getEngagementClass = (rate: number) => {
  if (rate >= 10) return 'engagement-excellent'
  if (rate >= 5) return 'engagement-good'
  if (rate >= 2) return 'engagement-normal'
  return 'engagement-low'
}

const formatHashtags = (hashtags: string[]) => {
  if (!hashtags || hashtags.length === 0) return '-'
  return hashtags.map((tag) => '#' + tag).join(', ')
}

const getPlatformUrl = (platform: string, socialId: string) => {
  if (!platform || !socialId) return null
  switch (platform?.toUpperCase()) {
    case 'TIKTOK':
      return `https://www.tiktok.com/@${socialId}`
    case 'INSTAGRAM':
      return `https://www.instagram.com/${socialId}`
    case 'YOUTUBE':
      return null // 暂时不处理
    default:
      return null
  }
}

const openPlatformUrl = (platform: string, socialId: string) => {
  const url = getPlatformUrl(platform, socialId)
  if (url) {
    window.open(url, '_blank')
  }
}

// 保存搜索相关方法
const loadSavedSearches = () => {
  try {
    const saved = localStorage.getItem(SAVED_SEARCHES_KEY)
    if (saved) {
      savedSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载保存的搜索失败:', error)
  }
}

const saveCurrentSearch = () => {
  if (!saveSearchName.value.trim()) {
    ElMessage.warning('请输入搜索名称')
    return
  }

  // 检查是否已存在同名搜索
  const existingIndex = savedSearches.value.findIndex(
    (search) => search.name === saveSearchName.value.trim(),
  )

  const newSearch: SavedSearch = {
    id: Date.now().toString(),
    name: saveSearchName.value.trim(),
    searchForm: JSON.parse(JSON.stringify(searchForm)), // 深拷贝
    createdAt: new Date().toISOString(),
  }

  if (existingIndex > -1) {
    // 更新现有搜索
    savedSearches.value[existingIndex] = newSearch
    ElMessage.success('搜索已更新')
  } else {
    // 添加新搜索
    savedSearches.value.push(newSearch)
    ElMessage.success('搜索已保存')
  }

  // 保存到localStorage
  localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))

  // 关闭对话框并清空输入
  showSaveSearchDialog.value = false
  saveSearchName.value = ''
}

const applySavedSearch = (savedSearch: SavedSearch) => {
  try {
    // 应用保存的搜索条件
    Object.assign(searchForm, savedSearch.searchForm)

    // 执行搜索
    handleAdvancedSearch()

    ElMessage.success(`已应用搜索: ${savedSearch.name}`)
  } catch (error) {
    console.error('应用保存的搜索失败:', error)
    ElMessage.error('应用搜索失败，请重试')
  }
}

const deleteSavedSearch = (searchId: string) => {
  const index = savedSearches.value.findIndex((search) => search.id === searchId)
  if (index > -1) {
    const searchName = savedSearches.value[index].name
    savedSearches.value.splice(index, 1)
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches.value))
    ElMessage.success(`已删除搜索: ${searchName}`)
  }
}

const openSaveSearchDialog = () => {
  saveSearchName.value = ''
  showSaveSearchDialog.value = true
}

const formatSavedSearchTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    return '刚刚'
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}小时前`
  } else if (diffInHours < 24 * 7) {
    return `${Math.floor(diffInHours / 24)}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 监听高级搜索条件变化，重置搜索执行状态
watch(
  () => searchForm.conditions,
  (newConditions, oldConditions) => {
    // 只有在初始化完成后才处理变化
    if (!oldConditions) return

    // 检查是否有条件发生了变化
    const hasChanged =
      newConditions.length !== oldConditions.length ||
      newConditions.some((newCondition, index) => {
        const oldCondition = oldConditions[index]
        return (
          !oldCondition ||
          newCondition.field !== oldCondition.field ||
          newCondition.operator !== oldCondition.operator ||
          newCondition.value !== oldCondition.value
        )
      })

    if (hasChanged && hasExecutedSearch.value && !isSearching.value) {
      console.log('高级搜索条件发生变化，重置搜索执行状态')

      // 重置搜索执行状态
      hasExecutedSearch.value = false
    }
  },
  { deep: true },
)

// 任务状态管理函数
// 保存活跃任务到本地存储
const saveActiveTasksToStorage = () => {
  const tasksArray = Array.from(activeTasks.value.entries())
  localStorage.setItem(TASK_STORAGE_KEY, JSON.stringify(tasksArray))
}

// 从本地存储加载活跃任务
const loadActiveTasksFromStorage = () => {
  try {
    const stored = localStorage.getItem(TASK_STORAGE_KEY)
    if (stored) {
      const tasksArray = JSON.parse(stored)
      activeTasks.value = new Map(tasksArray)
      // 为每个活跃任务重新启动轮询
      activeTasks.value.forEach((task, taskId) => {
        if (task.status === 'running') {
          startTaskPolling(taskId, task.task_type || 'mismatch')
        }
      })
    }
  } catch (error) {
    console.error('加载活跃任务失败:', error)
  }
}

// 开始轮询任务状态
const startTaskPolling = (taskId: string, taskType: 'mismatch' | 'nano' = 'mismatch') => {
  // 如果已经在轮询，先清除
  if (taskPollingIntervals.value.has(taskId)) {
    clearInterval(taskPollingIntervals.value.get(taskId)!)
  }

  const intervalId = setInterval(async () => {
    try {
      // 根据任务类型选择不同的状态查询函数
      const status =
        taskType === 'mismatch' ? await getMatchTaskStatus(taskId) : await getNanoTaskStatus(taskId)

      // 添加任务类型到状态对象
      const extendedStatus: ExtendedTaskStatus = {
        ...status,
        task_type: taskType,
      }

      activeTasks.value.set(taskId, extendedStatus)
      saveActiveTasksToStorage()

      // 如果任务完成或失败，停止轮询
      if (status.status === 'completed' || status.status === 'failed') {
        stopTaskPolling(taskId)
        handleTaskCompletion(extendedStatus)
      }
    } catch (error) {
      console.error(`轮询任务 ${taskId} 状态失败:`, error)
      // 如果连续失败，可以考虑停止轮询或重试
    }
  }, POLLING_INTERVAL)

  taskPollingIntervals.value.set(taskId, intervalId as unknown as number)
}

// 停止轮询任务状态
const stopTaskPolling = (taskId: string) => {
  const intervalId = taskPollingIntervals.value.get(taskId)
  if (intervalId) {
    clearInterval(intervalId)
    taskPollingIntervals.value.delete(taskId)
  }
  activeTasks.value.delete(taskId)
  saveActiveTasksToStorage()
}

// 处理任务完成
const handleTaskCompletion = (status: ExtendedTaskStatus) => {
  const taskTypeName = status.task_type === 'mismatch' ? 'Mismatch' : 'Nano'

  if (status.status === 'completed') {
    // 成功完成
    ElNotification({
      title: `批量 ${taskTypeName} 任务完成`,
      message: `任务已完成！${status.message}`,
      type: 'success',
      duration: 8000,
      position: 'top-right',
    })
    // 刷新表格数据
    loadData()
  } else if (status.status === 'failed') {
    // 任务失败
    ElNotification({
      title: `批量 ${taskTypeName} 任务失败`,
      message: `任务执行失败：${status.message}`,
      type: 'error',
      duration: 10000,
      position: 'top-right',
    })
  }
}

// 清理所有任务轮询
const cleanupAllPolling = () => {
  taskPollingIntervals.value.forEach((intervalId) => {
    clearInterval(intervalId)
  })
  taskPollingIntervals.value.clear()
}

// 生命周期
// 加载项目列表
const loadProjectList = async () => {
  try {
    const projects = await fetchAllProjects()
    projectList.value = projects
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

onMounted(() => {
  // 一次性清理：检查并清理可能冲突的本地存储数据
  cleanupConflictedLocalStorage()

  loadData()
  loadSavedSearches() // 加载保存的搜索
  loadProjectList() // 加载项目列表
  loadActiveTasksFromStorage() // 加载活跃任务
  const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      // 验证列配置是否适用于当前页面（检查是否包含 Mismatch 特有的字段）
      // 过滤掉不属于 Mismatch 页面的字段
      const filteredColumns = parsedColumns.filter((col: any) => allowedMismatchFields.has(col.key))

      const hasMismatchFields = filteredColumns.some(
        (col: any) => col.key === 'ai_scored_at' || col.key === 'nano_email_fetched_at',
      )

      if (hasMismatchFields && filteredColumns.length > 0) {
        // 检查是否有被过滤掉的字段
        if (filteredColumns.length < parsedColumns.length) {
          const removedFields = parsedColumns
            .filter((col: any) => !allowedMismatchFields.has(col.key))
            .map((col: any) => col.key)
          console.warn(`检测到并过滤了不属于 Mismatch 页面的字段: ${removedFields.join(', ')}`)
        }
        tableColumns.value = filteredColumns
        // 如果有字段被过滤，保存清理后的配置
        if (filteredColumns.length < parsedColumns.length) {
          saveColumnSettings()
        }
      } else {
        // 如果不包含 Mismatch 特有字段，说明可能是其他页面的配置，使用默认配置
        console.warn('检测到可能来自其他页面的列配置，使用默认配置')
        tableColumns.value = [...defaultColumns]
        saveColumnSettings() // 保存正确的默认配置
      }
    } catch (error) {
      console.error('解析列配置失败，使用默认配置:', error)
      tableColumns.value = [...defaultColumns]
      saveColumnSettings()
    }
  }
})

// 清理冲突的本地存储数据（一次性执行）
const cleanupConflictedLocalStorage = () => {
  const cleanupFlag = 'mismatch-cleanup-done-v1'
  if (localStorage.getItem(cleanupFlag)) {
    return // 已经清理过了
  }

  try {
    // 检查是否存在旧的冲突键名数据
    const oldColumnData = localStorage.getItem('kol-list-column-settings')
    if (oldColumnData) {
      const parsedData = JSON.parse(oldColumnData)
      // 检查是否包含其他页面的字段（如 social_id, performance_id 等）
      const hasKolFields = parsedData.some((col: any) => col.key === 'social_id')
      const hasPerformanceFields = parsedData.some((col: any) => col.key === 'performance_id')

      if (hasKolFields || hasPerformanceFields) {
        // 这是其他页面的数据，不应该影响 Mismatch 页面
        console.log('清理检测到的其他页面列配置数据')
        // 不删除原数据，但确保当前页面使用正确的配置
        localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(defaultColumns))
      }
    }

    // 标记清理已完成
    localStorage.setItem(cleanupFlag, 'true')
  } catch (error) {
    console.error('清理本地存储时出错:', error)
  }
}

// 页面卸载时清理轮询
onUnmounted(() => {
  cleanupAllPolling()
})

// 任务状态辅助函数
const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'running':
      return '运行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

const getTaskStatusClass = (status: string) => {
  switch (status) {
    case 'running':
      return 'status-running'
    case 'completed':
      return 'status-completed'
    case 'failed':
      return 'status-failed'
    default:
      return 'status-unknown'
  }
}
</script>

<style scoped>
.kol-list-container {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

/* 容器响应式优化 */
@media (max-width: 768px) {
  .kol-list-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .kol-list-container {
    padding: 12px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.header-right .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header-right .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 按钮图标优化 */
.header-right .el-button .el-icon {
  transition: transform 0.3s ease;
}

.header-right .el-button:hover .el-icon {
  transform: scale(1.1);
}

.search-card {
  margin-bottom: 16px;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px;
}

/* 搜索头部样式 */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.search-title .el-icon {
  color: #667eea;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.advanced-toggle,
.save-search {
  font-size: 14px;
  color: #667eea;
  transition: all 0.3s ease;
}

.advanced-toggle:hover,
.save-search:hover {
  color: #4c6ef5;
  transform: translateY(-1px);
}

/* 搜索模板样式 */
.search-templates {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.templates-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 高级搜索样式 */
.advanced-search {
  margin-top: 16px;
}

.advanced-form {
  background: #fafbfc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.search-section {
  margin-bottom: 24px;
}

.search-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* 范围输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

/* 搜索操作栏样式 */
.search-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.actions-left {
  display: flex;
  gap: 12px;
}

.actions-right {
  display: flex;
  gap: 8px;
}

/* 当前筛选条件样式 */
.current-filters {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #c7d2fe;
}

.filters-label {
  font-size: 14px;
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-tag {
  background: white;
  border: 1px solid #c7d2fe;
  color: #4338ca;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  background: #eef2ff;
  transform: translateY(-1px);
}

.clear-all {
  color: #dc2626;
  font-size: 13px;
  padding: 4px 8px;
  margin-left: 8px;
}

.clear-all:hover {
  background: #fee2e2;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }

  .header-left {
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-right {
    justify-content: center;
    gap: 8px;
  }

  .header-right .el-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 13px;
    padding: 8px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 14px;
  }

  .search-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }

  .search-actions-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .actions-left,
  .actions-right {
    justify-content: center;
  }

  .range-input {
    flex-direction: column;
    gap: 4px;
  }

  .range-separator {
    display: none;
  }

  .filters-list {
    justify-content: center;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .page-header {
    padding: 18px 22px;
  }

  .header-right {
    gap: 10px;
  }

  .header-right .el-button {
    font-size: 13px;
    padding: 8px 14px;
  }
}

/* 平板设备优化 */
@media (max-width: 900px) and (min-width: 481px) {
  .page-header {
    padding: 16px 20px;
  }

  .header-right {
    gap: 8px;
  }

  .header-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 100px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .page-header {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 18px;
  }

  .page-description {
    font-size: 12px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
    min-width: auto;
    justify-content: center;
    font-size: 12px;
    padding: 10px 12px;
  }

  .header-right .el-button .el-icon {
    font-size: 13px;
  }
}

.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-card {
  margin-bottom: 20px;
  /* 防止表格加载时位置跳动 */
  min-height: 200px;
  position: relative;
}

.table-card :deep(.el-table) {
  width: 100% !important;
  table-layout: auto;
  /* 使用transform创建新的渲染层，提高性能 */
  transform: translateZ(0);
  will-change: transform;
}

/* 优化loading遮罩层的过渡效果 */
.table-card :deep(.el-loading-mask) {
  transition: opacity 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 确保表格容器在数据加载前后保持稳定位置 */
.table-card :deep(.el-table__body-wrapper) {
  min-height: 100px;
}

/* 表格容器样式 */
.table-container {
  position: relative;
  min-height: 200px;
  /* 使用transform优化性能 */
  transform: translateZ(0);
  will-change: transform;
}

/* 确保表格在loading状态变化时保持稳定位置 */
.table-container :deep(.el-table) {
  position: relative;
  /* 移除z-index，避免层叠问题 */
}

/* 优化loading遮罩层的过渡效果 */
.table-container :deep(.el-loading-mask) {
  transition: opacity 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 防止表格在数据加载时位置跳动 */
.table-container :deep(.el-table__body) {
  min-height: 100px;
}

/* 优化表格头部，移除可能导致卡顿的粘性定位 */
.table-container :deep(.el-table__header-wrapper) {
  background: white;
  /* 使用transform而不是position: sticky */
  transform: translateZ(0);
}

/* 优化表格行的性能 */
.table-container :deep(.el-table__row) {
  /* 移除transition: none，让Element Plus自然处理 */
  transform: translateZ(0);
}

/* 优化loading动画性能 */
.table-container :deep(.el-loading-spinner) {
  transform: translateZ(0);
  will-change: transform;
}

/* 确保表格内容在loading状态变化时保持稳定 */
.table-container :deep(.el-table__body) {
  transform: translateZ(0);
}

/* 优化表格容器的渲染性能 */
.table-card {
  /* 创建新的渲染层 */
  transform: translateZ(0);
  will-change: transform;
  /* 确保内容不会溢出 */
  overflow: hidden;
}

/* KOL 信息样式 */
.kol-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nick-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.nick-name.clickable {
  cursor: pointer;
  color: #409eff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nick-name.clickable:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

/* 互动率样式 */
.engagement-excellent {
  color: #67c23a;
  font-weight: 600;
}

.engagement-good {
  color: #409eff;
  font-weight: 600;
}

.engagement-normal {
  color: #e6a23c;
  font-weight: 600;
}

.engagement-low {
  color: #f56c6c;
  font-weight: 600;
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 禁用标签区域的动画 */
.tags-container {
  transition: none !important;
}

.tags-container * {
  transition: none !important;
  animation: none !important;
}

/* 自定义标签样式 */
.custom-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 4px;
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

/* 列设置弹窗样式 */
.column-settings {
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-header span {
  font-size: 14px;
  color: #666;
}

.column-list {
  min-height: 150px;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.column-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #909399;
}

.column-item:active {
  cursor: grabbing;
}

.fixed-column {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px 12px !important;
}

.drag-handle.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.fixed-label {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 高级搜索弹窗样式 */
.advanced-search-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.advanced-search-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.advanced-search-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.advanced-search-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 高级搜索内容区域 */
.advanced-search-content {
  background: #f8fafc;
  padding: 24px;
}

/* 搜索表单分组样式 */
.search-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.search-section:last-child {
  margin-bottom: 0;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding: 24px;
}

/* 表单项样式优化 */
.advanced-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.advanced-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.advanced-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.advanced-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.advanced-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 动态筛选条件样式 */
.global-logic {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.logic-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.logic-select {
  width: 120px;
}

.help-icon {
  color: #6b7280;
  cursor: help;
}

.filter-conditions {
  margin-bottom: 24px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.condition-row:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.field-select {
  width: 180px;
  flex-shrink: 0;
}

.operator-select {
  width: 140px;
  flex-shrink: 0;
}

.value-input {
  flex: 1;
  min-width: 0;
}

.value-input .el-input,
.value-input .el-input-number,
.value-input .el-date-picker,
.value-input .el-select {
  width: 100%;
}

.delete-btn {
  flex-shrink: 0;
  margin-left: 8px;
}

.add-condition {
  margin-bottom: 16px;
}

.add-btn {
  width: 100%;
  height: 48px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.add-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f0f4ff;
}

.advanced-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.advanced-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

.advanced-form :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

/* 范围输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* 平台选项样式 */
.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 高级搜索弹窗样式优化 */
.advanced-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.advanced-search-dialog :deep(.el-dialog__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.advanced-search-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  display: flex;
  gap: 12px;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .advanced-search-dialog {
    width: 95% !important;
    margin: 2.5vh auto !important;
  }

  .advanced-search-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }

  .advanced-search-dialog :deep(.el-dialog__title) {
    font-size: 18px;
  }

  .advanced-search-content {
    padding: 16px;
  }

  .section-content {
    padding: 16px;
  }

  .advanced-form :deep(.el-col) {
    margin-bottom: 16px;
  }

  .dialog-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .dialog-footer .el-button {
    flex: 1;
  }
}

/* 导出弹窗样式 */
.export-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.export-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

.export-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.export-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 16px;
}

.export-content {
  padding: 24px;
}

.export-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.form-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.file-extension {
  color: #9ca3af;
  font-size: 12px;
  padding: 0 8px;
}

.field-selection {
  margin-bottom: 0;
}

.field-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.selection-info {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selection-buttons {
  display: flex;
  gap: 8px;
}

.selection-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
  color: #409eff;
}

.selection-buttons .el-button:hover {
  color: #66b1ff;
}

.field-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background: #f9fafb;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
}

.field-item {
  margin: 0;
  font-size: 13px;
}

.field-item :deep(.el-checkbox__label) {
  font-size: 13px;
  color: #374151;
}

.export-dialog .dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.export-dialog .dialog-footer .el-button {
  border-radius: 6px;
}

/* 搜索逻辑选项样式 */
.logic-radio-group {
  display: flex;
  flex-wrap: wrap; /* 允许内容换行 */
  gap: 16px; /* 增加间距 */
  width: 100%; /* 确保占用全部宽度 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
  min-height: 100px; /* 确保有足够的高度 */
}

.logic-radio {
  flex: 1 1 45%; /* 每个选项最多占 45% 宽度 */
  margin-right: 0 !important;
  margin-bottom: 16px !important;
  min-width: 200px; /* 最小宽度 */
  display: flex; /* 确保内容居中 */
  align-items: center; /* 垂直居中 */
}

.logic-option {
  margin-left: 0;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8fafc;
  cursor: pointer;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column; /* 描述文字在标题下方 */
  gap: 8px; /* 标题和描述间距 */
  overflow: visible; /* 防止内容被剪切 */
  text-align: center; /* 水平居中内容 */
}

.logic-option strong {
  font-size: 16px; /* 增加标题字体大小以突出 */
}

.logic-description {
  font-size: 12px;
  color: #909399;
  margin-top: 0; /* 移除顶部边距 */
  line-height: 1.4;
  text-align: center; /* 水平居中描述 */
}

/* 选中状态样式 */
.logic-radio-group :deep(.el-radio.is-checked) .logic-option {
  border-color: #409eff !important;
  background: #ecf5ff !important;
}

/* 悬停状态样式 */
.logic-radio:hover .logic-option {
  border-color: #c6d1f0;
  background: #f0f4ff;
}

/* 确保选中状态优先级高于悬停状态 */
.logic-radio-group :deep(.el-radio.is-checked):hover .logic-option {
  border-color: #409eff !important;
  background: #ecf5ff !important;
}

/* 确保对话框主体能容纳内容 */
.advanced-search-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto; /* 允许滚动 */
}

/* Mismatch 弹框样式 */
.mismatch-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.mismatch-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.mismatch-info {
  margin-bottom: 8px;
}

.mismatch-title {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.mismatch-target-list {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.target-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.target-item .el-icon {
  color: #606266;
}

.target-more {
  color: #909399;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 4px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.section-title .el-icon {
  color: #409eff;
}

.prompt-section {
  margin-bottom: 8px;
}

.prompt-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prompt-textarea {
  border-radius: 8px;
}

.prompt-textarea :deep(.el-textarea__inner) {
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.prompt-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.action-buttons .el-button .el-icon {
  margin-right: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .logic-radio-group {
    flex-direction: column; /* 小屏幕上垂直堆叠 */
    gap: 12px;
  }

  .logic-radio {
    flex: 1 1 100%; /* 全宽 */
    min-width: auto; /* 移除最小宽度约束 */
  }

  .logic-option {
    padding: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    justify-content: center;
  }
}

/* 保存搜索相关样式 */
.saved-searches {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.saved-searches-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saved-searches-label::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #28a745;
  border-radius: 2px;
}

.saved-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.saved-search-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding-right: 24px;
}

.saved-search-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.saved-search-time {
  font-size: 11px;
  color: #6c757d;
  margin-left: 6px;
  opacity: 0.8;
}

.save-search-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.save-search-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.save-search-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.save-search-dialog :deep(.el-dialog__close) {
  color: white;
}

.save-search-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.save-search-content {
  padding: 16px 0;
}

.save-search-content .el-form-item {
  margin-bottom: 0;
}

.save-search-content .el-input {
  border-radius: 8px;
}

.save-search-content .el-input :deep(.el-input__inner) {
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
}

.save-search-content .el-input :deep(.el-input__inner):focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 搜索结果统计样式 */
.result-count-tag {
  margin-left: 8px;
  font-weight: 500;
}

/* 搜索进行中状态样式 */
.searching-tag {
  margin-left: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.searching-tag .el-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 活跃任务状态样式 */
.active-tasks-container {
  margin-bottom: 16px;
}

.active-tasks-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409eff;
}

.task-icon {
  animation: rotate 2s linear infinite;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.task-info {
  flex: 1;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 8px;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 批量 Mismatch 按钮角标样式 */
.mismatch-button-wrapper {
  position: relative;
  display: inline-block;
}

.mismatch-task-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  margin: 0;
}

.mismatch-task-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 2px solid white;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 批量 Nano 按钮角标样式 */
.nano-button-wrapper {
  position: relative;
  display: inline-block;
}

.nano-task-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  margin: 0;
}

.nano-task-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 2px solid white;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 角标悬停详情弹出框样式 */
.badge-task-details {
  padding: 0;
}

.badge-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.badge-details-list {
  max-height: 200px;
  overflow-y: auto;
}

.badge-task-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.badge-task-item:hover {
  background-color: #f8f9fa;
}

.badge-task-item:last-child {
  border-bottom: none;
}

.badge-task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.badge-task-id {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.badge-task-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.status-running {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-failed {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-unknown {
  background-color: #f5f5f5;
  color: #666;
}

.badge-task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.badge-progress-text {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.success-count {
  color: #67c23a;
  font-weight: 500;
}

.failed-count {
  color: #f56c6c;
  font-weight: 500;
}

.badge-details-footer {
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: center;
}
</style>

<style>
/* 全局弹出框样式 */
.mismatch-badge-popover {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.mismatch-badge-popover .el-popover__arrow::before {
  border-bottom-color: #f8f9fa !important;
}

/* Nano 弹出框全局样式 */
.nano-badge-popover {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.nano-badge-popover .el-popover__arrow::before {
  border-bottom-color: #f8f9fa !important;
}

/* 布尔状态显示样式 */
.boolean-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  font-size: 14px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.failed {
  color: #f56c6c;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-text.success {
  color: #67c23a;
}

.status-text.failed {
  color: #f56c6c;
}
</style>
