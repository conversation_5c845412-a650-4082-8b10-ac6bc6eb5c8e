<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import MainLayout from './components/MainLayout.vue'

const route = useRoute()

// 判断是否为登录页面
const isLoginPage = computed(() => {
  return route.name === 'login' || route.path === '/'
})
</script>

<template>
  <div class="app-wrapper">
    <!-- 登录页面时全屏显示 -->
    <template v-if="isLoginPage">
      <RouterView />
    </template>

    <!-- 登录后使用主控台布局 -->
    <template v-else>
      <MainLayout />
    </template>
  </div>
</template>

<style scoped>
.app-wrapper {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>
