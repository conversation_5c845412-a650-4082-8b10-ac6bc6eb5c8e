import httpClient from '../utils/httpClient'

// 平台枚举类型
export type PlatformType = 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE'

// 绩效记录类型定义
export interface Performance {
  id: number
  platform: PlatformType
  social_id: string
  kol_id: number
  project_code: string
  post_link: string
  post_date: string | null
  views_total: number | null
  likes_total: number | null
  comments_total: number | null
  shares_total: number | null
  views_day1: number | null
  likes_day1: number | null
  comments_day1: number | null
  shares_day1: number | null
  views_day3: number | null
  likes_day3: number | null
  comments_day3: number | null
  shares_day3: number | null
  views_day7: number | null
  likes_day7: number | null
  comments_day7: number | null
  shares_day7: number | null
  created_at: string
  updated_at: string
}

// 创建绩效记录请求类型
export interface CreatePerformanceRequest {
  platform: PlatformType
  social_id: string
  project_code: string
  post_link: string
  post_date?: string | null
  views_total?: number | null
  likes_total?: number | null
  comments_total?: number | null
  shares_total?: number | null
  views_day1?: number | null
  likes_day1?: number | null
  comments_day1?: number | null
  shares_day1?: number | null
  views_day3?: number | null
  likes_day3?: number | null
  comments_day3?: number | null
  shares_day3?: number | null
  views_day7?: number | null
  likes_day7?: number | null
  comments_day7?: number | null
  shares_day7?: number | null
}

// 绩效列表响应类型
export interface PerformanceListResponse {
  items: Performance[]
  total: number
  skip: number
  limit: number
}

// 绩效列表参数
export interface PerformanceListParams {
  skip?: number
  limit?: number
}

// 创建绩效记录
export async function createPerformance(
  performanceData: CreatePerformanceRequest,
): Promise<Performance> {
  const response = await httpClient.post('/api/v1/performances/', performanceData)
  return response.data
}

// 获取绩效记录列表
export async function fetchPerformances(
  params: PerformanceListParams = {},
): Promise<PerformanceListResponse> {
  const { skip = 0, limit = 100 } = params
  const response = await httpClient.get('/api/v1/performances/', {
    params: { skip, limit },
  })
  return response.data
}

// KOL绩效搜索参数类型
export interface KolPerformanceSearchParams {
  social_id_like?: string | null
  post_link_like?: string | null
  platform?: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE' | null
  project_code?: string | null
}

// 搜索KOL绩效数据
export async function searchKolPerformanceData(
  searchParams: KolPerformanceSearchParams,
  listParams: KolPerformanceParams = {},
): Promise<KolPerformanceListResponse> {
  const { skip = 0, limit = 100 } = listParams
  const params = new URLSearchParams()

  // 添加分页参数
  params.append('skip', skip.toString())
  params.append('limit', limit.toString())

  const response = await httpClient.post(
    `/api/v1/kol-performance/search?${params.toString()}`,
    searchParams,
  )
  return response.data
}

// 根据ID获取绩效记录
export async function fetchPerformanceById(id: number): Promise<Performance> {
  const response = await httpClient.get(`/api/v1/performances/${id}`)
  return response.data
}

// 更新绩效记录
export async function updatePerformance(
  id: number,
  performanceData: Partial<CreatePerformanceRequest>,
): Promise<Performance> {
  const response = await httpClient.put(`/api/v1/performances/${id}`, performanceData)
  return response.data
}

// 删除绩效记录
export async function deletePerformance(id: number): Promise<void> {
  await httpClient.delete(`/api/v1/performances/${id}`)
}

// 绩效支付统一管理接口类型定义
export interface PerformancePaymentRequest {
  platform: PlatformType
  social_id: string
  project_code: string
  post_link: string
  post_date?: string | null
  payment_amount?: number | null
  paypal_accounts?: string | null
  tracker?: string | null
  fund_source?: string | null
  payout_date?: string | null
  note?: string | null
  payment_screenshot_file?: File | null
}

export interface PerformancePaymentResponse {
  performance: Performance
  payment: {
    id: number
    performance_id: number
    payment_amount: number
    paypal_accounts: string
    tracker: string
    payout_date: string
    fund_source: string
    payment_screenshot_filename: string | null
    note: string | null
    created_at: string
    updated_at: string
  }
}

// 创建绩效支付统一记录
export async function createPerformancePayment(
  data: PerformancePaymentRequest,
): Promise<PerformancePaymentResponse> {
  const formData = new FormData()

  // 添加基本字段
  formData.append('platform', data.platform)
  formData.append('social_id', data.social_id)
  formData.append('project_code', data.project_code)
  formData.append('post_link', data.post_link)

  // 添加可选字段
  if (data.post_date) {
    formData.append('post_date', data.post_date)
  }
  if (data.payment_amount !== null && data.payment_amount !== undefined) {
    formData.append('payment_amount', data.payment_amount.toString())
  }
  if (data.paypal_accounts) {
    formData.append('paypal_accounts', data.paypal_accounts)
  }
  if (data.tracker) {
    formData.append('tracker', data.tracker)
  }
  if (data.fund_source) {
    formData.append('fund_source', data.fund_source)
  }
  if (data.payout_date) {
    formData.append('payout_date', data.payout_date)
  }
  if (data.note) {
    formData.append('note', data.note)
  }

  // 添加文件
  if (data.payment_screenshot_file) {
    formData.append('payment_screenshot_file', data.payment_screenshot_file)
  }

  const response = await httpClient.post('/api/v1/performance-payments/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

  return response.data
}

// KOL表现数据接口类型定义
export interface KolPerformanceParams {
  skip?: number
  limit?: number
  platform?: PlatformType | null
  project_code?: string | null
  status?: string | null
  min_cpm?: number | null
  max_cpm?: number | null
  min_views?: number | null
  max_views?: number | null
}

export interface KolPerformanceItem {
  performance_id: number
  payment_id: number | null
  platform: PlatformType
  social_id: string
  nick_name: string
  project_code: string
  post_link: string
  post_date: string | null
  cpm: string | null
  engagement_rate: string | null
  status: string
  views_total: number | null
  likes_total: number | null
  comments_total: number | null
  shares_total: number | null
  payment_amount: string | null
  created_at: string
  updated_at: string
}

export interface KolPerformanceListResponse {
  items: KolPerformanceItem[]
  total: number
  skip: number
  limit: number
}

// 获取KOL表现数据列表
export async function fetchKolPerformanceData(
  params: KolPerformanceParams = {},
): Promise<KolPerformanceListResponse> {
  const { skip = 0, limit = 100, ...otherParams } = params

  const response = await httpClient.get('/api/v1/kol-performance/', {
    params: {
      skip,
      limit,
      ...otherParams,
    },
  })

  return response.data
}
