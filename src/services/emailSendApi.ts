import httpClient from '../utils/httpClient'

// 邮件发送搜索参数接口
export interface EmailSendSearch {
  social_id?: string | null
  platform?: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE' | null
  status?: 'PENDING' | 'SENT' | 'FAILED' | null
  template_code?: string | null
  project_code?: string | null
  from_email?: string | null
  to_email?: string | null
}

// 邮件发送列表参数接口
export interface EmailSendListParams {
  skip?: number
  limit?: number
}

// 邮件发送项接口
export interface EmailSend {
  id: number
  platform: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE' | null
  social_id: string
  project_code: string
  template_code: string
  from_email: string | null
  to_email: string | null
  note: string | null
  send_status: 'PENDING' | 'SENT' | 'FAILED'
  send_date: string | null
  created_at: string
  updated_at: string
}

// 邮件发送列表响应接口
export interface EmailSendListResponse {
  items: EmailSend[]
  total: number
  skip: number
  limit: number
}

// 搜索邮件发送记录
export async function searchEmailSends(
  searchParams: EmailSendSearch,
  listParams: EmailSendListParams = {},
): Promise<EmailSendListResponse> {
  const params = new URLSearchParams()

  // 添加列表参数
  if (listParams.skip !== undefined) {
    params.append('skip', listParams.skip.toString())
  }
  if (listParams.limit !== undefined) {
    params.append('limit', listParams.limit.toString())
  }

  const response = await httpClient.post(
    `/api/v1/email-send-logs/search?${params.toString()}`,
    searchParams,
  )
  return response.data
}

// 获取邮件发送列表（兼容旧接口）
export async function fetchEmailSends(params: EmailSendListParams): Promise<EmailSendListResponse> {
  const response = await httpClient.get('/api/v1/email-send-logs/', { params })
  return response.data
}

// 转换邮件发送数据格式（用于前端显示）
export function transformEmailSendData(item: EmailSend) {
  return {
    id: item.id,
    platform: item.platform?.toLowerCase() as 'tiktok' | 'instagram' | 'youtube' | null,
    social_id: item.social_id,
    send_status: item.send_status.toLowerCase() as 'pending' | 'sent' | 'failed',
    send_date: item.send_date,
    project_code: item.project_code,
    template_code: item.template_code,
    from_email: item.from_email,
    to_email: item.to_email,
    note: item.note,
    created_at: item.created_at,
    updated_at: item.updated_at,
  }
}
