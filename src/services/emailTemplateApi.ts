import httpClient from '../utils/httpClient'

// 邮件模板类型定义
export interface EmailTemplate {
  id: number
  code: string
  name: string
  project_code: string
  postmark_token?: string
  from_email?: string
  note?: string
  created_at: string
  updated_at: string
}

// 邮件模板列表响应类型
export interface EmailTemplateListResponse {
  items: EmailTemplate[]
  total: number
  skip: number
  limit: number
}

// 获取邮件模板列表参数
export interface EmailTemplateListParams {
  skip?: number
  limit?: number
  project_code?: string
}

// 获取邮件模板列表
export async function fetchEmailTemplates(
  params: EmailTemplateListParams = {},
): Promise<EmailTemplateListResponse> {
  const { skip = 0, limit = 100, project_code } = params
  const requestParams: Record<string, string | number> = { skip, limit }

  if (project_code) {
    requestParams.project_code = project_code
  }

  const res = await httpClient.get('/api/v1/email-templates/', {
    params: requestParams,
  })
  return res.data
}

// 获取所有邮件模板（用于下拉选择等场景）
export async function fetchAllEmailTemplates(): Promise<EmailTemplate[]> {
  const response = await fetchEmailTemplates({ skip: 0, limit: 1000 })
  return response.items
}

// 根据项目代码获取邮件模板列表
export async function fetchEmailTemplatesByProject(projectCode: string): Promise<EmailTemplate[]> {
  const response = await fetchEmailTemplates({ project_code: projectCode })
  return response.items
}

// 根据模板代码获取邮件模板信息
export async function fetchEmailTemplateByCode(code: string): Promise<EmailTemplate | null> {
  try {
    const templates = await fetchAllEmailTemplates()
    return templates.find((template) => template.code === code) || null
  } catch {
    return null
  }
}

// 创建邮件模板请求体类型
export interface CreateEmailTemplateRequest {
  code: string
  name: string
  project_code: string
  postmark_token?: string
  from_email?: string
  note?: string
}

// 创建邮件模板
export async function createEmailTemplate(
  templateData: CreateEmailTemplateRequest,
): Promise<EmailTemplate> {
  const res = await httpClient.post('/api/v1/email-templates/', templateData)
  return res.data
}

// 更新邮件模板请求体类型
export interface UpdateEmailTemplateRequest {
  name: string
  project_code: string
  postmark_token?: string
  from_email?: string
  note?: string
}

// 更新邮件模板
export async function updateEmailTemplate(
  id: number,
  templateData: UpdateEmailTemplateRequest,
): Promise<EmailTemplate> {
  const res = await httpClient.put(`/api/v1/email-templates/${id}`, templateData)
  return res.data
}

// 删除邮件模板
export async function deleteEmailTemplate(id: number): Promise<EmailTemplate> {
  const res = await httpClient.delete(`/api/v1/email-templates/${id}`)
  return res.data
}
