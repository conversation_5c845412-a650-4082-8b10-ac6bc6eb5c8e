import httpClient from '../utils/httpClient'

export type PlatformType = 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE' | null

export interface CandidateItem {
  id: number
  platform: PlatformType
  kol_id: string
  social_id?: string | null
  nick_name: string
  project_code: string
  reply_email_addr?: string
  follow_up_status:
    | 'PENDING'
    | 'PROCESSING'
    | 'DRAFTING'
    | 'COMPLETED'
    | 'PAID'
    | 'NOT_TARGET'
    | 'OFF'
  follow_up_note?: string
  first_contact_date: string
  last_contact_date: string
  send_round: number
  latest_email_send_id?: number | null
  thread_id: string
  tracker?: string
  note?: string | null
  parsed_email?: {
    date: number
    from: string
    subject: string
    body_preview: string
  }
  parsed_social_link?: {
    error?: string
  }
  need_review: boolean
  created_at: string
  updated_at: string
}

export interface CandidateListResponse {
  items: CandidateItem[]
  total: number
  skip: number
  limit: number
}

export interface CandidateListParams {
  skip?: number
  limit?: number
  project_code?: string
  platform?: PlatformType
  status?: string
  tracker?: string
  need_review?: boolean
}

// 候选人搜索参数接口 - 符合 CandidateSearch schema
export interface CandidateSearchParams {
  // 模糊匹配字段
  nick_name?: string | null // 昵称（模糊匹配）
  social_id?: string | null // KOL ID（精确匹配）
  tracker?: string | null // 跟进人（模糊匹配）
  reply_email?: string | null // 回复邮箱（模糊匹配）

  // 精确匹配字段
  platform?: PlatformType // 平台（枚举值）
  status?: string | null // 跟进状态（枚举值）
  project_code?: string | null // 项目编码
  need_review?: boolean | null // 是否需要人工审核（布尔值）
}

// 保持向后兼容
export type CandidateSearchRequest = CandidateSearchParams

export async function fetchCandidates(
  params: CandidateListParams = {},
): Promise<CandidateListResponse> {
  const { skip = 0, limit = 100, project_code, platform, status, tracker, need_review } = params
  const requestParams: Record<string, string | number | boolean | null> = { skip, limit }

  if (project_code) requestParams.project_code = project_code
  if (platform) requestParams.platform = platform
  if (status) requestParams.status = status
  if (tracker) requestParams.tracker = tracker
  if (need_review !== undefined) requestParams.need_review = need_review

  const res = await httpClient.get('/api/v1/candidates/', { params: requestParams })
  return res.data
}

// 搜索候选人
export async function searchCandidates(
  searchParams: CandidateSearchParams,
  paginationParams: { skip?: number; limit?: number } = {},
): Promise<CandidateListResponse> {
  const params = {
    skip: paginationParams.skip || 0,
    limit: paginationParams.limit || 100,
  }

  const res = await httpClient.post('/api/v1/candidates/search', searchParams, {
    params,
  })
  return res.data
}

// 更新候选人的请求参数接口
export interface UpdateCandidateRequest {
  nick_name?: string
  social_id?: string | null
  reply_email_addr?: string
  platform?: PlatformType
  follow_up_status?:
    | 'PENDING'
    | 'PROCESSING'
    | 'DRAFTING'
    | 'COMPLETED'
    | 'PAID'
    | 'NOT_TARGET'
    | 'OFF'
  follow_up_note?: string
  tracker?: string
  note?: string | null
  need_review?: boolean
}

// 更新候选人
export async function updateCandidate(
  id: number,
  candidateData: UpdateCandidateRequest,
): Promise<CandidateItem> {
  const res = await httpClient.put(`/api/v1/candidates/${id}`, candidateData)
  return res.data
}
