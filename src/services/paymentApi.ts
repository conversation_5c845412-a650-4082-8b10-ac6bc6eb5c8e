import httpClient from '../utils/httpClient'

// 支付记录类型定义
export interface Payment {
  id: number
  performance_id: number
  payment_amount: string | null
  paypal_accounts: string | null
  tracker: string | null
  fund_source: string | null
  payment_screenshot_filename: string | null
  note: string | null
  payout_date: string | null
  created_at: string
  updated_at: string
}

// 创建支付记录请求类型
export interface CreatePaymentRequest {
  payment_amount?: number | string | null
  paypal_accounts?: string | null
  tracker?: string | null
  fund_source?: string | null
  payment_screenshot_filename?: string | null
  note?: string | null
}

// 支付列表响应类型
export interface PaymentListResponse {
  items: Payment[]
  total: number
  skip: number
  limit: number
}

// 支付列表参数
export interface PaymentListParams {
  skip?: number
  limit?: number
}

// 支付搜索参数
export interface PaymentSearch {
  performance_id?: number | null
  tracker?: string | null
  paypal_account?: string | null
  fund_source?: string | null
  min_amount?: number | null
  max_amount?: number | null
  payout_date_after?: string | null
  payout_date_before?: string | null
}

// 标记为已支付请求类型
export interface PaymentMarkAsPaidRequest {
  payout_date?: string | null
}

// 创建支付记录
export async function createPayment(paymentData: CreatePaymentRequest): Promise<Payment> {
  const response = await httpClient.post('/api/v1/payments/', paymentData)
  return response.data
}

// 获取支付记录列表
export async function fetchPayments(params: PaymentListParams = {}): Promise<PaymentListResponse> {
  const { skip = 0, limit = 100 } = params
  const response = await httpClient.get('/api/v1/payments/', {
    params: { skip, limit },
  })
  return response.data
}

// 搜索支付记录
export async function searchPayments(
  searchParams: PaymentSearch,
  listParams: PaymentListParams = {},
): Promise<PaymentListResponse> {
  const params = new URLSearchParams()

  // 添加列表参数
  if (listParams.skip !== undefined) {
    params.append('skip', listParams.skip.toString())
  }
  if (listParams.limit !== undefined) {
    params.append('limit', listParams.limit.toString())
  }

  const response = await httpClient.post(
    `/api/v1/payments/search?${params.toString()}`,
    searchParams,
  )
  return response.data
}

// 根据ID获取支付记录
export async function fetchPaymentById(id: number): Promise<Payment> {
  const response = await httpClient.get(`/api/v1/payments/${id}`)
  return response.data
}

// 更新支付记录
export async function updatePayment(
  id: number,
  paymentData: Partial<CreatePaymentRequest>,
): Promise<Payment> {
  const response = await httpClient.put(`/api/v1/payments/${id}`, paymentData)
  return response.data
}

// 删除支付记录
export async function deletePayment(id: number): Promise<void> {
  await httpClient.delete(`/api/v1/payments/${id}`)
}

// 标记为已支付
export async function markAsPaid(
  id: number,
  markAsPaidData: PaymentMarkAsPaidRequest = {},
): Promise<Payment> {
  const response = await httpClient.post(`/api/v1/payments/${id}/mark-as-paid`, markAsPaidData)
  return response.data
}
