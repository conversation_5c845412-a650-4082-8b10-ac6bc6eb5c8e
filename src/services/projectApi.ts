import httpClient from '../utils/httpClient'

// 项目类型定义
export interface Project {
  name: string
  description: string
  code: string
  created_at: string
  updated_at: string
}

// 项目列表响应类型
export interface ProjectListResponse {
  items: Project[]
  total: number
  skip: number
  limit: number
}

// 获取项目列表参数
export interface ProjectListParams {
  skip?: number
  limit?: number
}

// 获取项目列表
export async function fetchProjects(params: ProjectListParams = {}): Promise<ProjectListResponse> {
  const { skip = 0, limit = 100 } = params
  const res = await httpClient.get('/api/v1/projects/', {
    params: { skip, limit },
  })
  return res.data
}

// 获取所有项目（用于下拉选择等场景）
export async function fetchAllProjects(): Promise<Project[]> {
  const response = await fetchProjects({ skip: 0, limit: 1000 })
  return response.items
}

// 根据项目代码获取项目信息
export async function fetchProjectByCode(code: string): Promise<Project | null> {
  try {
    const projects = await fetchAllProjects()
    return projects.find((project) => project.code === code) || null
  } catch {
    return null
  }
}

// 创建项目请求体类型
export interface CreateProjectRequest {
  code: string
  name: string
  description: string
}

// 创建项目
export async function createProject(projectData: CreateProjectRequest): Promise<Project> {
  const res = await httpClient.post('/api/v1/projects/', projectData)
  return res.data
}

// 更新项目请求体类型
export interface UpdateProjectRequest {
  name: string
  description: string
  status: string
}

// 更新项目
export async function updateProject(
  code: string,
  projectData: UpdateProjectRequest,
): Promise<Project> {
  const res = await httpClient.put(`/api/v1/projects/${code}`, projectData)
  return res.data
}

// 删除项目
export async function deleteProject(code: string): Promise<Project> {
  const res = await httpClient.delete(`/api/v1/projects/${code}`)
  return res.data
}
