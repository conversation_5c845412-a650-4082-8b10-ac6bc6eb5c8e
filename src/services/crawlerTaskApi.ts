import httpClient from '../utils/httpClient'

// 后端返回的单条任务类型
type RawCrawlerTask = {
  id: number
  task_name: string
  source: string
  platform: string
  status: string
  log_msg: string
  total_duration: number | null
  task_progress: number
  created_at: string
  updated_at: string
  cookies: string
  project_code: string
  filters: unknown
}

// 前端表格用的任务类型
export interface CrawlerTask {
  id: number
  taskName: string
  dataSource: string
  platform: string
  status: string
  logMsg: string
  duration: number | null
  progress: number
  createdAt: string
  updatedAt: string
  cookies: string
  projectCode: string
  filters: unknown
}

// 适配函数
function adaptTask(item: RawCrawlerTask): CrawlerTask {
  return {
    id: item.id,
    taskName: item.task_name,
    dataSource: item.source,
    platform: item.platform,
    status: item.status,
    logMsg: item.log_msg,
    duration: item.total_duration,
    progress: item.task_progress,
    createdAt: item.created_at,
    updatedAt: item.updated_at,
    cookies: item.cookies,
    projectCode: item.project_code,
    filters: item.filters,
  }
}

// 搜索参数类型
export interface SearchParams {
  task_name?: string
  platform?: string
  status?: string
  project_code?: string
}

// 创建任务请求体类型
export interface CreateTaskRequest {
  task_name: string
  source: string
  platform: string
  project_code: string
  filters?: unknown
  cookies?: string
}

// 创建任务响应类型
export interface CreateTaskResponse {
  task_id: number
  message: string
  status: string
  websocket_url: string
}

// 获取爬虫任务列表
export async function fetchCrawlerTasks(params: { skip?: number; limit?: number } = {}) {
  const { skip = 0, limit = 10 } = params
  const res = await httpClient.get('/api/v1/crawler-tasks/', {
    params: { skip, limit },
  })
  const data = res.data
  return {
    items: (data.items || []).map(adaptTask),
    total: data.total,
    skip: data.skip,
    limit: data.limit,
  }
}

// 创建新爬虫任务
export async function createCrawlerTask(taskData: CreateTaskRequest): Promise<CreateTaskResponse> {
  const res = await httpClient.post('/api/v1/crawler-tasks/', taskData)
  return res.data
}

// 搜索爬虫任务
export async function searchCrawlerTasks(
  searchParams: SearchParams,
  paginationParams: { skip?: number; limit?: number } = {},
) {
  const { skip = 0, limit = 10 } = paginationParams
  const res = await httpClient.post('/api/v1/crawler-tasks/search', searchParams, {
    params: { skip, limit },
  })
  const data = res.data
  return {
    items: (data.items || []).map(adaptTask),
    total: data.total,
    skip: data.skip,
    limit: data.limit,
  }
}

// 取消正在运行的任务
export async function cancelCrawlerTask(taskId: number) {
  const res = await httpClient.post(`/api/v1/crawler-tasks/${taskId}/cancel`)
  return res.data
}

// 删除爬虫任务
export async function deleteCrawlerTask(taskId: number) {
  const res = await httpClient.delete(`/api/v1/crawler-tasks/${taskId}`)
  return res.data
}

export interface CrawlerTaskStats {
  total: number
  pending: number
  running: number
  completed: number
  failed: number
}

export async function fetchCrawlerTaskStats(projectCode?: string): Promise<CrawlerTaskStats> {
  const res = await httpClient.get('/api/v1/crawler-tasks/stats', {
    params: projectCode ? { project_code: projectCode } : {},
  })
  return res.data
}
