import httpClient from '../utils/httpClient'

// KOL标识符接口
export interface KOLIdentifier {
  social_id: string
  platform: string
  project_code: string
}

// 单个邮件发送请求接口
export interface SingleEmailRequest {
  template_code: string
  social_id: string
  platform: string
  project_code: string
}

// 批量邮件发送请求接口
export interface BatchEmailByKolsRequest {
  template_code: string
  kols: KOLIdentifier[]
}

// 邮件发送响应接口
export interface EmailSendResponse {
  success: boolean
  message: string
  sent_count?: number
  failed_count?: number
  skipped_count?: number
  details?: Record<string, string | number | boolean>[] | null
}

// 邮件预检查请求接口
export interface EmailPreCheckRequest {
  kols: KOLIdentifier[]
}

// 邮件预检查响应接口
export interface EmailPreCheckResponse {
  total_count: number
  valid_email_count: number
  invalid_email_count: number
  missing_email_count: number
  valid_kols: KOLIdentifier[]
  invalid_kols: Array<KOLIdentifier & { reason: string }>
  missing_email_kols: KOLIdentifier[]
}

// 批量邮件发送范围类型
export type EmailSendScope = 'current_page' | 'all_filtered'

// 增强的批量邮件发送请求接口
export interface EnhancedBatchEmailRequest {
  template_code: string
  scope: EmailSendScope
  search_params?: Record<string, any> // 用于全量发送时的搜索参数
  kols?: KOLIdentifier[] // 用于当前页发送时的KOL列表
}

// 发送单个邮件
export async function sendSingleEmail(request: SingleEmailRequest): Promise<EmailSendResponse> {
  const response = await httpClient.post('/api/v1/email-services/send-single', request)
  return response.data
}

// 批量发送邮件给KOL列表
export async function sendBatchEmailsToKols(
  request: BatchEmailByKolsRequest,
): Promise<EmailSendResponse> {
  const response = await httpClient.post('/api/v1/email-services/send-batch-kols', request)
  return response.data
}
