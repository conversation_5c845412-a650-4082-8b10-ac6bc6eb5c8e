import httpClient from '../utils/httpClient'

// KOL类型定义
export interface KolItem {
  id: number
  platform: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE'
  nick_name: string
  social_id: string
  project_code: string
  email: string | null
  bio: string | null
  followers_count: number | null
  likes_count: number | null
  source: string
  engagement_rate: number | null
  mean_views_k: number | null
  median_views_k: number | null
  tier: 'NANO' | 'MICRO' | 'MID' | 'MACRO' | 'MEGA'
  hashtags: string[]
  captions: string[]
  topics: string[]
  crawler_task_id: number | null
  note: string | null
  bio_parsed_at: string | null
  bio_extracted_email: string | null
  ai_score: number | null
  ai_matched: boolean | null
  ai_scored_at: string | null
  nano_email_fetched_at: string | null
  nano_extracted_email: string | null
  email_fetch_status: 'PENDING' | 'SUCCESS' | 'FAILED'
  created_at: string
  updated_at: string
}

// KOL列表响应类型
export interface KolListResponse {
  items: KolItem[]
  total: number
  skip: number
  limit: number
}

// 获取KOL列表参数
export interface KolListParams {
  skip?: number
  limit?: number
  platform?: string
  tier?: string
  project_code?: string
  search?: string
}

export interface SearchCondition {
  field: string
  operator: string
  value: string | number | boolean | null
}

// KOL搜索参数接口 - 完全符合API文档
export interface KolSearchParams {
  kol_id?: number | null
  nick_name?: string | null
  social_id?: string | null
  platform?: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE' | null
  tier?: 'NANO' | 'MICRO' | 'MID' | 'MACRO' | 'MEGA' | null
  source?: string | null
  project_code?: string | null
  crawler_task_id?: number | null
  min_followers?: number | null
  max_followers?: number | null
  min_engagement_rate?: number | null
  max_engagement_rate?: number | null
  min_mean_views_k?: number | null
  max_mean_views_k?: number | null
  min_median_views_k?: number | null
  max_median_views_k?: number | null
  min_ai_score?: number | null
  max_ai_score?: number | null
  created_after?: string | null
  created_before?: string | null
  updated_after?: string | null
  updated_before?: string | null
  ai_matched?: boolean | null
  email?: string | null
  email_fetch_status?: string | null
  has_email?: boolean | null
  has_bio_extracted_email?: boolean | null
  has_nano_extracted_email?: boolean | null
  hashtags?: string[] | null
  topics?: string[] | null
  captions?: string[] | null
  hashtags_fuzzy?: string[] | null
  topics_fuzzy?: string[] | null
  captions_fuzzy?: string[] | null
}

// 保持向后兼容
export type KolAdvancedSearchParams = KolSearchParams

// 获取KOL列表
export async function fetchKols(params: KolListParams = {}): Promise<KolListResponse> {
  const { skip = 0, limit = 100 } = params

  const queryParams: Record<string, string | number> = { skip, limit }

  const res = await httpClient.get('/api/v1/kols/', {
    params: queryParams,
  })
  return res.data
}

// 高级搜索KOL
export async function searchKols(
  searchParams: KolSearchParams,
  paginationParams: { skip?: number; limit?: number } = {},
): Promise<KolListResponse> {
  const params = {
    skip: paginationParams.skip || 0,
    limit: paginationParams.limit || 100,
  }

  const response = await httpClient.post('/api/v1/kols/search', searchParams, {
    params,
  })
  return response.data
}

// 根据ID获取KOL详情
export async function fetchKolById(id: number): Promise<KolItem> {
  const res = await httpClient.get(`/api/v1/kols/${id}/`)
  return res.data
}

// 创建KOL
export interface CreateKolRequest {
  social_id: string
  nick_name: string
  platform: string
  project_code: string
  source?: string
  email?: string
  followers_count: number
  likes_count?: number
  bio?: string
}

export async function createKol(kolData: CreateKolRequest): Promise<KolItem> {
  const response = await httpClient.post('/api/v1/kols/', kolData)
  return response.data
}

// 更新KOL
export interface UpdateKolRequest {
  email?: string
  bio?: string
  hashtags?: string[]
  topics?: string[]
  captions?: string[]
  note?: string
}

export async function updateKol(
  socialId: string,
  platform: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE',
  projectCode: string,
  kolData: UpdateKolRequest,
): Promise<KolItem> {
  const response = await httpClient.put(
    `/api/v1/kols/${socialId}/${platform}/${projectCode}`,
    kolData,
  )
  return response.data
}

// 删除KOL
export async function deleteKol(id: number): Promise<void> {
  await httpClient.delete(`/api/v1/kols/${id}/`)
}

// 获取需要邮箱获取的KOL列表
export interface NeedEmailFetchParams {
  skip?: number
  limit?: number
}

export async function fetchKolsNeedEmailFetch(
  params: NeedEmailFetchParams = {},
): Promise<KolListResponse> {
  const { skip = 0, limit = 100 } = params

  const queryParams: Record<string, string | number> = { skip, limit }

  const res = await httpClient.get('/api/v1/kols/need-email-fetch', {
    params: queryParams,
  })
  return res.data
}

// 批量评估结果项
export interface BatchEvaluateResult {
  kol_id: number
  score: number | null
  ai_matched: boolean
  success: boolean
  message: string
}

// 批量评估响应
export interface BatchEvaluateResponse {
  total_count: number
  success_count: number
  failed_count: number
  results: BatchEvaluateResult[]
}

// 批量评估请求参数
export interface BatchEvaluateRequest {
  kol_ids: number[]
  prompt: string
}

// 任务状态接口
export interface TaskStatus {
  task_id: string
  status: 'running' | 'completed' | 'failed'
  progress: number
  total_count: number
  processed_count: number
  success_count: number
  failed_count: number
  message: string
  results?: BatchEvaluateResult[]
  created_at: string
  updated_at: string
}

// 批量评估响应（包含任务ID）
export interface BatchEvaluateTaskResponse {
  task_id: string
  message: string
}

// 批量评估KOL
export async function batchEvaluateKols(
  request: BatchEvaluateRequest,
): Promise<BatchEvaluateTaskResponse> {
  const response = await httpClient.post('/api/v1/kol-match/start-batch-evaluate', request)
  return response.data
}

// 查询任务状态
export async function getMatchTaskStatus(taskId: string): Promise<TaskStatus> {
  const response = await httpClient.get(`/api/v1/kol-match/match-task-status/${taskId}`)
  return response.data
}

// 批量 Nano 任务相关接口
export interface BatchNanoRequest {
  kol_ids: number[]
}

export interface BatchNanoResult {
  kol_id: number
  email: string | null
  topics: string[]
  success: boolean
  message: string
}

export interface BatchNanoTaskResponse {
  task_id: string
  message: string
}

// 启动批量 Nano 任务
export async function batchNanoKols(request: BatchNanoRequest): Promise<BatchNanoTaskResponse> {
  const response = await httpClient.post('/api/v1/nano-tools/start-batch-fetch-email', request)
  return response.data
}

// 获取批量 Nano 任务状态
export async function getNanoTaskStatus(taskId: string): Promise<TaskStatus> {
  const response = await httpClient.get(`/api/v1/nano-tools/task-status/${taskId}`)
  return response.data
}

// Nano邮箱获取响应
export interface NanoEmailFetchResponse {
  kol_id: number
  email: string
  success: boolean
  message: string
}

// Nano邮箱获取请求参数
export interface NanoEmailFetchRequest {
  kol_id: number
}

// 单个Nano邮箱获取
export async function fetchNanoEmail(
  request: NanoEmailFetchRequest,
): Promise<NanoEmailFetchResponse> {
  const response = await httpClient.post('/api/v1/nano-tools/fetch-email', request)
  return response.data
}

// 批量 Nano 邮箱获取响应类型
export interface BatchNanoEmailFetchResponse {
  total_count: number
  success_count: number
  failed_count: number
  results: BatchNanoEmailResult[]
}

// 批量 Nano 邮箱获取结果类型
export interface BatchNanoEmailResult {
  kol_id: number
  score: string
  ai_matched: boolean
  success: boolean
  message: string
}

// 批量 Nano 邮箱获取请求类型
export interface BatchNanoEmailFetchRequest {
  kol_ids: number[]
}

// 批量 Nano 邮箱获取
export async function batchFetchNanoEmail(
  request: BatchNanoEmailFetchRequest,
): Promise<BatchNanoEmailFetchResponse> {
  const response = await httpClient.post('/api/v1/nano-tools/start-batch-fetch-email', request)
  return response.data
}

// 数据转换函数 - 将API数据转换为前端使用的格式
export function transformKolData(apiKol: KolItem) {
  return {
    id: apiKol.id,
    platform: apiKol.platform, // 保持原始格式，让前端处理显示
    nick_name: apiKol.nick_name,
    full_name: apiKol.nick_name, // API中没有full_name，使用nick_name
    avatar_url: '', // API中没有avatar_url
    bio: apiKol.bio || '',
    followers_count: apiKol.followers_count || 0,
    engagement_rate: apiKol.engagement_rate ? apiKol.engagement_rate * 100 : 0, // 转换为百分比
    tier: apiKol.tier, // 保持原始格式，让前端处理显示
    mean_views_k: apiKol.mean_views_k || 0,
    email: apiKol.email || '',
    hashtags: apiKol.hashtags || [],
    topics: apiKol.topics || [],
    note: apiKol.note || '',
    created_at: apiKol.created_at,
    updated_at: apiKol.updated_at,
  }
}
