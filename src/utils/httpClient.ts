import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const httpClient = axios.create({
  timeout: 10000, // 10秒超时
})

// 请求拦截器
httpClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token 等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
httpClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('HTTP请求错误:', error)
    
    // 处理网络错误
    if (!error.response) {
      // 网络错误或服务器无响应
      if (error.code === 'ERR_NETWORK' || error.message.includes('Network Error')) {
        console.warn('后端服务不可用，但不阻塞页面导航')
        // 不显示错误消息，让组件自己处理
        return Promise.reject(new Error('BACKEND_UNAVAILABLE'))
      }
    }
    
    // 处理 HTTP 状态码错误
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录跳转
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(`请求失败: ${status}`)
      }
    }
    
    return Promise.reject(error)
  }
)

export default httpClient
