<template>
  <el-drawer
    v-model="visible"
    :title="null"
    direction="rtl"
    size="50%"
    class="email-send-detail-drawer"
    :before-close="handleClose"
  >
    <div v-if="emailSendData" class="perf-header">
      <div class="platform-icon">
        <img v-if="platformImg" :src="platformImg" :alt="platformLabel" class="platform-img-full" />
        <el-icon v-else :size="40">
          <component :is="platformIcon" />
        </el-icon>
      </div>
      <div class="header-main">
        <div class="kol-id-row">
          <span class="kol-id">{{ emailSendData.kol_id }}</span>
          <el-tag :type="getStatusType(emailSendData.send_status)" class="status-tag">
            {{ getStatusLabel(emailSendData.send_status) }}
          </el-tag>
        </div>
        <div class="sub-info">
          <span v-if="emailSendData.project_code">项目：{{ emailSendData.project_code }}</span>
          <span>平台：{{ platformLabel }}</span>
          <span v-if="emailSendData.send_date"
            >发送时间：{{ formatDate(emailSendData.send_date) }}</span
          >
        </div>
      </div>
      <div class="header-actions">
        <el-button type="success" @click="handleSave" class="save-btn-top">
          <el-icon><Check /></el-icon>
          保存修改
        </el-button>
      </div>
    </div>

    <div v-if="emailSendData" class="content-scroll-area">
      <!-- 邮箱信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>邮箱信息</h3>
        </template>
        <div class="info-row">
          <span class="info-label">发件人邮箱：</span>
          <span class="info-value">{{ emailSendData.from_email || '暂未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">收件人邮箱：</span>
          <span class="info-value">{{ emailSendData.to_email || '暂未设置' }}</span>
        </div>
      </el-card>

      <!-- 备注信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>备注信息</h3>
        </template>
        <el-input
          v-model="localNote"
          type="textarea"
          :rows="4"
          placeholder="请输入邮件发送备注信息..."
          maxlength="500"
          show-word-limit
          resize="none"
        />
      </el-card>

      <!-- 元数据卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>元数据信息</h3>
        </template>
        <div class="info-row">
          <span class="info-label">邮件ID：</span>
          <span class="info-value">{{ emailSendData.id }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">创建时间：</span>
          <span class="info-value">{{ formatDate(emailSendData.created_at) }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">最后更新时间：</span>
          <span class="info-value">{{ formatDate(emailSendData.updated_at) }}</span>
        </div>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Check, VideoPlay, Picture, Monitor } from '@element-plus/icons-vue'

// 平台图片静态路径（放public目录，避免vite/ts类型问题）
const tiktokIcon = '/assets/tiktok_icon.png'
const instagramIcon = '/assets/instagram_icon.png'
const youtubeIcon = '/assets/youtube_icon.png'

interface EmailSendItem {
  id: number
  platform: 'tiktok' | 'instagram' | 'youtube'
  kol_id: string
  send_status: 'pending' | 'sent' | 'failed'
  send_date: string | null
  project_code: string | null
  template_code: string | null
  from_email: string | null
  to_email: string | null
  note: string | null
  created_at: string
  updated_at: string
}

interface Props {
  modelValue: boolean
  emailSendData: EmailSendItem | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'resend', emailSend: EmailSendItem): void
  (e: 'save', emailSend: EmailSendItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localNote = ref('')

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听数据变化，更新本地备注
watch(
  () => props.emailSendData,
  (newData) => {
    if (newData) {
      localNote.value = newData.note || ''
    }
  },
  { immediate: true },
)

const handleClose = () => {
  visible.value = false
}

const handleSave = () => {
  if (props.emailSendData) {
    const updatedData: EmailSendItem = {
      ...props.emailSendData,
      note: localNote.value,
      updated_at: new Date().toISOString(),
    }
    emit('save', updatedData)
    visible.value = false
  }
}

// 平台icon和图片
const platformImg = computed(() => {
  if (!props.emailSendData) return ''
  switch (props.emailSendData.platform) {
    case 'tiktok':
      return tiktokIcon
    case 'instagram':
      return instagramIcon
    case 'youtube':
      return youtubeIcon
    default:
      return ''
  }
})
const platformIcon = computed(() => {
  if (!props.emailSendData) return VideoPlay
  switch (props.emailSendData.platform) {
    case 'tiktok':
      return VideoPlay
    case 'instagram':
      return Picture
    case 'youtube':
      return Monitor
    default:
      return VideoPlay
  }
})
const platformLabel = computed(() => {
  if (!props.emailSendData) return ''
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
  }
  return labels[props.emailSendData.platform] || props.emailSendData.platform
})

// 工具方法
const formatDate = (dateString?: string | null) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待发送',
    sent: '已发送',
    failed: '发送失败',
  }
  return labels[status] || status
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    sent: 'success',
    failed: 'danger',
  }
  return types[status] || ''
}
</script>

<style scoped>
.performance-detail-drawer .perf-header,
.email-send-detail-drawer .perf-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 12px 32px;
  background: #f8fafc;
  color: #1f2937;
  border-radius: 12px 0 0 0;
  min-height: 80px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}
.platform-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-main {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.kol-id-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}
.status-tag {
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  color: #6366f1;
}
.sub-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 15px;
  color: #6b7280;
}
.header-actions {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 180px;
}
.edit-main-button {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #6366f1;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 24px;
  transition: all 0.3s ease;
}
.edit-main-button:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.06);
}
.save-btn-top {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #409eff;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 24px;
  transition: all 0.3s ease;
}
.save-btn-top:hover {
  background: #dbeafe;
  color: #1d4ed8;
}
.content-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px 32px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.info-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: #fff;
}
.info-card .el-card__header {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 12px 12px 0 0;
}
.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}
.info-row:last-child {
  border-bottom: none;
}
.info-label {
  color: #888;
  min-width: 100px;
  font-weight: 500;
}
.info-value {
  color: #222;
  font-weight: 500;
}
.info-card :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}
.info-card :deep(.el-textarea__inner:hover) {
  border-color: #667eea;
}
.info-card :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
.platform-img-full {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}
@media (max-width: 900px) {
  .perf-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 16px 8px 16px;
  }
  .content-scroll-area {
    padding: 16px 20px 24px;
    gap: 12px;
  }
}
</style>
