<template>
  <el-drawer
    v-model="visible"
    :title="null"
    direction="rtl"
    size="50%"
    class="candidate-detail-drawer"
    :before-close="handleClose"
  >
    <!-- 顶部主信息 -->
    <div class="candidate-header">
      <div class="candidate-header-left">
        <div class="platform-icon">
          <el-icon v-if="candidateData?.platform === 'TIKTOK'" color="#ff0050" :size="36"
            ><VideoPlay
          /></el-icon>
          <el-icon v-else-if="candidateData?.platform === 'INSTAGRAM'" color="#e4405f" :size="36"
            ><Picture
          /></el-icon>
          <el-icon v-else-if="candidateData?.platform === 'YOUTUBE'" color="#ff0000" :size="36"
            ><Monitor
          /></el-icon>
        </div>
        <div class="platform-label">{{ getPlatformLabel(candidateData?.platform) }}</div>
      </div>
      <div class="candidate-header-center">
        <div class="main-row">
          <span class="candidate-id">{{ candidateData?.social_id || '-' }}</span>
          <el-tag
            v-if="candidateData?.follow_up_status"
            :type="getFollowUpStatusType(candidateData.follow_up_status)"
            class="tier-tag"
            >{{ getFollowUpStatusLabel(candidateData.follow_up_status) }}</el-tag
          >
        </div>
        <div class="sub-row">
          <span
            v-if="candidateData?.nick_name && candidateData?.nick_name !== candidateData?.kol_id"
            class="nick-name"
            >（{{ candidateData.nick_name }}）</span
          >
          <el-tag v-if="candidateData?.project_code" class="project-tag">{{
            candidateData.project_code
          }}</el-tag>
          <span v-if="candidateData?.reply_email_addr" class="email-info"
            ><el-icon><User /></el-icon>{{ candidateData.reply_email_addr }}</span
          >
          <span v-if="candidateData?.tracker" class="source-info"
            ><el-icon><Document /></el-icon>{{ candidateData.tracker }}</span
          >
        </div>
      </div>
      <div class="candidate-header-right">
        <el-button type="primary" @click="handleEdit" size="large" class="edit-main-button">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>

    <!-- 跟进备注卡片，紧跟主信息区下方 -->
    <el-card class="candidate-bio-card" shadow="hover" v-if="candidateData?.follow_up_note">
      <div class="bio-title">
        <el-icon><User /></el-icon>跟进备注
      </div>
      <div class="bio-content">{{ candidateData?.follow_up_note }}</div>
    </el-card>

    <div class="candidate-content-scroll" v-if="candidateData">
      <!-- 核心数据区 -->
      <el-card class="candidate-metrics-card" shadow="hover">
        <div class="candidate-metrics-grid">
          <div class="metric-block">
            <div class="metric-label">跟进状态</div>
            <div class="metric-value">
              <el-tag :type="getFollowUpStatusType(candidateData.follow_up_status)">
                {{ getFollowUpStatusLabel(candidateData.follow_up_status) }}
              </el-tag>
            </div>
          </div>
          <div class="metric-block">
            <div class="metric-label">发送轮次</div>
            <div class="metric-value">{{ candidateData.send_round || '-' }}</div>
          </div>
          <div class="metric-block">
            <div class="metric-label">跟进人</div>
            <div class="metric-value">{{ candidateData.tracker || '-' }}</div>
          </div>
        </div>
      </el-card>

      <!-- 基本信息区 -->
      <el-card class="candidate-info-card" shadow="hover">
        <div class="info-title">
          <el-icon><User /></el-icon>基本信息
        </div>
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="KOL ID">{{ candidateData.kol_id }}</el-descriptions-item>
          <el-descriptions-item label="KOL Name">{{
            candidateData.nick_name
          }}</el-descriptions-item>
          <el-descriptions-item label="Email">{{
            candidateData.reply_email_addr || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="Status">
            <el-tag :type="getFollowUpStatusType(candidateData.follow_up_status)">{{
              getFollowUpStatusLabel(candidateData.follow_up_status)
            }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Tracker">{{
            candidateData.tracker || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="Project Code">{{
            candidateData.project_code || '-'
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 候选人详细信息 -->
      <el-card class="candidate-detail-card" shadow="hover">
        <div class="info-title">
          <el-icon><User /></el-icon>候选人详细信息
        </div>
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="跟进状态">
            <el-tag :type="getFollowUpStatusType(candidateData.follow_up_status)">
              {{ getFollowUpStatusLabel(candidateData.follow_up_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送轮次">{{
            candidateData.send_round || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="邮件ID">{{
            candidateData.latest_email_send_id || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="邮件线程">
            <span class="thread-id">{{ candidateData.thread_id || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="首次联系">{{
            formatDateSafe(candidateData.first_contact_date)
          }}</el-descriptions-item>
          <el-descriptions-item label="最后联系">{{
            formatDateSafe(candidateData.last_contact_date)
          }}</el-descriptions-item>
          <el-descriptions-item label="社交媒体ID">{{
            candidateData.social_id || '-'
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <!-- 备注 -->
      <el-card class="candidate-note-card" shadow="hover" v-if="candidateData.note">
        <div class="note-title">
          <el-icon><Document /></el-icon>Note
        </div>
        <div class="note-content">{{ candidateData.note }}</div>
      </el-card>
      <!-- 系统时间 -->
      <el-card class="candidate-system-card" shadow="hover">
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="Created At">{{
            formatDateSafe(candidateData.created_at)
          }}</el-descriptions-item>
          <el-descriptions-item label="Updated At">{{
            formatDateSafe(candidateData.updated_at)
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 编辑表单弹窗（重构样式） -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑候选人信息"
      width="900px"
      :before-close="handleEditCancel"
      class="edit-dialog"
      append-to-body
    >
      <div class="edit-form-container" v-loading="editLoading">
        <!-- 基本信息分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <el-icon><User /></el-icon>
            <span>基本信息</span>
          </div>
          <el-form
            ref="editFormRef"
            :model="editForm"
            :rules="editRules"
            label-width="120px"
            class="edit-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="KOL ID" prop="social_id">
                  <el-input v-model="editForm.social_id" placeholder="请输入KOL ID" size="large" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KOL Name" prop="nick_name">
                  <el-input
                    v-model="editForm.nick_name"
                    placeholder="请输入KOL Name（可选）"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Platform" prop="platform">
                  <el-select
                    v-model="editForm.platform"
                    placeholder="请选择平台"
                    style="width: 100%"
                    size="large"
                    clearable
                  >
                    <el-option label="TikTok" value="TIKTOK" />
                    <el-option label="Instagram" value="INSTAGRAM" />
                    <el-option label="YouTube" value="YOUTUBE" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Email" prop="reply_email_addr">
                  <el-input
                    v-model="editForm.reply_email_addr"
                    placeholder="请输入邮箱地址"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 其他信息分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <el-icon><Document /></el-icon>
            <span>其他信息</span>
          </div>
          <el-form :model="editForm" label-width="120px" class="edit-form">
            <el-form-item label="跟进状态" prop="follow_up_status">
              <el-select
                v-model="editForm.follow_up_status"
                placeholder="请选择跟进状态"
                style="width: 100%"
                size="large"
              >
                <el-option label="待跟进" value="PENDING" />
                <el-option label="跟进中" value="PROCESSING" />
                <el-option label="起草中" value="DRAFTING" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已付款" value="PAID" />
              </el-select>
            </el-form-item>
            <el-form-item label="跟进备注" prop="follow_up_note">
              <el-input
                v-model="editForm.follow_up_note"
                type="textarea"
                :rows="4"
                placeholder="请输入跟进备注信息"
                maxlength="1000"
                show-word-limit
                resize="none"
              />
            </el-form-item>
            <el-form-item label="Note" prop="note">
              <el-input
                v-model="editForm.note"
                type="textarea"
                :rows="4"
                placeholder="请输入内部备注信息"
                maxlength="1000"
                show-word-limit
                resize="none"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditCancel" :disabled="editLoading">取消</el-button>
          <el-button type="primary" @click="handleEditSave" :loading="editLoading">
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Edit, User, VideoPlay, Picture, Monitor, Document } from '@element-plus/icons-vue'

// 导入API类型和函数
import type { CandidateItem, UpdateCandidateRequest } from '../services/candidateApi'
import { updateCandidate } from '../services/candidateApi'

// Props
interface Props {
  modelValue: boolean
  candidateData?: CandidateItem | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  candidateData: null,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  edit: [candidate: CandidateItem]
  save: [candidate: CandidateItem]
}>()

// 响应式数据
const showEditDialog = ref(false)
const editLoading = ref(false)
const editFormRef = ref<FormInstance>()

// 编辑表单数据
const editForm = reactive<Partial<CandidateItem>>({
  id: 0,
  platform: 'TIKTOK',
  kol_id: '',
  social_id: '',
  nick_name: '',
  project_code: '',
  reply_email_addr: '',
  follow_up_status: 'PENDING',
  follow_up_note: '',
  first_contact_date: '',
  last_contact_date: '',
  send_round: 0,
  latest_email_send_id: undefined,
  thread_id: '',
  tracker: '',
  note: '',
  created_at: '',
  updated_at: '',
})

// 表单验证规则
const editRules: FormRules = {
  nick_name: [{ min: 2, max: 50, message: '昵称长度在 2 到 50 个字符', trigger: 'blur' }],
  project_code: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
  thread_id: [{ required: true, message: '请输入邮件线程ID', trigger: 'blur' }],
  reply_email_addr: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
  follow_up_status: [{ required: true, message: '请选择跟进状态', trigger: 'change' }],
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  if (!props.candidateData) return

  // 复制数据到编辑表单
  Object.assign(editForm, props.candidateData)
  showEditDialog.value = true

  emit('edit', props.candidateData)
}

const handleEditCancel = () => {
  showEditDialog.value = false
  // 重置表单
  nextTick(() => {
    editFormRef.value?.resetFields()
  })
}

const handleEditSave = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    editLoading.value = true

    // 构建更新请求参数
    const updateData: UpdateCandidateRequest = {
      nick_name: editForm.nick_name,
      social_id: editForm.social_id,
      reply_email_addr: editForm.reply_email_addr,
      platform: editForm.platform,
      follow_up_status: editForm.follow_up_status,
      follow_up_note: editForm.follow_up_note,
      tracker: editForm.tracker,
      note: editForm.note,
    }

    // 调用更新接口
    const response = await updateCandidate(editForm.id!, updateData)

    // 发出保存事件，传递服务器返回的更新后数据
    emit('save', response)

    ElMessage.success('候选人信息更新成功')
    showEditDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查输入信息')
  } finally {
    editLoading.value = false
  }
}

const getPlatformLabel = (platform: string | null | undefined) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
    TIKTOK: 'TikTok',
    INSTAGRAM: 'Instagram',
    YOUTUBE: 'YouTube',
  }
  return labels[platform] || platform
}

const getFollowUpStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    PENDING: '待跟进',
    PROCESSING: '跟进中',
    DRAFTING: '起草中',
    COMPLETED: '已完成',
    PAID: '已付款',
    NOT_TARGET: '非目标',
    OFF: '已关闭',
  }
  return labels[status] || status
}

const getFollowUpStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'warning',
    PROCESSING: 'info',
    DRAFTING: 'info',
    COMPLETED: 'success',
    PAID: 'success',
    NOT_TARGET: 'danger',
    OFF: 'danger',
  }
  return types[status] || 'default'
}

const formatDateSafe = (dateString: string | undefined) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Define component name for better debugging
defineOptions({
  name: 'CandidateDetailDrawer',
})
</script>

<style scoped>
.candidate-detail-drawer :deep(.el-drawer) {
  border-radius: 12px 0 0 12px;
  background: #f8fafc;
}
.candidate-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 12px 32px;
  background: #f8fafc;
  color: #1f2937;
  border-radius: 12px 0 0 0;
  min-height: 80px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}
.candidate-header-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
}
.platform-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  margin-bottom: 4px;
}
.platform-label {
  font-size: 13px;
  color: #6b7280;
  margin-top: 2px;
}
.candidate-header-center {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.main-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}
.tier-tag {
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  color: #6366f1;
}
.sub-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  color: #6b7280;
  flex-wrap: wrap;
}
.nick-name {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}
.project-tag {
  font-size: 13px;
  color: #3b4a6b;
  background: #eaf1fb;
  border-radius: 6px;
  padding: 2px 10px;
  margin-left: 0;
  border: none;
  font-weight: 500;
}
.email-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
.source-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
.candidate-header-right {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 180px;
}
.edit-main-button {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #6366f1;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 24px;
  transition: all 0.3s ease;
}
.edit-main-button:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.06);
}
.candidate-content-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 0 32px 32px;
}
.candidate-metrics-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.candidate-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 20px;
}
.metric-block {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.06);
  padding: 18px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}
.metric-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}
.metric-value {
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
}
.candidate-info-card {
  margin-bottom: 20px;
  border-radius: 14px;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.candidate-bio-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.bio-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}
.bio-content {
  font-size: 15px;
  line-height: 1.6;
  color: #4b5563;
  word-break: break-all;
  white-space: pre-wrap;
}
.candidate-note-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.note-title {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.note-content {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.7;
  background: #f9fafb;
  border-radius: 8px;
  padding: 14px 16px;
}
.candidate-detail-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.candidate-system-card {
  border-radius: 14px;
}
.platform-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}
.thread-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
}
.email-preview {
  max-height: 100px;
  overflow-y: auto;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: #4b5563;
}
.review-status {
  padding: 16px 0;
  text-align: center;
}
/* 编辑弹窗样式 */
.edit-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}
.edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}
.edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}
.edit-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}
.edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}
.edit-form-container {
  background: #f8fafc;
  padding: 24px;
}
.edit-section-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.edit-section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}
.edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}
.edit-form :deep(.el-input__wrapper),
.edit-form :deep(.el-textarea__inner),
.edit-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}
.edit-form :deep(.el-input__wrapper:hover),
.edit-form :deep(.el-textarea__inner:hover),
.edit-form :deep(.el-select .el-input__wrapper:hover) {
  border-color: #667eea;
}
.edit-form :deep(.el-input__wrapper.is-focus),
.edit-form :deep(.el-textarea__inner:focus),
.edit-form :deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
.edit-form :deep(.el-form-item) {
  margin-bottom: 20px;
}
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 需要审核字段样式 */
.form-hint {
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.warning-text {
  color: #e6a23c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.success-text {
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-hint .el-icon {
  font-size: 14px;
}
@media (max-width: 900px) {
  .candidate-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 16px 8px 16px;
  }
  .candidate-content-scroll {
    padding: 0 12px 24px;
  }
  .candidate-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  .edit-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }
  .edit-form-container {
    padding: 16px;
  }
  .edit-section-card {
    margin-bottom: 14px;
  }
}
@media (max-width: 600px) {
  .candidate-header {
    padding: 10px 6px 6px 6px;
  }
  .candidate-content-scroll {
    padding: 0 4px 16px;
  }
  .candidate-metrics-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  .edit-dialog :deep(.el-dialog__header) {
    padding: 10px 8px;
  }
  .edit-form-container {
    padding: 8px;
  }
}

/* 编辑弹窗样式 */
.edit-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.edit-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 编辑表单容器 */
.edit-form-container {
  background: #f8fafc;
  padding: 24px;
}

.edit-form {
  background: transparent;
}

/* 表单分组样式 */
.form-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding: 24px;
}

/* 表单项样式优化 */
.edit-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.edit-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.edit-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.edit-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.edit-form :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.edit-form :deep(.el-textarea__inner:hover) {
  border-color: #667eea;
}

.edit-form :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.edit-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}
</style>
