<template>
  <el-drawer
    v-model="visible"
    :title="null"
    direction="rtl"
    size="50%"
    class="kol-detail-drawer"
    :before-close="handleClose"
  >
    <!-- 顶部主信息 -->
    <div class="kol-header">
      <div class="kol-header-left">
        <div class="platform-icon">
          <el-icon v-if="kolData?.platform === 'TIKTOK'" color="#ff0050" :size="36"
            ><VideoPlay
          /></el-icon>
          <el-icon v-else-if="kolData?.platform === 'INSTAGRAM'" color="#e4405f" :size="36"
            ><Picture
          /></el-icon>
          <el-icon v-else-if="kolData?.platform === 'YOUTUBE'" color="#ff0000" :size="36"
            ><Monitor
          /></el-icon>
        </div>
        <div class="platform-label">{{ getPlatformLabel(kolData?.platform) }}</div>
      </div>
      <div class="kol-header-center">
        <div class="main-row">
          <span class="kol-id">{{ kolData?.social_id }}</span>
          <el-tag v-if="kolData?.tier" :type="getTierType(kolData.tier)" class="tier-tag">{{
            getTierLabel(kolData.tier)
          }}</el-tag>
        </div>
        <div class="sub-row">
          <span
            v-if="kolData?.nick_name && kolData?.nick_name !== kolData?.social_id"
            class="nick-name"
            >（{{ kolData.nick_name }}）</span
          >
          <el-tag v-if="kolData?.project_code" class="project-tag">{{
            kolData.project_code
          }}</el-tag>
          <span v-if="kolData?.email" class="email-info"
            ><el-icon><User /></el-icon>{{ kolData.email }}</span
          >
          <span v-if="kolData?.source" class="source-info"
            ><el-icon><Document /></el-icon>{{ kolData.source }}</span
          >
        </div>
      </div>
      <div class="kol-header-right">
        <el-button type="primary" @click="handleEdit" size="large" class="edit-main-button">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>

    <!-- 个人简介卡片，紧跟主信息区下方 -->
    <el-card class="kol-bio-card" shadow="hover" v-if="kolData?.bio">
      <div class="bio-title">
        <el-icon><User /></el-icon>Bio
      </div>
      <div class="bio-content">{{ kolData?.bio }}</div>
    </el-card>

    <div class="kol-content-scroll" v-if="kolData">
      <!-- 核心数据区 -->
      <el-card class="kol-metrics-card" shadow="hover">
        <div class="kol-metrics-grid">
          <div class="metric-block">
            <div class="metric-label">Followers</div>
            <div class="metric-value">{{ formatNumberWithUnit(kolData.followers_count) }}</div>
          </div>
          <div class="metric-block">
            <div class="metric-label">Likes</div>
            <div class="metric-value">{{ formatNumberWithUnit(kolData.likes_count) }}</div>
          </div>
          <div class="metric-block">
            <div class="metric-label">Engagement Rate</div>
            <div class="metric-value" :class="getEngagementClass(kolData.engagement_rate)">
              {{ formatEngagementRate(kolData.engagement_rate) }}
            </div>
          </div>
          <div class="metric-block">
            <div class="metric-label">Mean Views</div>
            <div class="metric-value">{{ formatViewsNumber(kolData.mean_views_k) }}</div>
          </div>
          <div class="metric-block">
            <div class="metric-label">Median Views</div>
            <div class="metric-value">{{ formatViewsNumber(kolData.median_views_k) }}</div>
          </div>
        </div>
      </el-card>

      <!-- 基本信息区 -->
      <el-card class="kol-info-card" shadow="hover">
        <div class="info-title">
          <el-icon><User /></el-icon>基本信息
        </div>
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="KOL ID">{{ kolData.social_id }}</el-descriptions-item>
          <el-descriptions-item label="KOL Name">{{ kolData.nick_name }}</el-descriptions-item>
          <el-descriptions-item label="Email">{{ kolData.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="Tier">
            <el-tag :type="getTierType(kolData.tier)">{{ getTierLabel(kolData.tier) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Source">{{ kolData.source || '-' }}</el-descriptions-item>
          <el-descriptions-item label="Project Code">{{
            kolData.project_code || '-'
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 标签/文案/话题区 -->
      <el-card class="kol-tags-card" shadow="hover">
        <div class="kol-tags-section" v-if="kolData.hashtags && kolData.hashtags.length">
          <span class="tags-title"
            ><el-icon><PriceTag /></el-icon>Hashtags</span
          >
          <div class="tags-list">
            <el-tag
              v-for="tag in kolData.hashtags"
              :key="tag"
              class="tag-item"
              type="primary"
              effect="light"
              >#{{ tag }}</el-tag
            >
          </div>
        </div>
        <div class="kol-tags-section" v-if="kolData.captions && kolData.captions.length">
          <span class="tags-title"
            ><el-icon><Document /></el-icon>Captions</span
          >
          <div class="tags-list">
            <el-tag
              v-for="caption in kolData.captions"
              :key="caption"
              class="tag-item"
              type="info"
              effect="light"
              >{{ caption }}</el-tag
            >
          </div>
        </div>
        <div class="kol-tags-section" v-if="kolData.topics && kolData.topics.length">
          <span class="tags-title"
            ><el-icon><CollectionTag /></el-icon>Topics</span
          >
          <div class="tags-list">
            <el-tag
              v-for="topic in kolData.topics"
              :key="topic"
              class="tag-item"
              type="success"
              effect="light"
              >{{ topic }}</el-tag
            >
          </div>
        </div>
      </el-card>

      <!-- 备注 -->
      <el-card class="kol-note-card" shadow="hover" v-if="kolData.note">
        <div class="note-title">
          <el-icon><Document /></el-icon>Note
        </div>
        <div class="note-content">{{ kolData.note }}</div>
      </el-card>

      <!-- AI与系统信息（折叠） -->
      <el-collapse class="kol-ai-collapse">
        <el-collapse-item title="AI与系统信息">
          <el-descriptions :column="2" border size="default">
            <el-descriptions-item label="AI分数">{{
              kolData.ai_score ?? '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="AI匹配">
              <el-tag v-if="kolData.ai_matched === true" type="success">是</el-tag>
              <el-tag v-else-if="kolData.ai_matched === false" type="danger">否</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="邮箱获取状态">{{
              kolData.email_fetch_status ?? '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="简介解析时间">{{
              formatDateSafe(kolData.bio_parsed_at)
            }}</el-descriptions-item>
            <el-descriptions-item label="简介提取邮箱">{{
              kolData.bio_extracted_email ?? '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="AI评分时间">{{
              formatDateSafe(kolData.ai_scored_at)
            }}</el-descriptions-item>
            <el-descriptions-item label="Nano邮箱获取时间">{{
              formatDateSafe(kolData.nano_email_fetched_at)
            }}</el-descriptions-item>
            <el-descriptions-item label="Nano提取邮箱">{{
              kolData.nano_extracted_email ?? '-'
            }}</el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>

      <!-- 系统时间 -->
      <el-card class="kol-system-card" shadow="hover">
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="Created At">{{
            formatDateSafe(kolData.created_at)
          }}</el-descriptions-item>
          <el-descriptions-item label="Updated At">{{
            formatDateSafe(kolData.updated_at)
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 编辑表单弹窗（重构样式） -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑 KOL 信息"
      width="900px"
      :before-close="handleEditCancel"
      class="edit-dialog"
      append-to-body
    >
      <div class="edit-form-container" v-loading="editLoading">
        <!-- 基本信息分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <el-icon><User /></el-icon>
            <span>基本信息</span>
          </div>
          <el-form
            ref="editFormRef"
            :model="editForm"
            :rules="editRules"
            label-width="120px"
            class="edit-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="KOL ID" prop="social_id">
                  <el-input
                    v-model="editForm.social_id"
                    placeholder="KOL ID"
                    size="large"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KOL Name" prop="nick_name">
                  <el-input
                    v-model="editForm.nick_name"
                    placeholder="请输入KOL Name"
                    size="large"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Email" prop="email">
                  <el-input v-model="editForm.email" placeholder="请输入邮箱地址" size="large" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="Bio" prop="bio">
                  <el-input
                    v-model="editForm.bio"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入个人简介"
                    maxlength="500"
                    show-word-limit
                    resize="none"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 内容标签分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <el-icon><CollectionTag /></el-icon>
            <span>内容标签</span>
          </div>
          <el-form :model="editForm" label-width="120px" class="edit-form">
            <el-form-item label="Hashtags" prop="hashtags">
              <el-select
                v-model="editForm.hashtags"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择标签"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新标签后按回车添加，支持多选
              </div>
            </el-form-item>
            <el-form-item label="Topics" prop="topics">
              <el-select
                v-model="editForm.topics"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择话题"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新话题后按回车添加，支持多选
              </div>
            </el-form-item>
            <el-form-item label="Captions" prop="captions">
              <el-select
                v-model="editForm.captions"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择文案"
                style="width: 100%"
                :reserve-keyword="false"
                size="large"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <!-- 不渲染任何el-option，用户只能输入自定义内容 -->
              </el-select>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                可以输入新文案后按回车添加，支持多选
              </div>
            </el-form-item>
          </el-form>
        </el-card>
        <!-- 备注分组 -->
        <el-card class="edit-section-card" shadow="hover">
          <div class="edit-section-header">
            <el-icon><Document /></el-icon>
            <span>其他信息</span>
          </div>
          <el-form :model="editForm" label-width="120px" class="edit-form">
            <el-form-item label="Note" prop="note">
              <el-input
                v-model="editForm.note"
                type="textarea"
                :rows="4"
                placeholder="请输入内部备注信息"
                maxlength="1000"
                show-word-limit
                resize="none"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditCancel" :disabled="editLoading">取消</el-button>
          <el-button type="primary" @click="handleEditSave" :loading="editLoading">
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormRules } from 'element-plus'
import {
  Edit,
  User,
  VideoPlay,
  Picture,
  Monitor,
  PriceTag,
  CollectionTag,
  Document,
  InfoFilled,
} from '@element-plus/icons-vue'
import { updateKol } from '../services/kolApi'

// 类型定义
interface KolItem {
  id: number
  platform: 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE'
  social_id: string
  nick_name: string
  project_code?: string
  source?: string
  tier: 'NANO' | 'MICRO' | 'MID' | 'MACRO' | 'MEGA'
  followers_count: number
  engagement_rate: number
  mean_views_k: number
  likes_count?: number
  median_views_k?: number
  email?: string
  hashtags?: string[]
  captions?: string[]
  topics?: string[]
  bio?: string
  note?: string
  ai_score?: number
  ai_matched?: boolean
  email_fetch_status?: string
  bio_parsed_at?: string
  bio_extracted_email?: string
  ai_scored_at?: string
  nano_email_fetched_at?: string
  nano_extracted_email?: string
  created_at: string
  updated_at: string
}

// Props
interface Props {
  modelValue: boolean
  kolData?: KolItem | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  kolData: null,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  edit: [kol: KolItem]
  save: [kol: KolItem]
}>()

// 响应式数据
const showEditDialog = ref(false)
const editLoading = ref(false)
const editFormRef = ref()

// 编辑表单数据
const editForm = reactive<{
  social_id?: string
  platform?: string
  project_code?: string
  nick_name?: string
  email?: string
  bio?: string
  hashtags?: string[]
  topics?: string[]
  captions?: string[]
  note?: string
}>({})

// 表单验证规则
const editRules: FormRules = {
  social_id: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 50, message: '昵称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  nick_name: [{ min: 2, max: 50, message: '真实姓名长度在 2 到 50 个字符', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  tier: [{ required: true, message: '请选择分级', trigger: 'change' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
  followers_count: [
    { required: true, message: '请输入粉丝数', trigger: 'blur' },
    { type: 'number', min: 0, message: '粉丝数不能为负数', trigger: 'blur' },
  ],
  engagement_rate: [
    { required: true, message: '请输入互动率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '互动率范围为 0-100', trigger: 'blur' },
  ],
  mean_views_k: [
    { required: true, message: '请输入平均观看数', trigger: 'blur' },
    { type: 'number', min: 0, message: '平均观看数不能为负数', trigger: 'blur' },
  ],
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  if (props.kolData) {
    Object.assign(editForm, { ...props.kolData })
    showEditDialog.value = true
    emit('edit', props.kolData)
  }
}

const handleEditCancel = () => {
  ElMessageBox.confirm('确定要放弃本次编辑吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      showEditDialog.value = false
      nextTick(() => {
        editFormRef.value?.resetFields()
      })
    })
    .catch(() => {})
}

const handleEditSave = async () => {
  try {
    await editFormRef.value?.validate()
    editLoading.value = true

    // 调用新的更新API，只传递允许编辑的字段，确保platform转换为全大写
    const updatedKol = await updateKol(
      editForm.social_id!,
      editForm.platform!.toUpperCase() as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE',
      editForm.project_code!,
      {
        email: editForm.email,
        bio: editForm.bio,
        hashtags: editForm.hashtags,
        topics: editForm.topics,
        captions: editForm.captions,
        note: editForm.note,
      },
    )

    // 发出保存事件
    emit('save', {
      ...updatedKol,
      platform: updatedKol.platform as 'TIKTOK' | 'INSTAGRAM' | 'YOUTUBE',
      tier: updatedKol.tier as 'NANO' | 'MICRO' | 'MID' | 'MACRO' | 'MEGA',
      engagement_rate: updatedKol.engagement_rate ? Number(updatedKol.engagement_rate) : 0,
      mean_views_k: updatedKol.mean_views_k ? Number(updatedKol.mean_views_k) : 0,
    } as unknown as KolItem)
    ElMessage.success('保存成功')
    showEditDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查输入信息')
  } finally {
    editLoading.value = false
  }
}

// 工具方法
const formatEngagementRate = (rate: number | string | undefined) => {
  if (rate == null || rate === '') return '-'
  const num = typeof rate === 'string' ? parseFloat(rate) : rate
  return (num * 100).toFixed(2) + '%'
}
const formatViewsNumber = (num: number | undefined) => {
  if (num == null) return '-'
  return parseFloat(num.toString()) + 'K'
}
const formatNumberWithUnit = (num: number | undefined) => {
  if (num == null) return '-'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}
const formatDateSafe = (dateString: string | undefined) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}
const getPlatformLabel = (platform: string | undefined) => {
  if (!platform) return '-'
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
  }
  return labels[platform] || platform
}
const getTierLabel = (tier: string | undefined) => {
  if (!tier) return '-'
  const labels: Record<string, string> = {
    NANO: 'NANO',
    MICRO: 'MICRO',
    MID: 'MID',
    MACRO: 'MACRO',
    MEGA: 'MEGA',
  }
  return labels[tier] || tier
}
const getTierType = (tier: string | undefined) => {
  if (!tier) return ''
  const types: Record<string, string> = {
    NANO: 'info',
    MICRO: 'warning',
    MID: 'success',
    MACRO: 'danger',
    MEGA: 'danger',
  }
  return types[tier] || 'info'
}
const getEngagementClass = (rate: number | undefined) => {
  if (rate == null) return ''
  if (rate >= 10) return 'engagement-excellent'
  if (rate >= 5) return 'engagement-good'
  if (rate >= 2) return 'engagement-normal'
  return 'engagement-low'
}

// 组件卸载时清理
onUnmounted(() => {
  // Assuming kolData and visible are reactive properties managed by the parent component
  // If not, you might need to manage their state here or pass them as props/emits
  // For now, we'll just clear them if they were managed by this component's state.
  // If they are managed by the parent, this cleanup might not be necessary or might need adjustment.
  // If kolData and visible were managed by this component's state, you would clear them here.
  // Since they are props, we don't manage their state here.
  // If you were managing kolData and visible as refs, you would clear them here:
  // kolData.value = null;
  // visible.value = false;
  // showEditDialog.value = false;
})
</script>

<style scoped>
.kol-detail-drawer :deep(.el-drawer) {
  border-radius: 12px 0 0 12px;
  background: #f8fafc;
}
.kol-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 12px 32px;
  background: #f8fafc;
  color: #1f2937;
  border-radius: 12px 0 0 0;
  min-height: 80px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}
.kol-header-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
}
.platform-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  margin-bottom: 4px;
}
.platform-label {
  font-size: 13px;
  color: #6b7280;
  margin-top: 2px;
}
.kol-header-center {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.main-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}
.tier-tag {
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  background: #f4f6fa;
  border: 1px solid #e5e7eb;
  color: #6366f1;
}
.sub-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  color: #6b7280;
  flex-wrap: wrap;
}
.nick-name {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}
.project-tag {
  font-size: 13px;
  color: #3b4a6b;
  background: #eaf1fb;
  border-radius: 6px;
  padding: 2px 10px;
  margin-left: 0;
  border: none;
  font-weight: 500;
}
.email-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
.source-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
.kol-header-right {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 180px;
}
.edit-main-button {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #6366f1;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 24px;
  transition: all 0.3s ease;
}
.edit-main-button:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.06);
}
.kol-content-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 0 32px 32px;
}
.kol-metrics-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.kol-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 20px;
}
.metric-block {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.06);
  padding: 18px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}
.metric-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}
.metric-value {
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
}
.engagement-excellent {
  color: #059669;
}
.engagement-good {
  color: #2563eb;
}
.engagement-normal {
  color: #d97706;
}
.engagement-low {
  color: #dc2626;
}
.kol-info-card {
  margin-bottom: 20px;
  border-radius: 14px;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.kol-tags-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.kol-tags-section {
  margin-bottom: 12px;
}
.tags-title {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.tag-item {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
}
.kol-bio-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.bio-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}
.bio-content {
  font-size: 15px;
  line-height: 1.6;
  color: #4b5563;
  word-break: break-all;
  white-space: pre-wrap;
}
.kol-note-card {
  margin-bottom: 20px;
  border-radius: 14px;
}
.note-title {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.note-content {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.7;
  background: #f9fafb;
  border-radius: 8px;
  padding: 14px 16px;
}
.kol-ai-collapse {
  margin-bottom: 20px;
  border-radius: 14px;
  background: #fff;
}
.kol-system-card {
  border-radius: 14px;
}
/* 编辑弹窗样式 */
.edit-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}
.edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}
.edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}
.edit-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}
.edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}
.edit-form-container {
  background: #f8fafc;
  padding: 24px;
}
.edit-section-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.edit-section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}
.edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}
.edit-form :deep(.el-input__wrapper),
.edit-form :deep(.el-textarea__inner),
.edit-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}
.edit-form :deep(.el-input__wrapper:hover),
.edit-form :deep(.el-textarea__inner:hover),
.edit-form :deep(.el-select .el-input__wrapper:hover) {
  border-color: #667eea;
}
.edit-form :deep(.el-input__wrapper.is-focus),
.edit-form :deep(.el-textarea__inner:focus),
.edit-form :deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
.edit-form :deep(.el-form-item) {
  margin-bottom: 20px;
}
.edit-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}
.edit-form :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}
@media (max-width: 900px) {
  .kol-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 16px 8px 16px;
  }
  .kol-content-scroll {
    padding: 0 12px 24px;
  }
  .kol-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  .edit-dialog :deep(.el-dialog__header) {
    padding: 16px 20px;
  }
  .edit-form-container {
    padding: 16px;
  }
  .edit-section-card {
    margin-bottom: 14px;
  }
}
@media (max-width: 600px) {
  .kol-header {
    padding: 10px 6px 6px 6px;
  }
  .kol-content-scroll {
    padding: 0 4px 16px;
  }
  .kol-metrics-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  .edit-dialog :deep(.el-dialog__header) {
    padding: 10px 8px;
  }
  .edit-form-container {
    padding: 8px;
  }
}
</style>
