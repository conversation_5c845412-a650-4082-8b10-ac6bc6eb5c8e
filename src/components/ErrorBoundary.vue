<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    <div v-else class="error-fallback">
      <el-result
        icon="warning"
        title="页面加载遇到问题"
        sub-title="后端服务暂时不可用，但您仍可以使用其他功能"
      >
        <template #extra>
          <el-button type="primary" @click="retry">重试</el-button>
          <el-button @click="goHome">返回首页</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const hasError = ref(false)
const router = useRouter()

// 捕获子组件的错误
onErrorCaptured((error: Error) => {
  console.error('ErrorBoundary 捕获到错误:', error)

  // 检查是否是网络相关错误
  if (
    error.message.includes('Network Error') ||
    error.message.includes('ERR_NETWORK') ||
    error.message === 'BACKEND_UNAVAILABLE'
  ) {
    console.warn('网络错误被 ErrorBoundary 捕获，但不显示错误页面')
    // 对于网络错误，不显示错误页面，让组件自己处理
    return false
  }

  // 对于其他严重错误，显示错误页面
  hasError.value = true
  return false // 阻止错误继续向上传播
})

const retry = () => {
  hasError.value = false
  // 刷新当前页面
  window.location.reload()
}

const goHome = () => {
  hasError.value = false
  router.push('/dashboard')
  ElMessage.info('已返回首页')
}

// Define component name for better debugging
defineOptions({
  name: 'ErrorBoundary',
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}
</style>
