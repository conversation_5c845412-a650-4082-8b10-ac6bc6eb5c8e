import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '登录 - KOL Hub',
        requiresGuest: true,
      },
    },
    {
      path: '/login',
      redirect: '/',
    },
    // 登录成功后的主页面
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: {
        title: '控制台',
        requiresAuth: true,
      },
    },
    // KOL 管理相关路由
    {
      path: '/kol/list',
      name: 'kol-list',
      component: () => import('../views/KolListView.vue'),
      meta: {
        title: '红人列表',
        requiresAuth: true,
      },
    },

    {
      path: '/kol/mismatch',
      name: 'kol-mismatch',
      component: () => import('../views/MismatchListView.vue'),
      meta: {
        title: 'Mismatch',
        requiresAuth: true,
      },
    },
    {
      path: '/test/column-control',
      name: 'column-control-test',
      component: () => import('../views/ColumnControlTestView.vue'),
      meta: {
        title: '列控制测试',
        requiresAuth: true,
      },
    },
    // 合作管理相关路由
    {
      path: '/cooperation/projects',
      name: 'cooperation-projects',
      component: () => import('../views/DashboardView.vue'), // 临时使用 Dashboard
      meta: {
        title: '合作项目',
        requiresAuth: true,
      },
    },
    {
      path: '/cooperation/candidates',
      name: 'cooperation-candidates',
      component: () => import('../views/CandidatesListView.vue'),
      meta: {
        title: '候选人管理',
        requiresAuth: true,
      },
    },
    {
      path: '/cooperation/contracts',
      name: 'cooperation-contracts',
      component: () => import('../views/PerformanceListView.vue'),
      meta: {
        title: '合作数据',
        requiresAuth: true,
      },
    },

    // 爬虫任务相关路由
    {
      path: '/crawler/tasks',
      name: 'crawler-tasks',
      component: () => import('../views/CrawlerTaskView.vue'),
      meta: {
        title: '爬虫任务管理',
        requiresAuth: true,
      },
    },

    // 邮件数据相关路由
    {
      path: '/email/send-data',
      name: 'email-send-data',
      component: () => import('../views/EmailSendDataView.vue'),
      meta: {
        title: '邮件发送数据',
        requiresAuth: true,
      },
    },

    // 系统管理相关路由
    {
      path: '/system/config',
      name: 'system-config',
      component: () => import('../views/ConfigManagementView.vue'),
      meta: {
        title: '配置管理',
        requiresAuth: true,
      },
    },
  ],
})

export default router
