# KOL Hub Vue

A modern Vue.js application built with Vue 3, Element Plus UI library, TypeScript, and Vite.

## 🚀 Features

- **Vue 3** - Latest version with Composition API
- **Element Plus** - Comprehensive UI component library
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Auto Import** - Automatic imports for Element Plus components
- **Vue Router** - Client-side routing
- **Pinia** - State management
- **ESLint + Prettier** - Code quality and formatting
- **Vitest** - Unit testing framework

## 📦 Prerequisites

- Node.js 18+ (recommended: Node.js 20+)
- npm or yarn

## 🛠️ Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## 📝 Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The development server will start at `http://localhost:5173/`

## 📋 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Type-check, compile and minify for production
- `npm run preview` - Preview production build
- `npm run test:unit` - Run unit tests with Vitest
- `npm run lint` - Lint and fix files with ESLint
- `npm run format` - Format code with Prettier

## 📁 Project Structure

```
src/
├── assets/          # Static assets
├── components/      # Reusable components
├── router/          # Vue Router configuration
├── stores/          # Pinia stores
├── views/           # Page components
├── App.vue          # Root component
└── main.ts          # Application entry point
```

## 🎨 Element Plus Integration

Element Plus components are automatically imported thanks to the `unplugin-vue-components` configuration. You can use any Element Plus component without manual imports:

```vue
<template>
  <el-button type="primary">Click me</el-button>
  <el-input v-model="input" placeholder="Enter text" />
  <el-card>
    <el-form>
      <el-form-item label="Name">
        <el-input v-model="form.name" />
      </el-form-item>
    </el-form>
  </el-card>
</template>
```

## 🔧 Configuration

### Vite Configuration

The project uses Vite with the following plugins:

- `@vitejs/plugin-vue` - Vue 3 support
- `unplugin-auto-import` - Auto import for Element Plus APIs
- `unplugin-vue-components` - Auto import for Element Plus components
- `vite-plugin-vue-devtools` - Vue DevTools integration

### Auto Import Configuration

Element Plus components and composables are automatically imported. The configuration includes:

- Component auto-import via `unplugin-vue-components`
- API auto-import via `unplugin-auto-import`
- TypeScript support for auto-imported items

## 🎯 Demo Features

The application includes demo pages showcasing:

- Element Plus form components (inputs, buttons, forms)
- Data display components (tables, cards, tags)
- Navigation components (menu, breadcrumbs)
- Feedback components (messages, alerts)
- Layout components (container, grid system)

## 🔗 Useful Links

- [Vue 3 Documentation](https://vuejs.org/)
- [Element Plus Documentation](https://element-plus.org/)
- [Vite Documentation](https://vitejs.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/)
- [Pinia Documentation](https://pinia.vuejs.org/)

## 📄 License

This project is licensed under the MIT License.
